{"__type__": "sp.SkeletonData", "_name": "action3", "_native": "raw-skeleton.json", "_skeletonJson": {"skeleton": {"hash": "qSVoZmAIxEHUPo7EyLcetUZJmdM", "spine": "3.5.49", "width": 0, "height": 0, "images": "./image/"}, "bones": [{"name": "root"}], "slots": [{"name": "J0000", "bone": "root"}], "skins": {"default": {"J0000": {"J0000": {"width": 580, "height": 580}, "J0081": {"width": 580, "height": 580}, "J0082": {"width": 580, "height": 580}, "J0083": {"width": 580, "height": 580}, "J0084": {"width": 580, "height": 580}, "J0085": {"width": 580, "height": 580}, "J0086": {"width": 580, "height": 580}, "J0087": {"width": 580, "height": 580}, "J0088": {"width": 580, "height": 580}, "J0089": {"width": 580, "height": 580}, "J0090": {"width": 580, "height": 580}, "J0091": {"width": 580, "height": 580}, "J0092": {"width": 580, "height": 580}, "J0093": {"width": 580, "height": 580}, "J0094": {"width": 580, "height": 580}, "J0095": {"width": 580, "height": 580}, "J0096": {"width": 580, "height": 580}, "J0097": {"width": 580, "height": 580}, "J0098": {"width": 580, "height": 580}, "J0099": {"width": 580, "height": 580}, "J0100": {"width": 580, "height": 580}, "J0101": {"width": 580, "height": 580}, "J0102": {"width": 580, "height": 580}, "J0103": {"width": 580, "height": 580}, "J0104": {"width": 580, "height": 580}, "J0105": {"width": 580, "height": 580}}}}, "events": {"attack": {}, "back": {}, "hit": {}, "hit_big": {}, "hit_none": {}, "move": {}, "ready": {}, "shake_point": {}}, "animations": {"action1": {"slots": {"J0000": {"color": [{"time": 0, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "J0000"}, {"time": 0.1, "name": "J0081"}, {"time": 0.2333, "name": "J0082"}, {"time": 0.3667, "name": "J0083"}, {"time": 0.4667, "name": "J0084"}]}}, "events": [{"time": 0.0333, "name": "move"}, {"time": 0.3667, "name": "attack"}, {"time": 0.4, "name": "hit"}]}, "action2": {"slots": {"J0000": {"attachment": [{"time": 0, "name": "J0085"}, {"time": 0.1, "name": "J0086"}, {"time": 0.2333, "name": "J0087"}, {"time": 0.3667, "name": "J0088"}]}}, "events": [{"time": 0.1, "name": "attack"}, {"time": 0.1333, "name": "hit"}, {"time": 0.2, "name": "shake_point"}]}, "action3": {"slots": {"J0000": {"attachment": [{"time": 0, "name": "J0089"}, {"time": 0.0667, "name": "J0090"}, {"time": 0.1667, "name": "J0091"}, {"time": 0.3, "name": "J0092"}]}}, "events": [{"time": 0.0667, "name": "attack"}, {"time": 0.1, "name": "hit"}]}, "action4": {"slots": {"J0000": {"attachment": [{"time": 0, "name": "J0093"}, {"time": 0.1, "name": "J0094"}, {"time": 0.2, "name": "J0095"}, {"time": 0.3, "name": "J0096"}, {"time": 0.4333, "name": "J0097"}, {"time": 0.5667, "name": "J0098"}]}}, "events": [{"time": 0.1, "name": "attack"}, {"time": 0.1333, "name": "hit"}]}, "action5": {"slots": {"J0000": {"attachment": [{"time": 0, "name": "J0099"}, {"time": 0.1, "name": "J0100"}, {"time": 0.2333, "name": "J0101"}, {"time": 0.3667, "name": "J0102"}, {"time": 0.5, "name": "J0103"}, {"time": 0.6, "name": "J0104"}, {"time": 0.7, "name": "J0105"}, {"time": 0.8333, "name": "J0000"}]}}, "events": [{"time": 0.1, "name": "attack"}, {"time": 0.1333, "name": "hit"}, {"time": 0.1667, "name": "shake_point"}, {"time": 0.7667, "name": "back"}]}, "action6": {"slots": {"J0000": {"color": [{"time": 0, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "J0000"}, {"time": 0.1, "name": "J0081"}, {"time": 0.2333, "name": "J0082"}, {"time": 0.3667, "name": "J0083"}, {"time": 0.4667, "name": "J0084"}, {"time": 0.6, "name": "J0085"}, {"time": 0.7, "name": "J0086"}, {"time": 0.8333, "name": "J0087"}, {"time": 0.9667, "name": "J0088"}, {"time": 1.1, "name": "J0089"}, {"time": 1.1667, "name": "J0090"}, {"time": 1.2667, "name": "J0091"}, {"time": 1.4, "name": "J0092"}, {"time": 1.5, "name": "J0093"}, {"time": 1.6, "name": "J0094"}, {"time": 1.7, "name": "J0095"}, {"time": 1.8, "name": "J0096"}, {"time": 1.9333, "name": "J0097"}, {"time": 2.0667, "name": "J0098"}, {"time": 2.1667, "name": "J0099"}, {"time": 2.2667, "name": "J0100"}, {"time": 2.4, "name": "J0101"}, {"time": 2.5333, "name": "J0102"}, {"time": 2.6667, "name": "J0103"}, {"time": 2.7667, "name": "J0104"}, {"time": 2.8667, "name": "J0105"}, {"time": 3, "name": "J0000"}]}}, "events": [{"time": 0.0333, "name": "move"}, {"time": 0.4, "name": "hit"}, {"time": 0.7333, "name": "hit"}, {"time": 1.2, "name": "hit"}, {"time": 1.6333, "name": "hit"}, {"time": 2.3, "name": "hit"}, {"time": 2.9333, "name": "back"}]}, "action7": {"slots": {"J0000": {"color": [{"time": 0, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "J0000"}, {"time": 0.1, "name": "J0081"}, {"time": 0.2333, "name": "J0082"}, {"time": 0.3667, "name": "J0083"}, {"time": 0.4667, "name": "J0084"}, {"time": 0.6, "name": "J0085"}, {"time": 0.7, "name": "J0086"}, {"time": 0.8333, "name": "J0087"}, {"time": 0.9667, "name": "J0088"}, {"time": 1.1333, "name": "J0098"}, {"time": 1.2333, "name": "J0099"}, {"time": 1.3333, "name": "J0100"}, {"time": 1.4667, "name": "J0101"}, {"time": 1.6, "name": "J0102"}, {"time": 1.7333, "name": "J0103"}, {"time": 1.8333, "name": "J0104"}, {"time": 1.9333, "name": "J0105"}, {"time": 2.0667, "name": "J0000"}]}}, "events": [{"time": 0.0333, "name": "move"}, {"time": 0.4, "name": "hit"}, {"time": 0.7333, "name": "hit"}, {"time": 1.3667, "name": "hit"}, {"time": 2, "name": "back"}]}}}, "_atlasText": "\naction3.png\nsize: 890,615\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nJ0000\n  rotate: true\n  xy: 599, 319\n  size: 144, 141\n  orig: 580, 580\n  offset: 184, 284\n  index: -1\nJ0081\n  rotate: false\n  xy: 100, 424\n  size: 114, 189\n  orig: 580, 580\n  offset: 211, 283\n  index: -1\nJ0082\n  rotate: false\n  xy: 225, 75\n  size: 141, 167\n  orig: 580, 580\n  offset: 184, 285\n  index: -1\nJ0083\n  rotate: true\n  xy: 392, 450\n  size: 163, 117\n  orig: 580, 580\n  offset: 233, 285\n  index: -1\nJ0084\n  rotate: false\n  xy: 368, 2\n  size: 141, 112\n  orig: 580, 580\n  offset: 184, 285\n  index: -1\nJ0085\n  rotate: true\n  xy: 511, 3\n  size: 111, 126\n  orig: 580, 580\n  offset: 216, 285\n  index: -1\nJ0086\n  rotate: true\n  xy: 216, 437\n  size: 176, 174\n  orig: 580, 580\n  offset: 149, 285\n  index: -1\nJ0087\n  rotate: true\n  xy: 511, 465\n  size: 148, 146\n  orig: 580, 580\n  offset: 177, 285\n  index: -1\nJ0088\n  rotate: true\n  xy: 622, 190\n  size: 127, 117\n  orig: 580, 580\n  offset: 197, 285\n  index: -1\nJ0089\n  rotate: false\n  xy: 523, 149\n  size: 97, 145\n  orig: 580, 580\n  offset: 248, 291\n  index: -1\nJ0090\n  rotate: false\n  xy: 213, 244\n  size: 114, 178\n  orig: 580, 580\n  offset: 239, 333\n  index: -1\nJ0091\n  rotate: true\n  xy: 742, 333\n  size: 136, 138\n  orig: 580, 580\n  offset: 191, 373\n  index: -1\nJ0092\n  rotate: false\n  xy: 659, 471\n  size: 120, 142\n  orig: 580, 580\n  offset: 213, 372\n  index: -1\nJ0093\n  rotate: true\n  xy: 471, 296\n  size: 152, 126\n  orig: 580, 580\n  offset: 206, 365\n  index: -1\nJ0094\n  rotate: true\n  xy: 742, 241\n  size: 90, 138\n  orig: 580, 580\n  offset: 227, 334\n  index: -1\nJ0095\n  rotate: false\n  xy: 368, 116\n  size: 153, 154\n  orig: 580, 580\n  offset: 163, 346\n  index: -1\nJ0096\n  rotate: false\n  xy: 741, 110\n  size: 132, 129\n  orig: 580, 580\n  offset: 248, 350\n  index: -1\nJ0097\n  rotate: true\n  xy: 639, 3\n  size: 105, 119\n  orig: 580, 580\n  offset: 252, 373\n  index: -1\nJ0098\n  rotate: false\n  xy: 2, 38\n  size: 96, 184\n  orig: 580, 580\n  offset: 213, 383\n  index: -1\nJ0099\n  rotate: false\n  xy: 329, 272\n  size: 140, 163\n  orig: 580, 580\n  offset: 189, 290\n  index: -1\nJ0100\n  rotate: true\n  xy: 100, 39\n  size: 183, 123\n  orig: 580, 580\n  offset: 240, 286\n  index: -1\nJ0101\n  rotate: true\n  xy: 2, 224\n  size: 191, 104\n  orig: 580, 580\n  offset: 238, 285\n  index: -1\nJ0102\n  rotate: true\n  xy: 2, 417\n  size: 196, 96\n  orig: 580, 580\n  offset: 231, 284\n  index: -1\nJ0103\n  rotate: true\n  xy: 108, 240\n  size: 182, 103\n  orig: 580, 580\n  offset: 226, 278\n  index: -1\nJ0104\n  rotate: true\n  xy: 781, 471\n  size: 142, 107\n  orig: 580, 580\n  offset: 231, 280\n  index: -1\nJ0105\n  rotate: true\n  xy: 760, 19\n  size: 89, 128\n  orig: 580, 580\n  offset: 242, 281\n  index: -1\n", "textures": [{"__uuid__": "73xcD26p1FXplJpWx4IoCX"}], "textureNames": ["action3.png"]}