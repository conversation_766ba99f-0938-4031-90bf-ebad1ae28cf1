import sys
import re
import os
from PIL import Image

def parse_atlas(atlas_file):
    """
    解析.atlas文件中的部件信息及图像尺寸。
    返回部件字典和图像尺寸。
    """
    with open(atlas_file, 'r') as file:
        content = file.read()

    size_pattern = r"size: (\d+),(\d+)"
    part_pattern = r"(\w+)\n\s+rotate: (false|true)\n\s+xy: (\d+), (\d+)\n\s+size: (\d+), (\d+)\n\s+orig: (\d+), (\d+)\n\s+offset: (\d+), (\d+)\n\s+index: (-?\d+)"

    atlas_size = re.search(size_pattern, content)
    if atlas_size:
        atlas_width, atlas_height = map(int, atlas_size.groups())
    else:
        atlas_width, atlas_height = None, None

    parts = {}
    matches = re.findall(part_pattern, content)
    for part in matches:
        name, _, x, y, width, height, _, _, _, _, _ = part
        parts[name] = {
            'xy': (int(x), int(y)),
            'size': (int(width), int(height))
        }
    return parts, (atlas_width, atlas_height)

def crop_images(image_file, parts, output_dir, expected_size):
    """
    使用解析得到的部件信息来裁剪图像，并保存为单独的文件到指定目录。
    检查图像尺寸与预期尺寸是否一致。
    """
    img = Image.open(image_file)
    if img.size != expected_size:
        print(f"Warning: The image size {img.size} does not match the expected atlas size {expected_size}.")
    
    os.makedirs(output_dir, exist_ok=True)
    for name, attrs in parts.items():
        xy = attrs['xy']
        size = attrs['size']
        cropped_img = img.crop((xy[0], xy[1], xy[0] + size[0], xy[1] + size[1]))
        filename = os.path.join(output_dir, f"{name}.png")
        print(f"Saving {filename}")
        cropped_img.save(filename)

def main():
    if len(sys.argv) < 2:
        print("Usage: python script.py atlas_file [output_dir]")
        sys.exit(1)

    atlas_file = sys.argv[1]
    output_dir = sys.argv[2] if len(sys.argv) > 2 else os.path.splitext(atlas_file)[0] + "_images"

    parts, expected_size = parse_atlas(atlas_file)
    image_file = atlas_file.replace('.atlas', '.png')
    crop_images(image_file, parts, output_dir, expected_size)

if __name__ == "__main__":
    main()
