# -*- coding: utf-8 -*-
# brief 拆分Cocos2d格式的atlas文件
# author <PERSON> (modified for Cocos2d format)
# info 命令行输入格式为 python 文件所在绝对路径 inputfile outputfile
#

import os
import sys,getopt
import os.path
import shutil
from PIL import Image

def lineType(str):
	if len(str) == 0:
		return 1	# 文件结束
	else:
		str = str.replace("\n","")
		str = str.replace(" ","")
		if len(str) == 0:
			return 2	# 空行，另一个图片文件
	return 3

def ReadCocos2dAtlas():
	try:
		global atlas
		global file_last_path
		
		# 读取PNG文件名
		line = atlas.readline()
		line = line.replace("\n","")
		pngName = line

		if not os.path.isfile(file_name+'\\'+pngName):	#图片文件不存在
			print((file_name + '\\' + pngName + ' not exist'))
			return
		
		try:
			big_image = Image.open(file_name+'\\'+pngName)
			
			# 跳过size, filter, scale等行
			while True:
				line = atlas.readline()
				if not line:
					break
				line = line.strip()
				if line.startswith('size:') or line.startswith('filter:') or line.startswith('scale:'):
					continue
				elif line:  # 遇到非空行，可能是子图像名称
					break
			
			# 处理子图像
			while line:
				line = line.strip()
				if not line:  # 空行，继续读取
					line = atlas.readline()
					continue
				
				# 这是子图像名称
				sub_image_name = line
				
				# 读取bounds信息
				bounds_line = atlas.readline().strip()
				if not bounds_line.startswith('bounds:'):
					print(f"Expected bounds for {sub_image_name}, got: {bounds_line}")
					break
				
				# 解析bounds: x,y,width,height
				bounds_parts = bounds_line.split(':')[1].split(',')
				x = int(bounds_parts[0])
				y = int(bounds_parts[1])
				width = int(bounds_parts[2])
				height = int(bounds_parts[3])
				
				# 检查是否有rotate信息
				rotate = False
				next_line = atlas.readline().strip()
				if next_line.startswith('rotate:'):
					rotate = next_line.split(':')[1].strip()
					if rotate == '90':
						rotate = True
					next_line = atlas.readline().strip()
				
				# 裁剪图像
				try:
					region = (x, y, x + width, y + height)
					rect_on_big = big_image.crop(region)
					
					# 如果rotate为真，还原时则需翻转回来
					if rotate:
						try:
							rect_on_big = rect_on_big.transpose(Image.ROTATE_270)
						except Exception as err:
							print(err)
					
					# 保存图像
					try:
						# 创建输出目录
						if aim_path == None:
							path = os.path.join(file_name, os.path.splitext(pngName)[0])
						else:
							last_path = os.path.split(file_name)[1] + '_' + file_last_path
							path = os.path.join(aim_path, last_path)
						
						if not os.path.isdir(path):
							os.makedirs(path)
						
						# 保存文件
						output_name = sub_image_name + ".png"
						output_path = os.path.join(path, output_name)
						print("SaveTo:", output_path)
						rect_on_big.save(output_path)
						
					except Exception as err:
						print(f"Error saving {sub_image_name}: {err}")
						
				except Exception as err:
					print(f"Error cropping {sub_image_name}: {err}")
				
				# 读取下一行
				line = next_line if not next_line.startswith('rotate:') else atlas.readline()
				
			del big_image
			return
			
		except Exception as err:
			print(f"Error processing {pngName}: {err}")
			
	except Exception as err:
		print(f"Error in ReadCocos2dAtlas: {err}")

while True:
	if len(sys.argv) < 2:
		print('请输入正确的格式:比如python *.py inputfile [outputfile]')
		sys.exit()
	fileName = sys.argv[1]
	temp_list = []
	root_list = []
	file_last_list = []	
	if os.path.isdir(fileName):
		for root, dirs, files in os.walk(fileName):
			for file in files:
				full_path = os.path.join(root,file)
				if file.endswith('.atlas'):
					a = file.split('.')
					file_last_list.append(a[0])
					root_list.append(root)
					temp_list.append(full_path)
	else:
		if fileName.endswith('.atlas'):
			a = fileName.split('.')
			file_last_list.append(a[0])
			root_list.append(os.path.dirname(fileName))
			temp_list.append(fileName)
		else:
			print('请输入目录或 .atlas 文件')
			sys.exit(-1)
	break

aim_path = None
if sys.argv.__len__() >= 3:
	aim_path = sys.argv[2]

for i in range(0,len(temp_list)):
	atlas = open(temp_list[i],"r")
	file_name = root_list[i]
	file_last_path = file_last_list[i]
	try:
		ReadCocos2dAtlas()
	except Exception as err:
		print(err)

	atlas.close()

print('byebye:') 