# -*- coding: utf-8 -*-
import os
import sys
import re
from PIL import Image

def detect_atlas_format(atlas_file):
    """检测atlas文件格式"""
    with open(atlas_file, 'r', encoding='utf-8') as file:
        content = file.read()
    
    if re.search(r'xy:\s*\d+,\s*\d+', content) and re.search(r'rotate:\s*(true|false)', content):
        return 'spine'
    elif re.search(r'bounds:\s*\d+,\d+,\d+,\d+', content):
        return 'cocos2d'
    else:
        return 'unknown'

def parse_spine_atlas(atlas_file):
    """解析Spine格式atlas文件"""
    parts = {}
    atlas_size = None
    
    with open(atlas_file, 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        if line.endswith('.png'):
            i += 1
            continue
        
        if line.startswith('size:'):
            size_match = re.search(r'size:\s*(\d+),(\d+)', line)
            if size_match:
                atlas_size = (int(size_match.group(1)), int(size_match.group(2)))
            i += 1
            continue
        
        if line.startswith('format:') or line.startswith('filter:') or line.startswith('repeat:'):
            i += 1
            continue
        
        if line and not line.startswith(' '):
            # 子图像名称
            sub_image_name = line
            i += 1
            
            # 读取属性
            rotate = False
            xy = (0, 0)
            size = (0, 0)
            
            while i < len(lines) and lines[i].startswith('  '):
                attr_line = lines[i].strip()
                
                if attr_line.startswith('rotate:'):
                    rotate = 'true' in attr_line
                elif attr_line.startswith('xy:'):
                    xy_match = re.search(r'xy:\s*(\d+),\s*(\d+)', attr_line)
                    if xy_match:
                        xy = (int(xy_match.group(1)), int(xy_match.group(2)))
                elif attr_line.startswith('size:'):
                    size_match = re.search(r'size:\s*(\d+),\s*(\d+)', attr_line)
                    if size_match:
                        size = (int(size_match.group(1)), int(size_match.group(2)))
                
                i += 1
            
            parts[sub_image_name] = {
                'xy': xy,
                'size': size,
                'rotate': rotate
            }
        else:
            i += 1
    
    return parts, atlas_size

def parse_cocos2d_atlas(atlas_file):
    """解析Cocos2d格式atlas文件"""
    parts = {}
    atlas_size = None
    
    with open(atlas_file, 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        if not line:
            i += 1
            continue
        
        if line.endswith('.png'):
            i += 1
            continue
        
        if line.startswith('size:'):
            size_match = re.search(r'size:(\d+),(\d+)', line)
            if size_match:
                atlas_size = (int(size_match.group(1)), int(size_match.group(2)))
            i += 1
            continue
        
        if line.startswith('filter:') or line.startswith('scale:'):
            i += 1
            continue
        
        # 子图像名称
        sub_image_name = line
        i += 1
        
        # 读取bounds
        if i < len(lines):
            bounds_line = lines[i].strip()
            if bounds_line.startswith('bounds:'):
                bounds_match = re.search(r'bounds:(\d+),(\d+),(\d+),(\d+)', bounds_line)
                if bounds_match:
                    x, y, width, height = map(int, bounds_match.groups())
                    rotate = False
                    i += 1
                    
                    # 检查rotate
                    if i < len(lines):
                        rotate_line = lines[i].strip()
                        if rotate_line.startswith('rotate:'):
                            if '90' in rotate_line:
                                rotate = True
                            i += 1
                    
                    parts[sub_image_name] = {
                        'xy': (x, y),
                        'size': (width, height),
                        'rotate': rotate
                    }
                else:
                    i += 1
            else:
                i += 1
        else:
            i += 1
    
    return parts, atlas_size

def crop_images(image_file, parts, output_dir, format_type):
    """裁剪并保存图像"""
    if not os.path.exists(image_file):
        print(f"Error: {image_file} not found!")
        return
    
    img = Image.open(image_file)
    os.makedirs(output_dir, exist_ok=True)
    
    for name, attrs in parts.items():
        xy = attrs['xy']
        size = attrs['size']
        rotate = attrs.get('rotate', False)
        
        # 裁剪
        region = (xy[0], xy[1], xy[0] + size[0], xy[1] + size[1])
        cropped_img = img.crop(region)
        
        # 旋转处理
        if rotate:
            if format_type == 'spine':
                cropped_img = cropped_img.transpose(Image.ROTATE_270)
            elif format_type == 'cocos2d':
                cropped_img = cropped_img.transpose(Image.ROTATE_270)
        
        # 保存
        output_path = os.path.join(output_dir, f"{name}.png")
        print(f"Saving: {output_path}")
        cropped_img.save(output_path)

def process_atlas(atlas_file, output_dir=None):
    """处理单个atlas文件"""
    print(f"Processing: {atlas_file}")
    
    # 检测格式
    format_type = detect_atlas_format(atlas_file)
    print(f"Format: {format_type}")
    
    if format_type == 'unknown':
        print(f"Unknown format: {atlas_file}")
        return
    
    # 解析
    if format_type == 'spine':
        parts, atlas_size = parse_spine_atlas(atlas_file)
    else:
        parts, atlas_size = parse_cocos2d_atlas(atlas_file)
    
    if not parts:
        print(f"No parts found in {atlas_file}")
        return
    
    # 输出目录
    if output_dir is None:
        atlas_name = os.path.splitext(os.path.basename(atlas_file))[0]
        output_dir = os.path.join(os.path.dirname(atlas_file), f"{atlas_name}_images")
    
    # 图像文件
    image_file = atlas_file.replace('.atlas', '.png')
    
    # 裁剪保存
    crop_images(image_file, parts, output_dir, format_type)
    print(f"Extracted {len(parts)} images to: {output_dir}")

def main():
    if len(sys.argv) < 2:
        print("Usage: python ExtractAtlasEx.py atlas_file [output_dir]")
        print("       python ExtractAtlasEx.py input_directory [output_directory]")
        sys.exit(1)

    input_path = sys.argv[1]
    output_dir = sys.argv[2] if len(sys.argv) > 2 else None

    if os.path.isfile(input_path) and input_path.endswith('.atlas'):
        process_atlas(input_path, output_dir)
    elif os.path.isdir(input_path):
        atlas_files = []
        for root, dirs, files in os.walk(input_path):
            for file in files:
                if file.endswith('.atlas'):
                    atlas_files.append(os.path.join(root, file))
        
        for atlas_file in atlas_files:
            try:
                process_atlas(atlas_file, output_dir)
            except Exception as e:
                print(f"Error processing {atlas_file}: {e}")
    else:
        print("Input must be .atlas file or directory")

if __name__ == "__main__":
    main()