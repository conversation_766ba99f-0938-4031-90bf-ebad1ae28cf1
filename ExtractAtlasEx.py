# -*- coding: utf-8 -*-
import os
import sys
import re
from PIL import Image

def detect_atlas_format(atlas_file):
    """检测atlas文件格式"""
    with open(atlas_file, 'r', encoding='utf-8') as file:
        content = file.read()
    
    if re.search(r'xy:\s*\d+,\s*\d+', content) and re.search(r'rotate:\s*(true|false)', content):
        return 'spine'
    elif re.search(r'bounds:\s*\d+,\d+,\d+,\d+', content):
        return 'cocos2d'
    else:
        return 'unknown'

def parse_spine_atlas(atlas_file):
    """解析Spine格式atlas文件"""
    parts = {}
    atlas_size = None
    global_scale = 1.0

    with open(atlas_file, 'r', encoding='utf-8') as file:
        lines = file.readlines()

    i = 0
    while i < len(lines):
        line = lines[i].strip()

        if line.endswith('.png'):
            i += 1
            continue

        if line.startswith('size:'):
            size_match = re.search(r'size:\s*(\d+),(\d+)', line)
            if size_match:
                atlas_size = (int(size_match.group(1)), int(size_match.group(2)))
            i += 1
            continue

        if line.startswith('format:') or line.startswith('filter:') or line.startswith('repeat:'):
            i += 1
            continue

        if line.startswith('scale:'):
            scale_match = re.search(r'scale:\s*([\d.]+)', line)
            if scale_match:
                global_scale = float(scale_match.group(1))
            i += 1
            continue

        if line and not line.startswith(' '):
            # 子图像名称
            sub_image_name = line
            i += 1

            # 读取属性
            rotate = False
            rotate_angle = 0
            xy = (0, 0)
            size = (0, 0)
            offsets = None

            while i < len(lines) and lines[i].startswith('  '):
                attr_line = lines[i].strip()

                if attr_line.startswith('rotate:'):
                    if 'true' in attr_line:
                        rotate = True
                        rotate_angle = 90  # Spine默认旋转90度
                    else:
                        rotate = False
                        rotate_angle = 0
                elif attr_line.startswith('xy:'):
                    xy_match = re.search(r'xy:\s*(\d+),\s*(\d+)', attr_line)
                    if xy_match:
                        xy = (int(xy_match.group(1)), int(xy_match.group(2)))
                elif attr_line.startswith('size:'):
                    size_match = re.search(r'size:\s*(\d+),\s*(\d+)', attr_line)
                    if size_match:
                        size = (int(size_match.group(1)), int(size_match.group(2)))
                elif attr_line.startswith('offset:'):
                    # Spine格式的偏移量
                    offset_match = re.search(r'offset:\s*(\d+),\s*(\d+)', attr_line)
                    if offset_match:
                        offset_x, offset_y = map(int, offset_match.groups())
                        # 需要原始尺寸信息来构建完整的offsets
                        # 这里先记录偏移量，后续可能需要调整
                        offsets = (offset_x, offset_y, size[0], size[1])

                i += 1

            parts[sub_image_name] = {
                'xy': xy,
                'size': size,
                'rotate': rotate,
                'rotate_angle': rotate_angle,
                'offsets': offsets,
                'scale': global_scale
            }
        else:
            i += 1

    return parts, atlas_size

def parse_cocos2d_atlas(atlas_file):
    """解析Cocos2d格式atlas文件"""
    parts = {}
    atlas_size = None
    global_scale = 1.0

    with open(atlas_file, 'r', encoding='utf-8') as file:
        lines = file.readlines()

    i = 0
    while i < len(lines):
        line = lines[i].strip()

        if not line:
            i += 1
            continue

        if line.endswith('.png'):
            i += 1
            continue

        if line.startswith('size:'):
            size_match = re.search(r'size:(\d+),(\d+)', line)
            if size_match:
                atlas_size = (int(size_match.group(1)), int(size_match.group(2)))
            i += 1
            continue

        if line.startswith('filter:'):
            i += 1
            continue

        if line.startswith('scale:'):
            scale_match = re.search(r'scale:([\d.]+)', line)
            if scale_match:
                global_scale = float(scale_match.group(1))
            i += 1
            continue

        # 子图像名称
        sub_image_name = line
        i += 1

        # 读取bounds
        if i < len(lines):
            bounds_line = lines[i].strip()
            if bounds_line.startswith('bounds:'):
                bounds_match = re.search(r'bounds:(\d+),(\d+),(\d+),(\d+)', bounds_line)
                if bounds_match:
                    x, y, width, height = map(int, bounds_match.groups())
                    rotate_angle = 0
                    offsets = None
                    i += 1

                    # 检查后续行的属性
                    while i < len(lines) and lines[i].strip():
                        attr_line = lines[i].strip()

                        if attr_line.startswith('rotate:'):
                            rotate_match = re.search(r'rotate:(\d+)', attr_line)
                            if rotate_match:
                                rotate_angle = int(rotate_match.group(1))
                            i += 1
                        elif attr_line.startswith('offsets:'):
                            offsets_match = re.search(r'offsets:(\d+),(\d+),(\d+),(\d+)', attr_line)
                            if offsets_match:
                                offsets = tuple(map(int, offsets_match.groups()))
                            i += 1
                        else:
                            # 如果不是已知属性，跳出循环
                            break

                    parts[sub_image_name] = {
                        'xy': (x, y),
                        'size': (width, height),
                        'rotate_angle': rotate_angle,
                        'offsets': offsets,
                        'scale': global_scale
                    }
                else:
                    i += 1
            else:
                i += 1
        else:
            i += 1

    return parts, atlas_size

def crop_images(image_file, parts, output_dir, format_type):
    """裁剪并保存图像"""
    if not os.path.exists(image_file):
        print(f"Error: {image_file} not found!")
        return

    img = Image.open(image_file)
    os.makedirs(output_dir, exist_ok=True)

    for name, attrs in parts.items():
        xy = attrs['xy']
        size = attrs['size']

        # 获取旋转角度（新格式）或旋转标志（旧格式）
        rotate_angle = attrs.get('rotate_angle', 0)
        rotate = attrs.get('rotate', False)
        offsets = attrs.get('offsets', None)
        scale = attrs.get('scale', 1.0)

        # 裁剪
        region = (xy[0], xy[1], xy[0] + size[0], xy[1] + size[1])
        cropped_img = img.crop(region)

        # 旋转处理
        if rotate_angle != 0:
            # 支持多种旋转角度
            if rotate_angle == 90:
                cropped_img = cropped_img.transpose(Image.ROTATE_270)
            elif rotate_angle == 180:
                cropped_img = cropped_img.transpose(Image.ROTATE_180)
            elif rotate_angle == 270:
                cropped_img = cropped_img.transpose(Image.ROTATE_90)
        elif rotate:
            # 兼容旧的布尔值旋转
            if format_type == 'spine':
                cropped_img = cropped_img.transpose(Image.ROTATE_270)
            elif format_type == 'cocos2d':
                cropped_img = cropped_img.transpose(Image.ROTATE_270)

        # 处理偏移量（如果有的话）
        if offsets:
            offset_x, offset_y, orig_width, orig_height = offsets
            # 创建原始尺寸的透明图像
            final_img = Image.new('RGBA', (orig_width, orig_height), (0, 0, 0, 0))
            # 将裁剪的图像粘贴到正确位置
            final_img.paste(cropped_img, (offset_x, offset_y))
            cropped_img = final_img

        # 处理缩放（如果需要的话）
        if scale != 1.0:
            new_width = int(cropped_img.width * scale)
            new_height = int(cropped_img.height * scale)
            cropped_img = cropped_img.resize((new_width, new_height), Image.LANCZOS)

        # 保存
        output_path = os.path.join(output_dir, f"{name}.png")
        print(f"Saving: {output_path} (rotate: {rotate_angle}°, offsets: {offsets}, scale: {scale})")
        cropped_img.save(output_path)

def process_atlas(atlas_file, output_dir=None):
    """处理单个atlas文件"""
    print(f"Processing: {atlas_file}")
    
    # 检测格式
    format_type = detect_atlas_format(atlas_file)
    print(f"Format: {format_type}")
    
    if format_type == 'unknown':
        print(f"Unknown format: {atlas_file}")
        return
    
    # 解析
    if format_type == 'spine':
        parts, atlas_size = parse_spine_atlas(atlas_file)
    else:
        parts, atlas_size = parse_cocos2d_atlas(atlas_file)
    
    if not parts:
        print(f"No parts found in {atlas_file}")
        return
    
    # 输出目录
    if output_dir is None:
        atlas_name = os.path.splitext(os.path.basename(atlas_file))[0]
        output_dir = os.path.join(os.path.dirname(atlas_file), f"{atlas_name}_images")
    
    # 图像文件
    image_file = atlas_file.replace('.atlas', '.png')
    
    # 裁剪保存
    crop_images(image_file, parts, output_dir, format_type)
    print(f"Extracted {len(parts)} images to: {output_dir}")

def main():
    if len(sys.argv) < 2:
        print("Usage: python ExtractAtlasEx.py atlas_file [output_dir]")
        print("       python ExtractAtlasEx.py input_directory [output_directory]")
        sys.exit(1)

    input_path = sys.argv[1]
    output_dir = sys.argv[2] if len(sys.argv) > 2 else None

    if os.path.isfile(input_path) and input_path.endswith('.atlas'):
        process_atlas(input_path, output_dir)
    elif os.path.isdir(input_path):
        atlas_files = []
        for root, dirs, files in os.walk(input_path):
            for file in files:
                if file.endswith('.atlas'):
                    atlas_files.append(os.path.join(root, file))
        
        for atlas_file in atlas_files:
            try:
                process_atlas(atlas_file, output_dir)
            except Exception as e:
                print(f"Error processing {atlas_file}: {e}")
    else:
        print("Input must be .atlas file or directory")

if __name__ == "__main__":
    main()