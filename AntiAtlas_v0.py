# -*- coding: utf-8 -*-
# brief 拆分atlas文件
# author Hari
# info 命令行输入格式为 python 文件所在绝对路径 inputfile outputfile
#

import os
import sys,getopt
import os.path
import shutil
from PIL import Image

def lineType(str):
	if len(str) == 0:
		return 1	# 文件结束
	else:
		str = str.replace("\n","")
		str = str.replace(" ","")
		if len(str) == 0:
			return 2	# 空行，另一个图片文件
	return 3
			

def ReadPng():
	try:
		global atlas
		global file_last_path
		line = atlas.readline()
		line = line.replace("\n","")
		pngName = line

		if not os.path.isfile(file_name+'\\'+pngName):	#图片文件不存在
			pngPath = os.path.join(file_name, file_last_path + '.atlas');
			print((pngPath + ' '+ pngName + ' not exist'))
			while True:
				line1 = atlas.readline() # name
				if lineType(line1) == 1:
					break
				elif lineType(line1) == 2:
					ReadPng()
		else:
			try:
				big_image = Image.open(file_name+'\\'+pngName)
				_line = atlas.readline();	# size
				_line = atlas.readline();	# format
				_line = atlas.readline();	# filter
				_line = atlas.readline();   # repeat

				while True:
					line1 = atlas.readline() # name
					if lineType(line1) == 1:
						break
					elif lineType(line1) == 2:
						ReadPng()
					else:
						line2 = atlas.readline() # rotate
						line3 = atlas.readline() # xy
						line4 = atlas.readline() # size
						line5 = atlas.readline() # orig
						line6 = atlas.readline() # offset
						line7 = atlas.readline() # index

						name = line1.replace("\n","") + ".png";

						if line2.split(":")[-1] == ' false\n':
							rotate = False
						else:
							rotate = True
						#orig
						args = line5.split(":")[1].split(",");
						orig_x = int(args[0])
						orig_y = int(args[1])
						#size
						args = line4.split(":")[1].split(",");
						width = int(args[0])
						height = int(args[1])
						#xy
						args = line3.split(":")[1].split(",");
						ltx = int(args[0])
						lty = int(args[1])
						#offset
						args = line6.split(":")[1].split(",");
						offset_x = int(args[0])
						offset_y = int(args[1])
				
						if rotate ==True:
							region = (ltx,lty,ltx+height,lty+width)
						elif rotate ==False:
							region = (ltx,lty,ltx+width,lty+height)
						try:
							rect_on_big = big_image.crop(region)
							#如果rotate为真，还原时则需翻转回来
							if rotate==True:
								# rect_on_big = rect_on_big.rotate(270)
								try:
									rect_on_big = rect_on_big.transpose(Image.ROTATE_270)
									x,y = rect_on_big.size
								except Exception as err:
									print(err)
							try:
								x,y = rect_on_big.size;
								result_image = Image.new('RGBA', (orig_x,orig_y), (0, 0, 0, 0))
								resultBox = (offset_x,orig_y-offset_y-y)
								#resultBox = (orig_x-offset_x-x,orig_y-offset_y-y) 右下角
								#resultBox = (orig_x-offset_x-x,offset_y) 右上角
								#resultBox = (offset_x,offset_y) 左上角
								try:
									result_image.paste(rect_on_big,resultBox,mask=0)
									# filepath = os.path.join(aim_path, name)
									# print(filepath)
									# path = os.path.dirname(filepath)
									# print(aim_path)
									# print(file_last_path)
									# print(os.path.split(file_name)[1])
									last_path = os.path.split(file_name)[1] + '_' + file_last_path
									# print(last_path)
									path = os.path.join(aim_path,last_path)
									# print(path)
									# name = os.path.basename(filepath)
									if not os.path.isdir(path):
										os.makedirs(path)
									name = name.replace('/','-')			
									# print(name)
									#result_image.save(aim_path+'/'+name)
									try:
										result_image.save(path +'/'+name)
									except Exception as err:
										print(err)
								except Exception as err:
									print(err)
							except Exception as err:
								print(err)
						except Exception as err:
							print(err)
				del big_image
				return
			except Exception as err:
				print(err)
	except Exception as err:
		print(err)
while True:
	if len(sys.argv) != 3:
		print('请输入正确的格式:比如python *.py inputfile outputfile')
		sys.exit()
	fileName = sys.argv[1]
	temp_list = []
	root_list = []
	file_last_list = []
	for root, dirs, files in os.walk(fileName):
		for file in files:
			full_path = os.path.join(root,file)
			# print(full_path)
			if file.find('.atlas') !=-1:
				a = file.split('.')
				file_last_list.append(a[0])
				root_list.append(root)
				temp_list.append(full_path)
	break

# curPath = os.getcwd()# 当前路径
# aim_path = os.path.join(curPath, fileName)
# print(aim_path)
# if os.path.isdir(aim_path):
# 	shutil.rmtree(aim_path,True)#如果有该目录,删除
# os.makedirs(aim_path)
aim_path = sys.argv[2]
for i in range(0,len(temp_list)):
	atlas = open(temp_list[i],"r")
	file_name = root_list[i]
	file_last_path = file_last_list[i]
	try:
		line = atlas.readline()
		if  lineType(line) == 1:   # 文件结束
			print("文件结束")
		elif lineType(line) == 2:
			ReadPng()
	except Exception as err:
		print(err)

	atlas.close()

print('byebye:')



