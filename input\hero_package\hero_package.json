{"skeleton": {"hash": "9AEZUhLryuo", "spine": "4.1.24", "x": -357.32, "y": -83.15, "width": 704.15, "height": 187.52, "images": "./images/", "audio": "/Users/<USER>/Desktop/DDANGDDANG"}, "bones": [{"name": "root"}, {"name": "hero_package_1", "parent": "root", "color": "c54a4aff"}, {"name": "hero1", "parent": "hero_package_1", "x": -251.05, "y": -52.92, "scaleX": 0.7, "scaleY": 0.7, "color": "ffffffff"}, {"name": "hero1_body", "parent": "hero1", "length": 57.14, "rotation": 90, "x": -0.08, "y": 12.8}, {"name": "hero1_head", "parent": "hero1_body", "length": 55.82, "rotation": -0.14, "x": 53.18, "y": 0.15}, {"name": "hero1_shield", "parent": "hero1_body", "x": 33.5, "y": 47.71}, {"name": "hero1_sword", "parent": "hero1_body", "rotation": -60, "x": 7.47, "y": -25.92}, {"name": "hero1_sword_eff", "parent": "hero1"}, {"name": "undead_night", "parent": "root", "x": 210.99, "y": -53.07, "scaleX": -0.7, "scaleY": 0.7}, {"name": "undead_knight_body", "parent": "undead_night"}, {"name": "undead_knight_sword", "parent": "undead_night", "length": 54.88, "rotation": 89.72, "x": -69.85, "y": 34.29}, {"name": "undead_night2", "parent": "root", "x": 280.62, "y": -53.07, "scaleX": -0.7, "scaleY": 0.7}, {"name": "undead_knight_body2", "parent": "undead_night2"}, {"name": "undead_knight_sword2", "parent": "undead_night2", "length": 54.88, "rotation": 89.72, "x": -69.85, "y": 34.29}, {"name": "undead_night3", "parent": "root", "x": 245.58, "y": -20.25, "scaleX": -0.7, "scaleY": 0.7}, {"name": "undead_knight_body3", "parent": "undead_night3"}, {"name": "undead_knight_sword3", "parent": "undead_night3", "length": 54.88, "rotation": 89.72, "x": -69.85, "y": 34.29}, {"name": "hero_package_3", "parent": "root", "color": "a4e41cff"}, {"name": "knight", "parent": "hero_package_3", "x": -39.6, "y": -63.7, "scaleX": 0.7, "scaleY": 0.7}, {"name": "knight_body", "parent": "knight"}, {"name": "knight_sword", "parent": "knight", "length": 54.88, "rotation": 89.72, "x": -69.85, "y": 34.29}, {"name": "knight_regen", "parent": "hero_package_3", "x": -39.6, "y": -63.7, "scaleX": 0.7, "scaleY": 0.7}, {"name": "spiral_eff", "parent": "knight_regen", "scaleY": 0.5}, {"name": "spiral_eff1", "parent": "spiral_eff"}, {"name": "spiral_circle1", "parent": "spiral_eff"}, {"name": "spiral_beam1", "parent": "spiral_eff"}, {"name": "spiral_circle2", "parent": "spiral_eff", "scaleX": 0.9708, "scaleY": 1.0063}, {"name": "spiral_circle3", "parent": "spiral_eff", "scaleX": 0.834, "scaleY": 0.8645}, {"name": "spiral_beam2", "parent": "spiral_eff"}, {"name": "spiral_sparkle1", "parent": "knight_regen"}, {"name": "spiral_sparkle2", "parent": "knight_regen"}, {"name": "spiral_sparkle3", "parent": "knight_regen"}, {"name": "spiral_sparkle4", "parent": "knight_regen"}, {"name": "spiral_sparkle5", "parent": "knight_regen"}, {"name": "spiral_sparkle6", "parent": "knight_regen"}, {"name": "spiral_sparkle7", "parent": "knight_regen"}, {"name": "spiral_sparkle8", "parent": "knight_regen"}, {"name": "spiral_sparkle9", "parent": "knight_regen"}, {"name": "spiral_sparkle10", "parent": "knight_regen"}, {"name": "hero_package_2", "parent": "root", "x": -237.89, "y": -50.08, "color": "db8d21ff"}, {"name": "dwarf_skill", "parent": "hero_package_2", "scaleX": 0.8, "scaleY": 0.8}, {"name": "hero_2_eff", "parent": "dwarf_skill"}, {"name": "hero_2", "parent": "hero_package_2", "scaleX": 0.8, "scaleY": 0.8}, {"name": "hero_2_body", "parent": "hero_2", "length": 34.8, "rotation": 89.34}, {"name": "hero_2_hair1", "parent": "hero_2_body", "length": 17.82, "rotation": -39.86, "x": 104.3, "y": -17.89}, {"name": "hero_2_hair2", "parent": "hero_2_hair1", "length": 16.38, "rotation": 32.02, "x": 17.82}, {"name": "hero_2_hair3", "parent": "hero_2_hair2", "length": 18.54, "rotation": 50.86, "x": 16.38}, {"name": "hero_2_axe_center", "parent": "hero_package_2", "x": 110, "scaleX": 0.8, "scaleY": 0.8}, {"name": "hero_2_axe", "parent": "hero_2_axe_center", "rotation": 27.07}, {"name": "hero_package_4", "parent": "root", "color": "4b7be9ff"}, {"name": "hobbitW", "parent": "hero_package_3", "x": -177.65, "y": -76.75, "scaleX": 0.8, "scaleY": 0.8}, {"name": "hobbitW_body", "parent": "hobbitW"}, {"name": "hobbitW_sword", "parent": "hobbitW", "x": -41.05, "y": 19.73}, {"name": "archer", "parent": "hero_package_3", "x": -324.66, "y": -58.27, "scaleX": 0.7, "scaleY": 0.7}, {"name": "archer_body", "parent": "archer"}, {"name": "archer_bow", "parent": "archer", "x": 75.93, "y": 36.93}, {"name": "archer_arrow", "parent": "archer_bow", "x": -1.63}, {"name": "archer_bow2", "parent": "archer_bow", "length": 12.74, "rotation": 116.1, "x": -0.06, "y": 4.58}, {"name": "archer_bow3", "parent": "archer_bow2", "length": 10.2, "rotation": 15.5, "x": 12.74}, {"name": "archer_bow4", "parent": "archer_bow", "length": 11.09, "rotation": -119.45, "x": -0.14, "y": -4.52}, {"name": "archer_bow5", "parent": "archer_bow4", "length": 9.56, "rotation": -10.6, "x": 11.09}, {"name": "hero_3", "parent": "hero_package_3", "x": -250.81, "y": -50.68, "scaleX": 0.8, "scaleY": 0.8}, {"name": "hero_3_body", "parent": "hero_3", "length": 60.25, "rotation": 89.48, "y": 0.03}, {"name": "hero_3_bow", "parent": "hero_3_body", "rotation": -89.48, "x": 54.74, "y": -158.4, "color": "2cbe05ff"}, {"name": "hero_3_bow_wingF", "parent": "hero_3_bow", "x": 58.68, "y": -10.68, "color": "fcd905ff"}, {"name": "hero_3_bow3", "parent": "hero_3_bow_wingF", "length": 17.83, "rotation": -149.42, "x": -4.4, "y": -4.3}, {"name": "hero_3_bow4", "parent": "hero_3_bow3", "length": 25.29, "rotation": -16.9, "x": 17.83}, {"name": "hero_3_bow_stringF", "parent": "hero_3_bow4", "rotation": -103.61, "x": 29.22, "y": 9.05}, {"name": "hero_3_bow_wingB", "parent": "hero_3_bow", "x": 58.66, "y": 11.2, "color": "fcd905ff"}, {"name": "hero_3_bow6", "parent": "hero_3_bow_wingB", "length": 18.17, "rotation": 147.48, "x": -5.07, "y": 4.86}, {"name": "hero_3_bow7", "parent": "hero_3_bow6", "length": 23.79, "rotation": 11.53, "x": 18.17}, {"name": "hero_3_bow_stringB", "parent": "hero_3_bow7", "rotation": 110.9, "x": 27.95, "y": -1.22}, {"name": "hero_3_string1", "parent": "hero_3_bow", "x": -23.82, "y": 5.97, "color": "2cbe05ff"}, {"name": "hero_3_bow_arrow", "parent": "hero_3_bow", "x": 34.99, "y": 8.33, "color": "813434ff"}, {"name": "hero_3_circle_effect", "parent": "hero_3_body", "rotation": -89.48, "x": -0.03, "scaleY": 0.45}, {"name": "hero_3_circle_eff1", "parent": "hero_3_circle_effect"}, {"name": "hero_3_circle_eff2", "parent": "hero_3_circle_effect"}, {"name": "hero_3_circle_eff3", "parent": "hero_3_circle_effect"}, {"name": "hero_3_bow_idle", "parent": "hero_3", "rotation": 30, "x": -45.54, "y": 90.27, "color": "2cbe05ff"}, {"name": "wind_skill_effect", "parent": "root"}, {"name": "wind_skill", "parent": "wind_skill_effect", "scaleX": 0.8, "scaleY": 0.8, "color": "3fa039ff"}, {"name": "wind_leaf1", "parent": "wind_skill"}, {"name": "wind_leaf2", "parent": "wind_skill"}, {"name": "wind_leaf3", "parent": "wind_skill"}, {"name": "wind_leaf4", "parent": "wind_skill"}, {"name": "wind_leaf5", "parent": "wind_skill"}, {"name": "wind_effect", "parent": "wind_skill_effect"}, {"name": "uniit_h4_effects", "parent": "hero_package_4", "x": -249.98, "y": -51.72, "scaleX": 0.8, "scaleY": 0.8}, {"name": "unit_h4_attack_eff", "parent": "uniit_h4_effects"}, {"name": "unit_h4_attack_hit_eff1", "parent": "uniit_h4_effects", "scaleX": 0.8, "scaleY": 0.8, "color": "36deffff"}, {"name": "unit_h4_skill_ready_eff1", "parent": "unit_h4_attack_hit_eff1", "scaleX": 0.7387, "scaleY": 0.7387}, {"name": "unit_h4_skill_ready_eff", "parent": "uniit_h4_effects"}, {"name": "unit_h4_hurt_eff", "parent": "uniit_h4_effects"}, {"name": "unit_h4_lightning_eff", "parent": "uniit_h4_effects"}, {"name": "unit_h4_attack_hit_eff2", "parent": "uniit_h4_effects", "scaleX": 0.8, "scaleY": 0.8, "color": "36deffff"}, {"name": "unit_h4_skill_ready_eff2", "parent": "unit_h4_attack_hit_eff2", "scaleX": 0.7387, "scaleY": 0.7387}, {"name": "unit_h4_attack_hit_eff3", "parent": "uniit_h4_effects", "scaleX": 0.8, "scaleY": 0.8, "color": "36deffff"}, {"name": "unit_h4_skill_ready_eff3", "parent": "unit_h4_attack_hit_eff3", "scaleX": 0.7387, "scaleY": 0.7387}, {"name": "unit_h4", "parent": "hero_package_4", "x": -249.98, "y": -51.72, "scaleX": 0.8, "scaleY": 0.8}, {"name": "unit_h4_body", "parent": "unit_h4", "length": 48.66, "rotation": 89.05, "y": 2.52}, {"name": "unit_h4_head", "parent": "unit_h4_body", "length": 35.18, "rotation": 0.29, "x": 55.43, "y": -8.4}, {"name": "unit_h4_eye", "parent": "unit_h4_head", "x": 27.14, "y": -10.24}, {"name": "unit_h4_hairB", "parent": "unit_h4_head", "x": 34.35, "y": -20.95}, {"name": "unit_h4_spellbook", "parent": "unit_h4", "x": 34.37, "y": 13.36}, {"name": "unit_h4_spellbook1", "parent": "unit_h4_spellbook", "rotation": 89.05, "x": -1.24, "y": 6.33}, {"name": "unit_h4_spellbook2", "parent": "unit_h4_spellbook", "rotation": 89.05, "x": 12.18, "y": -0.47}, {"name": "unit_h4_spellbook_paper1", "parent": "unit_h4_spellbook2", "x": 39.48, "y": 4.89}, {"name": "unit_h4_spellbookB", "parent": "unit_h4_spellbook2", "x": 24.68, "y": -19.92}, {"name": "unit_h4_spellbookF", "parent": "unit_h4_spellbook2", "x": 24.04, "y": 29.96}, {"name": "unit_h4_spellbook_paper2", "parent": "unit_h4_spellbook2", "x": 39.87, "y": 4.63}, {"name": "unit_h4_stunning", "parent": "unit_h4"}, {"name": "unit_h4_stun1", "parent": "unit_h4_stunning"}, {"name": "unit_h4_stun2", "parent": "unit_h4_stunning"}, {"name": "knight_regen2", "parent": "hero_package_3", "x": -39.6, "y": -63.7, "scaleX": 0.7, "scaleY": 0.7}, {"name": "spiral_eff2", "parent": "knight_regen2", "scaleY": 0.5}, {"name": "spiral_eff3", "parent": "spiral_eff2"}, {"name": "spiral_circle4", "parent": "spiral_eff2"}, {"name": "spiral_beam3", "parent": "spiral_eff2"}, {"name": "spiral_circle5", "parent": "spiral_eff2", "scaleX": 0.9708, "scaleY": 1.0063}, {"name": "spiral_circle6", "parent": "spiral_eff2", "scaleX": 0.834, "scaleY": 0.8645}, {"name": "spiral_beam4", "parent": "spiral_eff2"}, {"name": "spiral_sparkle11", "parent": "knight_regen2"}, {"name": "spiral_sparkle12", "parent": "knight_regen2"}, {"name": "spiral_sparkle13", "parent": "knight_regen2"}, {"name": "spiral_sparkle14", "parent": "knight_regen2"}, {"name": "spiral_sparkle15", "parent": "knight_regen2"}, {"name": "spiral_sparkle16", "parent": "knight_regen2"}, {"name": "spiral_sparkle17", "parent": "knight_regen2"}, {"name": "spiral_sparkle18", "parent": "knight_regen2"}, {"name": "spiral_sparkle19", "parent": "knight_regen2"}, {"name": "spiral_sparkle20", "parent": "knight_regen2"}, {"name": "knight2", "parent": "hero_package_3", "x": -39.6, "y": -63.7, "scaleX": 0.7, "scaleY": 0.7}, {"name": "knight_body2", "parent": "knight2"}, {"name": "knight_sword2", "parent": "knight2", "length": 54.88, "rotation": 89.72, "x": -69.85, "y": 34.29}, {"name": "die", "parent": "root", "x": 241.78, "y": 4.19, "scaleX": 0.4, "scaleY": 0.4}, {"name": "M_die_skull01", "parent": "die", "x": -2.44, "y": -0.29, "color": "ff0000ff"}, {"name": "die_skull01", "parent": "M_die_skull01", "rotation": 0.16, "x": -2.44, "y": -0.3}, {"name": "M_die_skull02", "parent": "die", "x": 2.46, "y": -2.38, "color": "80ff07ff"}, {"name": "die_skull02", "parent": "M_die_skull02", "rotation": 94.33}, {"name": "die_smoke", "parent": "die", "x": -67.94, "y": 40.96}, {"name": "M_die_skull03", "parent": "die", "x": -6.06, "y": 0.77, "color": "fdff06ff"}, {"name": "die_skull03", "parent": "M_die_skull03", "rotation": 0.68}, {"name": "die_smoke2", "parent": "die", "x": -14.88, "y": 122.41, "scaleX": 0.8, "scaleY": 0.8}, {"name": "die_smoke3", "parent": "die", "x": 43.22, "y": 46.93, "scaleX": 1.1, "scaleY": 1.1}, {"name": "die2", "parent": "root", "x": 279.31, "y": -31.61, "scaleX": 0.4, "scaleY": 0.4}, {"name": "M_die_skull04", "parent": "die2", "x": -2.44, "y": -0.29, "color": "ff0000ff"}, {"name": "die_skull04", "parent": "M_die_skull04", "rotation": 0.16, "x": -2.44, "y": -0.3}, {"name": "M_die_skull05", "parent": "die2", "x": 2.46, "y": -2.38, "color": "80ff07ff"}, {"name": "die_skull05", "parent": "M_die_skull05", "rotation": 94.33}, {"name": "die_smoke4", "parent": "die2", "x": -67.94, "y": 40.96}, {"name": "M_die_skull06", "parent": "die2", "x": -6.06, "y": 0.77, "color": "fdff06ff"}, {"name": "die_skull06", "parent": "M_die_skull06", "rotation": 0.68}, {"name": "die_smoke5", "parent": "die2", "x": -14.88, "y": 122.41, "scaleX": 0.8, "scaleY": 0.8}, {"name": "die_smoke6", "parent": "die2", "x": 43.22, "y": 46.93, "scaleX": 1.1, "scaleY": 1.1}, {"name": "die3", "parent": "root", "x": 211.21, "y": -38.66, "scaleX": 0.4, "scaleY": 0.4}, {"name": "M_die_skull07", "parent": "die3", "x": -2.44, "y": -0.29, "color": "ff0000ff"}, {"name": "die_skull07", "parent": "M_die_skull07", "rotation": 0.16, "x": -2.44, "y": -0.3}, {"name": "M_die_skull08", "parent": "die3", "x": 2.46, "y": -2.38, "color": "80ff07ff"}, {"name": "die_skull08", "parent": "M_die_skull08", "rotation": 94.33}, {"name": "die_smoke7", "parent": "die3", "x": -67.94, "y": 40.96}, {"name": "M_die_skull09", "parent": "die3", "x": -6.06, "y": 0.77, "color": "fdff06ff"}, {"name": "die_skull09", "parent": "M_die_skull09", "rotation": 0.68}, {"name": "die_smoke8", "parent": "die3", "x": -14.88, "y": 122.41, "scaleX": 0.8, "scaleY": 0.8}, {"name": "die_smoke9", "parent": "die3", "x": 43.22, "y": 46.93, "scaleX": 1.1, "scaleY": 1.1}], "slots": [{"name": "BG_clip", "bone": "root", "attachment": "BG_clip"}, {"name": "shadow", "bone": "hero1", "attachment": "shadow"}, {"name": "shadow2", "bone": "undead_night", "attachment": "shadow2"}, {"name": "shadow5", "bone": "knight", "attachment": "shadow"}, {"name": "shadow11", "bone": "knight2", "attachment": "shadow"}, {"name": "shadow4", "bone": "undead_night3", "attachment": "shadow2"}, {"name": "shadow3", "bone": "undead_night2", "attachment": "shadow2"}, {"name": "shadow6", "bone": "hero_2", "attachment": "shadow"}, {"name": "wind_eff", "bone": "wind_skill_effect"}, {"name": "wind_effect", "bone": "wind_effect", "blend": "screen"}, {"name": "wind_effect2", "bone": "wind_effect", "blend": "screen"}, {"name": "undead_knight_body3", "bone": "undead_knight_body3", "attachment": "undead_knight_body2"}, {"name": "undead_knight_sword3", "bone": "undead_knight_sword3", "attachment": "undead_knight_sword2"}, {"name": "undead_knight_body", "bone": "undead_knight_body", "attachment": "undead_knight_body2"}, {"name": "undead_knight_body2", "bone": "undead_knight_body2", "attachment": "undead_knight_body2"}, {"name": "undead_knight_sword", "bone": "undead_knight_sword", "attachment": "undead_knight_sword2"}, {"name": "undead_knight_sword2", "bone": "undead_knight_sword2", "attachment": "undead_knight_sword2"}, {"name": "knight_body", "bone": "knight_body", "attachment": "knight_body2"}, {"name": "knight_body2", "bone": "knight_body2", "attachment": "knight_body2"}, {"name": "knight_sword", "bone": "knight_sword", "attachment": "knight_sword2"}, {"name": "knight_sword2", "bone": "knight_sword2", "attachment": "knight_sword2"}, {"name": "spiral_circle1", "bone": "spiral_circle1", "blend": "screen"}, {"name": "spiral_circle4", "bone": "spiral_circle4", "blend": "screen"}, {"name": "spiral_eff1", "bone": "spiral_eff1", "blend": "screen"}, {"name": "spiral_eff2", "bone": "spiral_eff3", "blend": "screen"}, {"name": "spiral_beam2", "bone": "spiral_beam2", "blend": "screen"}, {"name": "spiral_beam4", "bone": "spiral_beam4", "blend": "screen"}, {"name": "spiral_beam1", "bone": "spiral_beam1", "blend": "screen"}, {"name": "spiral_beam3", "bone": "spiral_beam3", "blend": "screen"}, {"name": "spiral_circle3", "bone": "spiral_circle3", "blend": "screen"}, {"name": "spiral_circle6", "bone": "spiral_circle6", "blend": "screen"}, {"name": "spiral_circle2", "bone": "spiral_circle2", "blend": "screen"}, {"name": "spiral_circle5", "bone": "spiral_circle5", "blend": "screen"}, {"name": "spiral_sparkle1", "bone": "spiral_sparkle1", "blend": "screen"}, {"name": "spiral_sparkle11", "bone": "spiral_sparkle11", "blend": "screen"}, {"name": "spiral_sparkle2", "bone": "spiral_sparkle2", "blend": "screen"}, {"name": "spiral_sparkle12", "bone": "spiral_sparkle12", "blend": "screen"}, {"name": "spiral_sparkle3", "bone": "spiral_sparkle3", "blend": "screen"}, {"name": "spiral_sparkle13", "bone": "spiral_sparkle13", "blend": "screen"}, {"name": "spiral_sparkle4", "bone": "spiral_sparkle4", "blend": "screen"}, {"name": "spiral_sparkle14", "bone": "spiral_sparkle14", "blend": "screen"}, {"name": "spiral_sparkle5", "bone": "spiral_sparkle5", "blend": "screen"}, {"name": "spiral_sparkle15", "bone": "spiral_sparkle15", "blend": "screen"}, {"name": "spiral_sparkle6", "bone": "spiral_sparkle6", "blend": "screen"}, {"name": "spiral_sparkle16", "bone": "spiral_sparkle16", "blend": "screen"}, {"name": "spiral_sparkle7", "bone": "spiral_sparkle7", "blend": "screen"}, {"name": "spiral_sparkle17", "bone": "spiral_sparkle17", "blend": "screen"}, {"name": "spiral_sparkle8", "bone": "spiral_sparkle8", "blend": "screen"}, {"name": "spiral_sparkle18", "bone": "spiral_sparkle18", "blend": "screen"}, {"name": "spiral_sparkle9", "bone": "spiral_sparkle9", "blend": "screen"}, {"name": "spiral_sparkle19", "bone": "spiral_sparkle19", "blend": "screen"}, {"name": "spiral_sparkle10", "bone": "spiral_sparkle10", "blend": "screen"}, {"name": "spiral_sparkle20", "bone": "spiral_sparkle20", "blend": "screen"}, {"name": "hero_2_axe", "bone": "hero_2_axe", "attachment": "hero_2_axe"}, {"name": "hero_2_body", "bone": "hero_2_body", "attachment": "hero_2_body"}, {"name": "hero_2_hair", "bone": "hero_2_hair1", "attachment": "hero_2_hair"}, {"name": "hero_2_eff", "bone": "hero_2_eff"}, {"name": "hero_2_attack_eff", "bone": "hero_package_2", "blend": "screen"}, {"name": "hero1_sword_eff", "bone": "hero1_sword_eff", "color": "fff4b9ff", "blend": "screen"}, {"name": "hero1_sword", "bone": "hero1_sword", "attachment": "hero1_sword"}, {"name": "hero1_body", "bone": "hero1_body", "attachment": "hero1_body"}, {"name": "hero1_head", "bone": "hero1_head", "attachment": "hero1_head"}, {"name": "hero1_shield", "bone": "hero1_shield", "attachment": "hero1_shield"}, {"name": "shadow9", "bone": "hobbitW", "attachment": "shadow"}, {"name": "hobbit_W_body", "bone": "hobbitW_body", "attachment": "hobbit_W_body2"}, {"name": "hobbit_W_sword", "bone": "hobbitW_sword", "attachment": "hobbit_W_sword2"}, {"name": "shadow10", "bone": "archer", "attachment": "shadow"}, {"name": "hero_3_circle_eff1", "bone": "hero_3_circle_eff1", "blend": "screen"}, {"name": "hero_3_leaf1", "bone": "wind_leaf1"}, {"name": "hero_3_circle_eff4", "bone": "hero_3_circle_eff1", "blend": "screen"}, {"name": "hero_3_leaf2", "bone": "wind_leaf2"}, {"name": "hero_3_circle_eff2", "bone": "hero_3_circle_eff2", "blend": "screen"}, {"name": "hero_3_leaf5", "bone": "wind_leaf5"}, {"name": "hero_3_circle_eff5", "bone": "hero_3_circle_eff2", "blend": "screen"}, {"name": "hero_3_circle_eff3", "bone": "hero_3_circle_eff3", "blend": "screen"}, {"name": "hero_3_circle_eff6", "bone": "hero_3_circle_eff3", "blend": "screen"}, {"name": "hero_3_leaf3", "bone": "wind_leaf3"}, {"name": "shadow7", "bone": "hero_3", "attachment": "shadow"}, {"name": "hero_3_leaf4", "bone": "wind_leaf4"}, {"name": "hero_3_bow_idle", "bone": "hero_3_bow_idle", "attachment": "hero_3_bow_idle"}, {"name": "hero_3_body", "bone": "hero_3_body", "attachment": "hero_3_body"}, {"name": "hero_3_bow_wingB", "bone": "hero_3_bow_wingB", "attachment": "hero_3_bow_wingB"}, {"name": "hero_3_bow_attack", "bone": "hero_3_bow", "attachment": "hero_3_bow_attack"}, {"name": "hero_3_bow_string2", "bone": "hero_3_bow", "attachment": "hero_3_string2"}, {"name": "hero_3_bow_wingF", "bone": "hero_3_bow_wingF", "attachment": "hero_3_bow_wingF"}, {"name": "hero_3_bow_string1", "bone": "hero_3_string1", "attachment": "hero_3_string1"}, {"name": "hero_3_arrow", "bone": "hero_3_bow_arrow", "attachment": "hero_3_arrow"}, {"name": "hero_3_arrow2", "bone": "hero_3_bow_arrow", "attachment": "hero_3_arrow_eff"}, {"name": "hero_3_arrow3", "bone": "hero_3_bow_arrow", "blend": "screen"}, {"name": "archer_body", "bone": "archer_body", "attachment": "archer_body2"}, {"name": "archer_bow", "bone": "archer_bow", "attachment": "archer_bow2"}, {"name": "archer_arrow", "bone": "archer_arrow", "attachment": "archer_arrow2"}, {"name": "die_smoke", "bone": "die_smoke", "attachment": "die_smoke"}, {"name": "die_smoke4", "bone": "die_smoke4", "attachment": "die_smoke"}, {"name": "die_smoke7", "bone": "die_smoke7", "attachment": "die_smoke"}, {"name": "die_smoke3", "bone": "die_smoke3", "attachment": "die_smoke"}, {"name": "die_smoke6", "bone": "die_smoke6", "attachment": "die_smoke"}, {"name": "die_smoke9", "bone": "die_smoke9", "attachment": "die_smoke"}, {"name": "die_smoke2", "bone": "die_smoke2", "attachment": "die_smoke"}, {"name": "die_smoke5", "bone": "die_smoke5", "attachment": "die_smoke"}, {"name": "die_smoke8", "bone": "die_smoke8", "attachment": "die_smoke"}, {"name": "die_skull02", "bone": "die_skull02", "attachment": "die_skull02"}, {"name": "die_skull05", "bone": "die_skull05", "attachment": "die_skull02"}, {"name": "die_skull08", "bone": "die_skull08", "attachment": "die_skull02"}, {"name": "die_skull03", "bone": "die_skull03", "attachment": "die_skull02"}, {"name": "die_skull06", "bone": "die_skull06", "attachment": "die_skull02"}, {"name": "die_skull09", "bone": "die_skull09", "attachment": "die_skull02"}, {"name": "die_skull01", "bone": "die_skull01", "attachment": "die_skull01"}, {"name": "die_skull04", "bone": "die_skull04", "attachment": "die_skull01"}, {"name": "die_skull07", "bone": "die_skull07", "attachment": "die_skull01"}, {"name": "skull01", "bone": "die", "attachment": "skull01"}, {"name": "skull04", "bone": "die2", "attachment": "skull01"}, {"name": "skull07", "bone": "die3", "attachment": "skull01"}, {"name": "skull02", "bone": "die", "attachment": "skull02"}, {"name": "skull05", "bone": "die2", "attachment": "skull02"}, {"name": "skull08", "bone": "die3", "attachment": "skull02"}, {"name": "skull03", "bone": "die", "attachment": "skull03"}, {"name": "skull06", "bone": "die2", "attachment": "skull03"}, {"name": "skull09", "bone": "die3", "attachment": "skull03"}, {"name": "shadow8", "bone": "unit_h4", "attachment": "shadow"}, {"name": "unit_h4_spellbook", "bone": "unit_h4_spellbook1", "attachment": "unit_h4_spellbook"}, {"name": "unit_h4_spellbookB", "bone": "unit_h4_spellbook2"}, {"name": "unit_h4_spellbook_paper1", "bone": "unit_h4_spellbook2"}, {"name": "unit_h4_spellbook_paper2", "bone": "unit_h4_spellbook2"}, {"name": "unit_h4_spellbookF", "bone": "unit_h4_spellbook2"}, {"name": "unit_h4_spellbook_shine", "bone": "unit_h4_spellbook2"}, {"name": "unit_h4_hairB", "bone": "unit_h4_hairB", "attachment": "unit_h4_hairB"}, {"name": "unit_h4_body", "bone": "unit_h4_body", "attachment": "unit_h4_body"}, {"name": "unit_h4_body_shine", "bone": "unit_h4_body"}, {"name": "unit_h4_head1", "bone": "unit_h4_head", "attachment": "unit_h4_head1"}, {"name": "unit_h4_eye", "bone": "unit_h4_eye"}, {"name": "unit_h4_skill_ready1", "bone": "unit_h4_skill_ready_eff"}, {"name": "unit_h4_stun1", "bone": "unit_h4_stunning"}, {"name": "unit_h4_skill_ready2", "bone": "unit_h4_skill_ready_eff"}, {"name": "unit_h4_stun2", "bone": "unit_h4_stun1"}, {"name": "unit_h4_skill_ready3", "bone": "unit_h4_skill_ready_eff"}, {"name": "unit_h4_stun3", "bone": "unit_h4_stun2"}, {"name": "unit_h4_attack", "bone": "unit_h4_attack_eff"}, {"name": "unit_h4_stunning", "bone": "unit_h4_stunning"}, {"name": "unit_h4_hurt_eff", "bone": "unit_h4_hurt_eff"}, {"name": "unit_h4_attack_c", "bone": "unit_h4_attack_hit_eff1"}, {"name": "unit_h4_attack_c2", "bone": "unit_h4_attack_hit_eff2"}, {"name": "unit_h4_attack_c3", "bone": "unit_h4_attack_hit_eff3"}, {"name": "unit_h4_skill_ready_eff1", "bone": "unit_h4_skill_ready_eff1"}, {"name": "unit_h4_skill_ready_eff2", "bone": "unit_h4_skill_ready_eff2"}, {"name": "unit_h4_skill_ready_eff3", "bone": "unit_h4_skill_ready_eff3"}, {"name": "unit_h4_attack_ch1", "bone": "unit_h4_attack_hit_eff1"}, {"name": "unit_h4_attack_ch2", "bone": "unit_h4_attack_hit_eff2"}, {"name": "unit_h4_attack_ch3", "bone": "unit_h4_attack_hit_eff3"}, {"name": "unit_h4_lightning", "bone": "unit_h4_lightning_eff"}], "path": [{"name": "skull01", "order": 7, "bones": ["M_die_skull02"], "target": "skull01"}, {"name": "skull02", "order": 8, "bones": ["M_die_skull03"], "target": "skull02"}, {"name": "skull03", "order": 6, "bones": ["M_die_skull01"], "target": "skull03"}, {"name": "skull04", "bones": ["M_die_skull04"], "target": "skull04", "rotation": -78.48}, {"name": "skull05", "order": 1, "bones": ["M_die_skull05"], "target": "skull05"}, {"name": "skull06", "order": 2, "bones": ["M_die_skull06"], "target": "skull06"}, {"name": "skull07", "order": 3, "bones": ["M_die_skull07"], "target": "skull07", "rotation": -81.39}, {"name": "skull08", "order": 4, "bones": ["M_die_skull08"], "target": "skull08"}, {"name": "skull09", "order": 5, "bones": ["M_die_skull09"], "target": "skull09"}], "skins": [{"name": "default", "attachments": {"archer_arrow": {"archer_arrow2": {"type": "mesh", "path": "archer_arrow", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [38, -16.46, -32, -16.46, -32, 15.54, 38, 15.54], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 71, "height": 33}}, "archer_body": {"archer_body2": {"path": "archer_body", "x": 3.04, "y": 50.59, "width": 92, "height": 103}}, "archer_bow": {"archer_bow2": {"type": "mesh", "path": "archer_bow", "uvs": [0, 0, 0, 0.18525, 0.21824, 0.26547, 0.36617, 0.34686, 0.42845, 0.44359, 0.43105, 0.53678, 0.38433, 0.61936, 0.22862, 0.71962, 0, 0.81164, 0, 1, 0.56859, 1, 0.56859, 0.83397, 0.72949, 0.77617, 0.8852, 0.69832, 0.98123, 0.59923, 1, 0.49306, 0.96306, 0.37628, 0.87223, 0.27601, 0.73468, 0.21113, 0.56081, 0.15333, 0.55302, 0], "triangles": [11, 7, 6, 12, 11, 6, 11, 9, 8, 11, 8, 7, 9, 11, 10, 14, 5, 15, 13, 5, 14, 6, 5, 13, 12, 6, 13, 19, 1, 0, 19, 0, 20, 2, 1, 19, 3, 2, 19, 3, 19, 18, 17, 4, 3, 17, 3, 18, 4, 17, 16, 4, 16, 15, 5, 4, 15], "vertices": [1, 58, 22.93, 0.27, 1, 1, 58, 13.79, 8.38, 1, 2, 57, 16.15, 8.21, 0.09131, 58, 5.48, 7, 0.90869, 4, 55, -10.1, 10.11, 0.02746, 57, 9.38, 6.59, 0.75873, 58, -1.48, 7.25, 0.21378, 59, -7.84, -15.87, 3e-05, 4, 55, -8.23, 3.72, 0.27954, 57, 2.82, 7.72, 0.64369, 58, -7.5, 10.09, 0.00118, 59, -3.2, -11.11, 0.07559, 4, 55, -8.15, -2.43, 0.32131, 57, -2.74, 10.36, 0.1233, 59, 2.12, -8.01, 0.54938, 60, -7.34, -9.53, 0.00601, 4, 55, -9.56, -7.88, 0.05698, 57, -7.01, 14.01, 0.00312, 59, 7.55, -6.56, 0.7344, 60, -2.27, -7.09, 0.2055, 2, 59, 15.61, -7.37, 0.04155, 60, 5.8, -6.41, 0.95845, 1, 60, 14.86, -7.76, 1, 1, 60, 24.38, 0.24, 1, 1, 60, 13.41, 13.3, 1, 2, 59, 17.17, 5.22, 0.04144, 60, 5.02, 6.25, 0.95856, 2, 59, 11.47, 7.55, 0.59779, 60, -1.01, 7.49, 0.40221, 3, 55, 5.47, -13.09, 0.00506, 59, 4.7, 9.09, 0.974, 60, -7.95, 7.76, 0.02094, 3, 55, 8.35, -6.55, 0.18886, 57, -13.7, -2.65, 0.0146, 59, -2.41, 8.39, 0.79653, 3, 55, 8.91, 0.46, 0.45367, 57, -7.65, -6.24, 0.29986, 59, -8.79, 5.43, 0.24647, 3, 55, 7.81, 8.17, 0.11723, 57, -0.24, -8.64, 0.88051, 59, -14.96, 0.68, 0.00225, 3, 55, 5.08, 14.78, 0.00058, 57, 6.9, -9.1, 0.99353, 58, -8.06, -7.21, 0.00588, 2, 57, 12.56, -7.28, 0.74227, 58, -2.12, -6.96, 0.25773, 2, 57, 18.28, -4.27, 0.04199, 58, 4.19, -5.6, 0.95801, 1, 58, 11.92, -12.14, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 16, 18, 18, 20], "width": 30, "height": 66}}, "BG_clip": {"BG_clip": {"type": "clipping", "end": "BG_clip", "vertexCount": 4, "vertices": [375.01, -95.03, 374.97, 185.02, -375.04, 184.97, -375.01, -95.02], "color": "ce3a3aff"}}, "die_skull01": {"die_skull01": {"x": 2.44, "y": 0.29, "rotation": -0.16, "width": 150, "height": 150}}, "die_skull02": {"die_skull02": {"x": 2.56, "y": 2.27, "rotation": -94.33, "width": 150, "height": 150}}, "die_skull03": {"die_skull02": {"x": 6.05, "y": -0.84, "rotation": -0.68, "width": 150, "height": 150}}, "die_skull04": {"die_skull01": {"x": 2.44, "y": 0.29, "rotation": -0.16, "width": 150, "height": 150}}, "die_skull05": {"die_skull02": {"x": 2.56, "y": 2.27, "rotation": -94.33, "width": 150, "height": 150}}, "die_skull06": {"die_skull02": {"x": 6.05, "y": -0.84, "rotation": -0.68, "width": 150, "height": 150}}, "die_skull07": {"die_skull01": {"x": 2.44, "y": 0.29, "rotation": -0.16, "width": 150, "height": 150}}, "die_skull08": {"die_skull02": {"x": 2.56, "y": 2.27, "rotation": -94.33, "width": 150, "height": 150}}, "die_skull09": {"die_skull02": {"x": 6.05, "y": -0.84, "rotation": -0.68, "width": 150, "height": 150}}, "die_smoke": {"die_smoke": {"width": 150, "height": 150}}, "die_smoke2": {"die_smoke": {"width": 150, "height": 150}}, "die_smoke3": {"die_smoke": {"width": 150, "height": 150}}, "die_smoke4": {"die_smoke": {"width": 150, "height": 150}}, "die_smoke5": {"die_smoke": {"width": 150, "height": 150}}, "die_smoke6": {"die_smoke": {"width": 150, "height": 150}}, "die_smoke7": {"die_smoke": {"width": 150, "height": 150}}, "die_smoke8": {"die_smoke": {"width": 150, "height": 150}}, "die_smoke9": {"die_smoke": {"width": 150, "height": 150}}, "hero1_body": {"hero1_body": {"x": 23.12, "y": -0.09, "rotation": -90, "width": 79, "height": 74}}, "hero1_head": {"hero1_head": {"x": 75.73, "y": -0.57, "rotation": -89.86, "width": 120, "height": 166}}, "hero1_shield": {"hero1_shield": {"x": -9, "y": -1.62, "rotation": -90, "width": 79, "height": 90}}, "hero1_sword": {"hero1_sword": {"x": 66.02, "y": -0.59, "rotation": -90, "width": 71, "height": 173}}, "hero1_sword_eff": {"hero1_sword_eff": {"type": "mesh", "uvs": [1, 0.94381, 1, 1, 0.39979, 1, 0, 0.21409, 0, 0, 1, 0], "triangles": [3, 4, 5, 2, 3, 5, 0, 2, 5, 2, 0, 1], "vertices": [41.23, 6.55, 41.23, -0.08, -51.8, -0.08, -113.77, 92.65, -113.77, 117.92, 41.23, 117.92], "hull": 6, "edges": [8, 10, 2, 0, 0, 10, 2, 4, 6, 8, 4, 6], "width": 155, "height": 118}}, "hero_2_attack_eff": {"hero_2_attack_eff": {"type": "mesh", "uvs": [0.45566, 0, 0.84136, 0.23259, 1, 0.57781, 1, 1, 0.50663, 1, 0, 0.05215, 0.04537, 0], "triangles": [4, 0, 1, 4, 1, 2, 5, 6, 0, 4, 5, 0, 4, 2, 3], "vertices": [111.92, 206.49, 172.09, 169.04, 196.83, 113.46, 196.83, 45.49, 119.87, 45.49, 40.83, 198.09, 47.91, 206.49], "hull": 7, "edges": [0, 2, 2, 4, 4, 6, 8, 10, 10, 12, 6, 8, 12, 0], "width": 156, "height": 161}}, "hero_2_axe": {"hero_2_axe": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [60.15, -8.66, -42.26, 43.67, 25.09, 175.46, 127.49, 123.13], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 115, "height": 148}}, "hero_2_body": {"hero_2_body": {"type": "mesh", "uvs": [1, 0.66851, 0.99999, 0.84113, 1, 1, 0, 1, 1e-05, 0.84226, 0, 0.66999, 0, 0, 1, 0], "triangles": [6, 7, 0, 5, 6, 0, 1, 5, 0, 4, 5, 1, 4, 1, 2, 3, 4, 2], "vertices": [52.89, -48.22, 25.28, -48.54, -0.14, -48.83, -1.56, 74.16, 23.67, 74.45, 51.24, 74.77, 158.43, 76.01, 159.85, -46.99], "hull": 8, "edges": [4, 6, 12, 14, 10, 12, 0, 14, 10, 0, 6, 8, 8, 10, 0, 2, 2, 4, 8, 2], "width": 123, "height": 160}}, "hero_2_eff": {"hero_2_eff": {"type": "mesh", "uvs": [0.52274, 0, 0.75153, 0.05412, 0.98216, 0.29636, 0.98807, 0.6453, 0.81953, 0.90926, 0.37043, 0.96488, 0.15866, 0.80398, 0.44975, 0.8842, 0.63168, 0.83413, 0.72074, 0.6379, 0.66694, 0.43806, 0.46973, 0.28916, 0.45004, 1e-05], "triangles": [11, 12, 0, 11, 0, 1, 10, 11, 1, 10, 1, 2, 3, 9, 10, 3, 10, 2, 4, 9, 3, 8, 9, 4, 5, 6, 7, 5, 7, 8, 5, 8, 4], "vertices": [5.66, 124.51, 62.63, 111.02, 120.06, 50.71, 121.53, -36.18, 79.56, -101.9, -32.26, -115.76, -84.99, -75.69, -12.51, -95.67, 32.79, -83.2, 54.97, -34.34, 41.57, 15.42, -7.54, 52.5, -12.44, 124.5], "hull": 13, "edges": [0, 24, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24], "width": 249, "height": 249}}, "hero_2_hair": {"hero_2_hair": {"type": "mesh", "uvs": [0.20515, 1, 0.25382, 0.84456, 0.42687, 0.71044, 0.47084, 0.51813, 0.42419, 0.33545, 0.2361, 0.21696, 0, 0.17604, 0, 1e-05, 0.19662, 0, 0.42482, 0, 0.63901, 0, 0.85634, 0.10012, 1, 0.23946, 1, 0.39221, 1, 0.60155, 0.97311, 0.74523, 0.84078, 0.87355, 0.64805, 0.96229, 0.49284, 1], "triangles": [5, 8, 9, 6, 7, 8, 5, 6, 8, 4, 5, 9, 11, 12, 4, 10, 4, 9, 11, 4, 10, 12, 13, 4, 3, 4, 13, 14, 3, 13, 14, 15, 3, 15, 2, 3, 16, 2, 15, 17, 2, 16, 18, 1, 2, 18, 0, 1, 2, 17, 18], "vertices": [1, 44, -7.97, 0.68, 1, 1, 44, 0.09, 4.88, 1, 3, 44, 10.63, 4.33, 0.91203, 45, -3.81, 7.48, 0.08354, 46, -6.94, 20.38, 0.00443, 3, 44, 20.16, 10.04, 0.2529, 45, 7.31, 7.28, 0.58015, 46, -0.08, 11.63, 0.16695, 3, 44, 26.8, 18.3, 0.0082, 45, 17.31, 10.76, 0.17265, 46, 8.93, 6.06, 0.81916, 2, 45, 22.83, 19.57, 0.00032, 46, 19.25, 7.35, 0.99968, 1, 46, 27.65, 13.11, 1, 1, 46, 35.07, 6.35, 1, 1, 46, 29.5, 0.25, 1, 1, 46, 23.05, -6.84, 1, 1, 46, 16.99, -13.48, 1, 2, 45, 33.26, -5.21, 0.00058, 46, 6.62, -16.39, 0.99942, 2, 45, 26.3, -12.35, 0.1752, 46, -3.32, -15.49, 0.8248, 2, 45, 17.69, -13.64, 0.7182, 46, -9.75, -9.63, 0.2818, 3, 44, 30.98, -9.94, 0.01512, 45, 5.89, -15.4, 0.9778, 46, -18.57, -1.59, 0.00708, 2, 44, 24.02, -14.4, 0.44497, 45, -2.38, -15.5, 0.55503, 2, 44, 14.85, -14.93, 0.97793, 45, -10.43, -11.08, 0.02207, 1, 44, 5.75, -12.06, 1, 1, 44, -0.12, -8.51, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36], "width": 42, "height": 57}}, "hero_3_arrow": {"hero_3_arrow": {"type": "mesh", "uvs": [1, 0.80614, 0.70957, 0.80614, 0.61383, 0.66075, 0.26513, 0.66075, 0.15181, 1, 0, 1, 0, 0, 0.1621, 0, 0.26604, 0.25113, 0.61383, 0.25113, 0.70957, 0.11896, 1, 0.12777], "triangles": [2, 9, 10, 3, 7, 8, 2, 3, 8, 2, 8, 9, 1, 2, 10, 1, 10, 11, 1, 11, 0, 4, 5, 6, 7, 4, 6, 3, 4, 7], "vertices": [68.21, -11.1, 28.42, -11.1, 15.31, -6.45, -24.09, -6.45, -39.62, -17.3, -60.41, -17.3, -60.41, 14.7, -38.21, 14.7, -23.97, 6.66, 15.31, 6.66, 28.42, 10.89, 68.21, 10.61], "hull": 12, "edges": [10, 12, 0, 22, 12, 14, 14, 16, 8, 10, 6, 8, 16, 18, 4, 6, 18, 20, 20, 22, 0, 2, 2, 4, 18, 4, 16, 6], "width": 137, "height": 32}}, "hero_3_arrow2": {"hero_3_arrow_eff": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97.55, -25.68, -68.45, -25.68, -68.45, 24.32, 97.55, 24.32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 50}}, "hero_3_arrow3": {"hero_3_arrow_eff1": {"x": 14.55, "y": -0.68, "width": 173, "height": 58}}, "hero_3_body": {"hero_3_body": {"type": "mesh", "uvs": [1, 0.71211, 1, 1, 0, 1, 0, 0.71099, 0, 0, 1, 0], "triangles": [3, 4, 5, 3, 5, 0, 2, 3, 0, 2, 0, 1], "vertices": [51.17, -61.66, -0.65, -62.13, -1.8, 62.86, 50.22, 63.34, 178.2, 64.51, 179.34, -60.48], "hull": 6, "edges": [2, 4, 8, 10, 4, 6, 6, 8, 2, 0, 0, 10, 6, 0], "width": 125, "height": 180}}, "hero_3_bow_attack": {"hero_3_bow_attack": {"x": -6.14, "y": -0.69, "width": 175, "height": 48}}, "hero_3_bow_idle": {"hero_3_bow_idle": {"type": "mesh", "uvs": [1, 0.35122, 1, 0.48453, 0.6636, 0.70714, 0.66458, 0.77541, 0.66783, 1, 0.34063, 1, 0.3439, 0.7748, 0.34486, 0.70839, 0, 0.48956, 0, 0.35374, 0.445, 0, 0.56487, 0], "triangles": [2, 11, 0, 7, 8, 9, 1, 2, 0, 10, 7, 9, 10, 11, 2, 7, 10, 2, 7, 2, 3, 6, 7, 3, 5, 6, 3, 4, 5, 3], "vertices": [86.23, 25.39, 86.23, -0.47, 28.03, -43.66, 28.2, -56.9, 29.98, -90.24, -26.62, -90.24, -27.28, -56.78, -27.11, -43.9, -86.77, -1.45, -86.77, 24.9, -9.79, 93.53, 10.95, 93.53], "hull": 12, "edges": [20, 18, 20, 22, 22, 0, 16, 18, 16, 14, 8, 10, 0, 2, 4, 2, 10, 12, 12, 14, 4, 6, 6, 8, 12, 6], "width": 173, "height": 194}}, "hero_3_bow_string1": {"hero_3_string1": {"x": -0.07, "y": 1.83, "width": 11, "height": 17}}, "hero_3_bow_string2": {"hero_3_string2": {"type": "mesh", "uvs": [1, 0.34736, 1, 0.4693, 1, 1, 0, 1, 0, 0.4693, 0, 0.34736, 0, 0, 0.49085, 0, 1, 0], "triangles": [5, 7, 0, 4, 5, 0, 4, 0, 1, 3, 4, 1, 2, 3, 1, 5, 6, 7, 7, 8, 0], "vertices": [1, 72, 3.44, 4.6, 1, 1, 72, 3.36, -4.92, 1, 1, 67, -0.19, -2.98, 1, 1, 67, -0.54, 1.51, 1, 1, 72, -1.49, -5.28, 1, 1, 72, -1.49, 5.48, 1, 1, 71, -0.47, -1.45, 1, 1, 71, -0.47, -0.91, 1, 1, 71, 2.25, 0.8, 1], "hull": 9, "edges": [4, 6, 10, 12, 0, 16, 10, 0, 6, 8, 8, 10, 0, 2, 2, 4, 8, 2, 12, 14, 14, 16], "width": 3, "height": 72}}, "hero_3_bow_wingB": {"hero_3_bow_wingB": {"type": "mesh", "uvs": [0.99219, 0.74921, 0.85872, 0.95534, 0.68354, 0.95534, 0.58894, 0.89167, 0.51564, 0.7685, 0.3812, 0.73095, 0.23632, 0.69195, 0.1066, 0.59597, 0.00938, 0.45289, 0.02894, 0.02228, 0.1408, 0.01209, 0.25851, 0.14755, 0.36067, 0.24166, 0.45883, 0.31166, 0.53003, 0.174, 0.64536, 0.27287, 0.78049, 0.36146, 0.89277, 0.47309, 0.9886, 0.6363], "triangles": [8, 10, 11, 10, 8, 9, 7, 11, 12, 7, 8, 11, 6, 7, 12, 5, 6, 12, 13, 5, 12, 15, 4, 13, 15, 13, 14, 4, 15, 16, 5, 13, 4, 3, 4, 16, 2, 16, 17, 2, 3, 16, 0, 17, 18, 0, 1, 17, 1, 2, 17], "vertices": [1, 68, 13.03, 0.39, 1, 1, 68, 4.49, -8.47, 1, 1, 68, -6.72, -8.47, 1, 1, 68, -12.78, -5.73, 1, 3, 68, -17.47, -0.43, 0.04153, 69, 7.6, 11.13, 0.78499, 70, -8.12, 13.02, 0.17347, 3, 68, -26.07, 1.18, 0.00018, 69, 15.73, 14.4, 0.39916, 70, 0.49, 14.59, 0.60066, 2, 69, 24.45, 17.97, 0.07853, 70, 9.74, 16.35, 0.92147, 2, 69, 33.67, 18.95, 0.00854, 70, 18.97, 15.47, 0.99146, 1, 70, 26.99, 11.95, 1, 1, 70, 32.45, -5.78, 1, 1, 70, 25.92, -8.76, 1, 1, 70, 16.8, -6.02, 1, 2, 69, 28.15, -2.64, 0.07282, 70, 9.25, -4.58, 0.92718, 2, 69, 21.23, -3.48, 0.32247, 70, 2.31, -4.02, 0.67753, 2, 69, 20.57, -10.92, 0.56047, 70, 0.17, -11.18, 0.43953, 3, 68, -9.17, 20.88, 0.01447, 69, 12.06, -11.3, 0.96824, 70, -8.24, -9.85, 0.01728, 3, 68, -0.52, 17.07, 0.13194, 69, 2.72, -12.74, 0.85958, 70, -17.68, -9.39, 0.00848, 2, 68, 6.67, 12.27, 0.24504, 69, -5.92, -12.55, 0.75496, 2, 68, 12.8, 5.25, 0.86316, 69, -14.86, -9.93, 0.13684], "hull": 19, "edges": [34, 36, 18, 20, 2, 4, 2, 0, 0, 36, 8, 10, 10, 12, 12, 14, 14, 16, 4, 6, 6, 8, 28, 30, 20, 22, 22, 24, 16, 18, 24, 26, 26, 28, 30, 32, 32, 34], "width": 64, "height": 43}}, "hero_3_bow_wingF": {"hero_3_bow_wingF": {"type": "mesh", "uvs": [0.99023, 0.25593, 0.90736, 0.41984, 0.82015, 0.54823, 0.68071, 0.68989, 0.51037, 0.79296, 0.44563, 0.65748, 0.36048, 0.72695, 0.25263, 0.84616, 0.16043, 1, 0.02189, 0.97931, 0.00635, 0.47795, 0.12702, 0.26238, 0.28593, 0.15432, 0.46153, 0.12027, 0.64564, 0.01641, 0.88196, 0.01846, 1, 0.10498], "triangles": [5, 12, 13, 6, 12, 5, 11, 12, 6, 7, 10, 11, 4, 5, 3, 6, 7, 11, 9, 10, 7, 8, 9, 7, 3, 13, 14, 2, 3, 14, 5, 13, 3, 0, 15, 16, 1, 15, 0, 1, 2, 14, 1, 14, 15], "vertices": [1, 64, 12.05, -6.79, 1, 2, 64, 6.83, -13.18, 0.47949, 65, -5.15, 13.36, 0.52051, 3, 64, 1.33, -18.19, 0.11434, 65, 2.13, 14.87, 0.88514, 66, -19.35, 9.67, 0.00052, 3, 64, -7.45, -23.71, 0.00279, 65, 12.5, 15.16, 0.90584, 66, -9.51, 12.96, 0.09137, 2, 65, 23.79, 13.16, 0.35789, 66, 1.87, 14.32, 0.64211, 2, 65, 24.61, 6.54, 0.1916, 66, 4.58, 8.23, 0.8084, 2, 65, 30.61, 6.14, 0.03656, 66, 10.44, 9.59, 0.96344, 1, 66, 18.14, 12.5, 1, 1, 66, 25.2, 16.96, 1, 1, 66, 33.49, 14.11, 1, 1, 66, 29.82, -5.12, 1, 1, 66, 20.44, -11.49, 1, 2, 65, 23.29, -15.47, 0.0359, 66, 9.72, -13.22, 0.9641, 2, 65, 13.09, -10.99, 0.59985, 66, -1.34, -11.89, 0.40015, 1, 64, -9.66, 2.55, 1, 1, 64, 5.23, 2.47, 1, 1, 64, 12.66, -0.9, 1], "hull": 17, "edges": [0, 32, 30, 32, 28, 30, 26, 28, 20, 22, 16, 18, 14, 16, 0, 2, 2, 4, 4, 6, 6, 8, 18, 20, 12, 14, 22, 24, 24, 26, 8, 10, 10, 12], "width": 63, "height": 39}}, "hero_3_circle_eff1": {"hero_3_circle_eff": {"width": 157, "height": 173}}, "hero_3_circle_eff2": {"hero_3_circle_eff": {"width": 157, "height": 173}}, "hero_3_circle_eff3": {"hero_3_circle_eff": {"width": 157, "height": 173}}, "hero_3_circle_eff4": {"hero_3_circle_eff": {"width": 157, "height": 173}}, "hero_3_circle_eff5": {"hero_3_circle_eff": {"width": 157, "height": 173}}, "hero_3_circle_eff6": {"hero_3_circle_eff": {"width": 157, "height": 173}}, "hero_3_leaf1": {"hero_3_leaf1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-25.5, -14, -25.5, 14, 25.5, 14, 25.5, -14], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 28, "height": 51}}, "hero_3_leaf2": {"hero_3_leaf2": {"rotation": -90, "width": 28, "height": 51}}, "hero_3_leaf3": {"hero_3_leaf3": {"rotation": -90, "width": 28, "height": 51}}, "hero_3_leaf4": {"hero_3_leaf3": {"rotation": -90, "width": 28, "height": 51}}, "hero_3_leaf5": {"hero_3_leaf2": {"rotation": -90, "width": 28, "height": 51}}, "hobbit_W_body": {"hobbit_W_body2": {"path": "hobbit_W_body", "x": -0.84, "y": 36.08, "width": 60, "height": 74}}, "hobbit_W_sword": {"hobbit_W_sword2": {"path": "hobbit_W_sword", "x": 0.57, "y": 15.09, "rotation": 90, "width": 50, "height": 25}}, "knight_body": {"knight_body2": {"path": "knight_body", "x": -2.15, "y": 56.04, "width": 96, "height": 114}}, "knight_body2": {"knight_body2": {"path": "knight_body", "x": -2.15, "y": 56.04, "width": 96, "height": 114}}, "knight_sword": {"knight_sword2": {"path": "knight_sword", "x": 29.46, "y": 0.4, "rotation": 0.28, "width": 101, "height": 47}}, "knight_sword2": {"knight_sword2": {"path": "knight_sword", "x": 29.46, "y": 0.4, "rotation": 0.28, "width": 101, "height": 47}}, "shadow": {"shadow": {"scaleX": 2.18, "scaleY": 2.18, "width": 48, "height": 20}}, "shadow2": {"shadow2": {"type": "mesh", "path": "shadow", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [31.13, -12.97, -31.13, -12.97, -31.13, 12.97, 31.13, 12.97], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 20}}, "shadow3": {"shadow2": {"type": "mesh", "path": "shadow", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [31.13, -12.97, -31.13, -12.97, -31.13, 12.97, 31.13, 12.97], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 20}}, "shadow4": {"shadow2": {"type": "mesh", "path": "shadow", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [31.13, -12.97, -31.13, -12.97, -31.13, 12.97, 31.13, 12.97], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 20}}, "shadow5": {"shadow": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [31.13, -12.97, -31.13, -12.97, -31.13, 12.97, 31.13, 12.97], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 20}}, "shadow6": {"shadow": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [37.91, -15.8, -37.91, -15.8, -37.91, 15.8, 37.91, 15.8], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 20}}, "shadow7": {"shadow": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [34.93, -14.55, -34.93, -14.55, -34.93, 14.55, 34.93, 14.55], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 20}}, "shadow8": {"shadow": {"scaleX": 1.9889, "scaleY": 1.9889, "width": 48, "height": 20}}, "shadow9": {"shadow": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [19.2, -8, -19.2, -8, -19.2, 8, 19.2, 8], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 20}}, "shadow10": {"shadow": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [24, -10, -24, -10, -24, 10, 24, 10], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 20}}, "shadow11": {"shadow": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [31.13, -12.97, -31.13, -12.97, -31.13, 12.97, 31.13, 12.97], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 20}}, "skull01": {"skull01": {"type": "path", "lengths": [171.04, 480.48, 721.14], "vertexCount": 9, "vertices": [-29.1, -132.99, -2.44, -0.29, 25.82, 140.44, 38.11, 380.78, 142.58, 381.93, 254.42, 383.16, 313.41, 30.43, 312.18, -345.64, 311.32, -608.65]}}, "skull02": {"skull02": {"type": "path", "lengths": [113.32, 290.07, 445.02], "vertexCount": 9, "vertices": [12.14, -130.2, 2.46, -2.38, -7.22, 125.43, -18.28, 253.38, -73.58, 258.91, -128.88, 264.44, -174.5, 58.45, -174.5, -157.22, -174.5, -372.89]}}, "skull03": {"skull03": {"type": "path", "lengths": [96.19, 305.95, 480.6], "vertexCount": 9, "vertices": [-7.79, -106.4, -6.06, 0.77, -4.33, 107.95, 3.5, 230.37, 54.37, 217.41, 112.52, 202.58, 117.08, -88.16, 121.64, -295.67, 126.35, -509.98]}}, "skull04": {"skull01": {"type": "path", "lengths": [171.04, 480.48, 721.14], "vertexCount": 9, "vertices": [-29.1, -132.99, -2.44, -0.29, 25.82, 140.44, 38.11, 380.78, 142.58, 381.93, 254.42, 383.16, 313.41, 30.43, 312.18, -345.64, 311.32, -608.65]}}, "skull05": {"skull02": {"type": "path", "lengths": [113.32, 290.07, 445.02], "vertexCount": 9, "vertices": [12.14, -130.2, 2.46, -2.38, -7.22, 125.43, -18.28, 253.38, -73.58, 258.91, -128.88, 264.44, -174.5, 58.45, -174.5, -157.22, -174.5, -372.89]}}, "skull06": {"skull03": {"type": "path", "lengths": [96.19, 305.95, 480.6], "vertexCount": 9, "vertices": [-7.79, -106.4, -6.06, 0.77, -4.33, 107.95, 3.5, 230.37, 54.37, 217.41, 112.52, 202.58, 117.08, -88.16, 121.64, -295.67, 126.35, -509.98]}}, "skull07": {"skull01": {"type": "path", "lengths": [171.04, 480.48, 721.14], "vertexCount": 9, "vertices": [-29.1, -132.99, -2.44, -0.29, 25.82, 140.44, 38.11, 380.78, 142.58, 381.93, 254.42, 383.16, 313.41, 30.43, 312.18, -345.64, 311.32, -608.65]}}, "skull08": {"skull02": {"type": "path", "lengths": [113.32, 290.07, 445.02], "vertexCount": 9, "vertices": [12.14, -130.2, 2.46, -2.38, -7.22, 125.43, -18.28, 253.38, -73.58, 258.91, -128.88, 264.44, -174.5, 58.45, -174.5, -157.22, -174.5, -372.89]}}, "skull09": {"skull03": {"type": "path", "lengths": [96.19, 305.95, 480.6], "vertexCount": 9, "vertices": [-7.79, -106.4, -6.06, 0.77, -4.33, 107.95, 3.5, 230.37, 54.37, 217.41, 112.52, 202.58, 117.08, -88.16, 121.64, -295.67, 126.35, -509.98]}}, "spiral_beam1": {"spiral_beam1": {"type": "mesh", "uvs": [1, 0.78652, 1, 1, 0, 1, 0, 0.78652, 0, 0, 1, 0], "triangles": [3, 4, 5, 3, 5, 0, 1, 2, 3, 1, 3, 0], "vertices": [63.5, 23.4, 63.5, -60.08, -63.5, -60.08, -63.5, 23.4, -63.49, 38.23, 63.5, 38.23], "hull": 6, "edges": [2, 4, 8, 10, 4, 6, 6, 8, 2, 0, 0, 10, 6, 0], "width": 150, "height": 232}}, "spiral_beam2": {"spiral_beam2": {"type": "mesh", "uvs": [1, 0.78652, 1, 1, 0, 1, 0, 0.78652, 0, 0, 1, 0], "triangles": [3, 4, 5, 3, 5, 0, 2, 3, 0, 2, 0, 1], "vertices": [91.27, -20.4, 91.27, -116.06, -91.27, -116.06, -91.27, -20.4, -91.27, 38.61, 91.27, 38.61], "hull": 6, "edges": [2, 4, 8, 10, 4, 6, 6, 8, 2, 0, 0, 10, 6, 0], "width": 306, "height": 377}}, "spiral_beam3": {"spiral_beam1": {"type": "mesh", "uvs": [1, 0.78652, 1, 1, 0, 1, 0, 0.78652, 0, 0, 1, 0], "triangles": [3, 4, 5, 3, 5, 0, 1, 2, 3, 1, 3, 0], "vertices": [63.5, 23.4, 63.5, -60.08, -63.5, -60.08, -63.5, 23.4, -63.49, 38.23, 63.5, 38.23], "hull": 6, "edges": [2, 4, 8, 10, 4, 6, 6, 8, 2, 0, 0, 10, 6, 0], "width": 150, "height": 232}}, "spiral_beam4": {"spiral_beam2": {"type": "mesh", "uvs": [1, 0.78652, 1, 1, 0, 1, 0, 0.78652, 0, 0, 1, 0], "triangles": [3, 4, 5, 3, 5, 0, 2, 3, 0, 2, 0, 1], "vertices": [91.27, -20.4, 91.27, -116.06, -91.27, -116.06, -91.27, -20.4, -91.27, 38.61, 91.27, 38.61], "hull": 6, "edges": [2, 4, 8, 10, 4, 6, 6, 8, 2, 0, 0, 10, 6, 0], "width": 306, "height": 377}}, "spiral_circle1": {"spiral_circle1": {"width": 200, "height": 200}}, "spiral_circle2": {"spiral_circle2": {"scaleX": 0.7954, "scaleY": 0.7954, "width": 200, "height": 200}}, "spiral_circle3": {"spiral_circle2": {"scaleX": 0.7954, "scaleY": 0.7954, "width": 200, "height": 200}}, "spiral_circle4": {"spiral_circle1": {"width": 200, "height": 200}}, "spiral_circle5": {"spiral_circle2": {"scaleX": 0.7954, "scaleY": 0.7954, "width": 200, "height": 200}}, "spiral_circle6": {"spiral_circle2": {"scaleX": 0.7954, "scaleY": 0.7954, "width": 200, "height": 200}}, "spiral_eff1": {"spiral_eff1": {"width": 200, "height": 200}}, "spiral_eff2": {"spiral_eff1": {"width": 200, "height": 200}}, "spiral_sparkle1": {"spiral_sparkle1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.7, 188.39, -8.7, 188.39, -8.7, 205.79, 8.7, 205.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 58}}, "spiral_sparkle2": {"spiral_sparkle1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.7, -8.7, -8.7, -8.7, -8.7, 8.7, 8.7, 8.7], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 58}}, "spiral_sparkle3": {"spiral_sparkle1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.7, 130.27, -8.7, 130.27, -8.7, 147.67, 8.7, 147.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 58}}, "spiral_sparkle4": {"spiral_sparkle1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.7, 122.92, -8.7, 122.92, -8.7, 140.32, 8.7, 140.32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 58}}, "spiral_sparkle5": {"spiral_sparkle1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.7, 8.09, -8.7, 8.09, -8.7, 25.49, 8.7, 25.49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 58}}, "spiral_sparkle6": {"spiral_sparkle1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.7, 73.89, -8.7, 73.89, -8.7, 91.29, 8.7, 91.29], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 58}}, "spiral_sparkle7": {"spiral_sparkle1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.7, 139.23, -8.7, 139.23, -8.7, 156.63, 8.7, 156.63], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 58}}, "spiral_sparkle8": {"spiral_sparkle1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [7.59, 8.25, -9.81, 8.25, -9.81, 25.65, 7.59, 25.65], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 58}}, "spiral_sparkle9": {"spiral_sparkle1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.7, 154.87, -8.7, 154.87, -8.7, 172.27, 8.7, 172.27], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 58}}, "spiral_sparkle10": {"spiral_sparkle1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.7, 99.09, -8.7, 99.09, -8.7, 116.49, 8.7, 116.49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 58}}, "spiral_sparkle11": {"spiral_sparkle1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.7, 188.39, -8.7, 188.39, -8.7, 205.79, 8.7, 205.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 58}}, "spiral_sparkle12": {"spiral_sparkle1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.7, -8.7, -8.7, -8.7, -8.7, 8.7, 8.7, 8.7], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 58}}, "spiral_sparkle13": {"spiral_sparkle1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.7, 130.27, -8.7, 130.27, -8.7, 147.67, 8.7, 147.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 58}}, "spiral_sparkle14": {"spiral_sparkle1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.7, 122.92, -8.7, 122.92, -8.7, 140.32, 8.7, 140.32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 58}}, "spiral_sparkle15": {"spiral_sparkle1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.7, 8.09, -8.7, 8.09, -8.7, 25.49, 8.7, 25.49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 58}}, "spiral_sparkle16": {"spiral_sparkle1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.7, 73.89, -8.7, 73.89, -8.7, 91.29, 8.7, 91.29], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 58}}, "spiral_sparkle17": {"spiral_sparkle1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.7, 139.23, -8.7, 139.23, -8.7, 156.63, 8.7, 156.63], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 58}}, "spiral_sparkle18": {"spiral_sparkle1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [7.59, 8.25, -9.81, 8.25, -9.81, 25.65, 7.59, 25.65], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 58}}, "spiral_sparkle19": {"spiral_sparkle1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.7, 154.87, -8.7, 154.87, -8.7, 172.27, 8.7, 172.27], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 58}}, "spiral_sparkle20": {"spiral_sparkle1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.7, 99.09, -8.7, 99.09, -8.7, 116.49, 8.7, 116.49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 58}}, "undead_knight_body": {"undead_knight_body2": {"path": "undead_knight_body", "x": -2.15, "y": 57.01, "width": 96, "height": 114}}, "undead_knight_body2": {"undead_knight_body2": {"path": "undead_knight_body", "x": -2.15, "y": 57.01, "width": 96, "height": 114}}, "undead_knight_body3": {"undead_knight_body2": {"path": "undead_knight_body", "x": -2.15, "y": 57.01, "width": 96, "height": 114}}, "undead_knight_sword": {"undead_knight_sword2": {"path": "undead_knight_sword", "x": 29.46, "y": 0.88, "rotation": 0.28, "width": 100, "height": 48}}, "undead_knight_sword2": {"undead_knight_sword2": {"path": "undead_knight_sword", "x": 29.46, "y": 0.88, "rotation": 0.28, "width": 100, "height": 48}}, "undead_knight_sword3": {"undead_knight_sword2": {"path": "undead_knight_sword", "x": 29.46, "y": 0.88, "rotation": 0.28, "width": 100, "height": 48}}, "unit_h4_attack": {"unit_h4_attack": {"x": 241.9, "y": -2.67, "width": 500, "height": 50}, "unit_h4_attack1": {"x": 241.9, "y": -2.67, "width": 500, "height": 50}, "unit_h4_attack2": {"x": 241.9, "y": -2.67, "width": 500, "height": 50}, "unit_h4_attack4": {"x": 241.9, "y": -2.67, "width": 500, "height": 50}}, "unit_h4_attack_c": {"unit_h4_attack_c0": {"scaleX": 1.01, "width": 173, "height": 173}}, "unit_h4_attack_c2": {"unit_h4_attack_c0": {"scaleX": 1.01, "width": 173, "height": 173}}, "unit_h4_attack_c3": {"unit_h4_attack_c0": {"scaleX": 1.01, "width": 173, "height": 173}}, "unit_h4_attack_ch1": {"unit_h4_attack_c2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132.31, -131, -132.31, -131, -132.31, 131, 132.31, 131], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 98, "height": 98}}, "unit_h4_attack_ch2": {"unit_h4_attack_c2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [153.29, -151.77, -153.29, -151.77, -153.29, 151.77, 153.29, 151.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 98, "height": 98}}, "unit_h4_attack_ch3": {"unit_h4_attack_c2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [146.84, -145.38, -146.84, -145.38, -146.84, 145.38, 146.84, 145.38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 98, "height": 98}}, "unit_h4_body": {"unit_h4_body": {"x": 27.19, "y": 0.45, "rotation": -89.05, "width": 79, "height": 62}}, "unit_h4_body_shine": {"unit_h4_body_shine": {"x": 31.77, "y": -9.16, "rotation": -89.05, "width": 54, "height": 49}}, "unit_h4_eye": {"unit_h4_eye": {"x": -0.29, "y": 0.49, "rotation": -89.35, "width": 74, "height": 44}}, "unit_h4_hairB": {"unit_h4_hairB": {"x": -18.54, "y": 12.59, "rotation": -89.35, "width": 70, "height": 46}}, "unit_h4_head1": {"unit_h4_head1": {"x": 44.08, "y": 7.96, "rotation": -89.35, "width": 106, "height": 103}, "unit_h4_head2": {"type": "mesh", "uvs": [0.99999, 0.31421, 0.99999, 0.42198, 1, 1, 0, 1, 0.00147, 0.55849, 0.00162, 0.51442, 0, 0, 1, 0, 0.23944, 0.43385, 0.23936, 0.30998, 0.68481, 0.23004, 0.2378, 0.48917, 0.30748, 0.39298, 0.68346, 0.29891], "triangles": [10, 6, 7, 10, 9, 6, 10, 12, 9, 0, 10, 7, 13, 10, 0, 13, 12, 10, 13, 0, 1, 8, 9, 12, 5, 9, 8, 9, 5, 6, 11, 8, 12, 11, 5, 8, 4, 5, 11, 13, 1, 2, 11, 12, 13, 2, 11, 13, 3, 4, 11, 2, 3, 11], "vertices": [71.91, -45.62, 65.33, -45.7, -2.87, -46.48, -4.05, 56.52, 48.05, 56.96, 47.12, 56.93, 97.22, 57.67, 98.39, -45.32, 56.9, 32.55, 71.52, 32.72, 81.47, -13.05, 56.5, 32.71, 67.93, 25.67, 79.48, -12.93], "hull": 8, "edges": [4, 6, 12, 14, 10, 12, 10, 16, 16, 18, 18, 20, 0, 14, 20, 0, 6, 8, 8, 10, 8, 22, 22, 24, 24, 26, 0, 2, 2, 4, 26, 2], "width": 103, "height": 118}}, "unit_h4_hurt_eff": {"unit_h4_hurt1": {"x": 7.32, "y": 5.21, "width": 220, "height": 190}, "unit_h4_hurt2": {"x": 17.53, "y": -6.39, "width": 220, "height": 190}, "unit_h4_hurt3": {"x": 15.8, "y": -5.52, "width": 220, "height": 190}}, "unit_h4_lightning": {"unit_h4_lightning01": {"type": "mesh", "uvs": [0.42787, 0, 0.27857, 0.14086, 0.44244, 0.25872, 0.65364, 0.23639, 0.67913, 0], "triangles": [4, 1, 0, 3, 1, 4, 2, 1, 3], "vertices": [-15.58, 618.08, -47.83, 528.77, -12.43, 454.05, 33.19, 468.21, 38.69, 618.08], "hull": 5, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 0, 8], "width": 216, "height": 634}, "unit_h4_lightning02": {"type": "mesh", "uvs": [0.37972, 0.0034, 0.24612, 0.20229, 0.35358, 0.47144, 0.72535, 0.45956, 0.68469, 0], "triangles": [1, 0, 4, 3, 1, 4, 2, 1, 3], "vertices": [-25.98, 615.93, -54.84, 489.83, -31.63, 319.19, 48.68, 326.72, 39.89, 618.08], "hull": 5, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 0, 8], "width": 216, "height": 634}, "unit_h4_lightning03": {"type": "mesh", "uvs": [0.31552, 0, 0.08231, 0.57066, 0.39325, 1, 0.688, 1, 0.85643, 0.42168, 0.66533, 0], "triangles": [0, 4, 1, 4, 0, 5, 3, 2, 1, 4, 3, 1], "vertices": [-39.85, 618.08, -90.22, 256.29, -23.06, -15.92, 40.61, -15.92, 76.99, 350.74, 35.71, 618.08], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 216, "height": 634}, "unit_h4_lightning04": {"type": "mesh", "uvs": [0.48071, 0.01007, 0.35762, 0.04318, 0.12118, 0.26388, 0.07259, 1, 0.9536, 1, 0.8791, 0.60045, 0.58112, 0], "triangles": [1, 0, 6, 6, 2, 1, 5, 2, 6, 3, 2, 5, 3, 5, 4], "vertices": [-4.17, 611.69, -30.75, 590.71, -81.83, 450.78, -92.32, -15.92, 97.98, -15.92, 81.89, 237.4, 17.52, 618.08], "hull": 7, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 0, 12], "width": 216, "height": 634}, "unit_h4_lightning05": {"type": "mesh", "uvs": [0.99571, 1, 1, 0.57728, 0.5552, 0.14581, 0.44184, 0.1436, 0, 0.51658, 0, 1], "triangles": [4, 3, 2, 4, 2, 1, 5, 4, 1, 0, 5, 1], "vertices": [107.07, -15.92, 108, 252.09, 11.92, 525.64, -12.56, 527.04, -108, 290.57, -108, -15.92], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 216, "height": 634}, "unit_h4_lightning06": {"type": "mesh", "uvs": [1, 1, 1, 0.88405, 0.57788, 0.31575, 0.35762, 0.31575, 0, 0.87633, 0, 1], "triangles": [1, 5, 4, 4, 3, 2, 2, 1, 4, 5, 1, 0], "vertices": [108, -15.92, 108, 57.59, 16.82, 417.9, -30.75, 417.9, -108, 62.49, -108, -15.92], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 216, "height": 634}, "unit_h4_lightning07": {"type": "mesh", "uvs": [1, 1, 1, 0.78253, 0.56816, 0.63245, 0.3641, 0.62914, 0, 0.87191, 0, 1], "triangles": [1, 5, 4, 4, 3, 2, 4, 2, 1, 0, 5, 1], "vertices": [108, -15.92, 108, 121.96, 14.72, 217.11, -29.35, 219.21, -108, 65.29, -108, -15.92], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 216, "height": 634}, "unit_h4_lightning08": {"type": "mesh", "uvs": [1, 1, 1, 0.9006, 0.77869, 0.81563, 0.13413, 0.82115, 0, 0.92929, 0, 1], "triangles": [2, 5, 4, 2, 1, 0, 2, 4, 3, 0, 5, 2], "vertices": [108, -15.92, 108, 47.1, 60.2, 100.97, -79.03, 97.47, -108, 28.91, -108, -15.92], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 216, "height": 634}}, "unit_h4_skill_ready1": {"unit_h4_skill_ready_1": {"width": 245, "height": 162}, "unit_h4_skill_ready_11": {"width": 245, "height": 162}}, "unit_h4_skill_ready2": {"unit_h4_skill_ready_2": {"width": 245, "height": 162}, "unit_h4_skill_ready_21": {"width": 245, "height": 162}}, "unit_h4_skill_ready3": {"unit_h4_skill_ready_3": {"width": 245, "height": 162}, "unit_h4_skill_ready_31": {"width": 245, "height": 162}}, "unit_h4_skill_ready_eff1": {"unit_h4_attack_c1": {"scaleX": 0.9821, "scaleY": 0.9821, "width": 145, "height": 145}}, "unit_h4_skill_ready_eff2": {"unit_h4_attack_c1": {"scaleX": 0.9821, "scaleY": 0.9821, "width": 145, "height": 145}}, "unit_h4_skill_ready_eff3": {"unit_h4_attack_c1": {"scaleX": 0.9821, "scaleY": 0.9821, "width": 145, "height": 145}}, "unit_h4_spellbook": {"unit_h4_spellbook": {"x": 14.2, "y": -11.9, "rotation": -89.05, "width": 60, "height": 47}}, "unit_h4_spellbookB": {"unit_h4_spellbookB": {"type": "mesh", "uvs": [1, 0.53681, 1, 1, 0, 1, 0, 0.23489, 0, 0, 1, 0, 0.82825, 0.23489, 0.86685, 0.45581], "triangles": [2, 6, 7, 7, 0, 1, 2, 3, 6, 7, 5, 0, 7, 6, 5, 5, 6, 4, 6, 3, 4, 2, 7, 1], "vertices": [1, 107, -3.71, -20.33, 1, 1, 105, 3.67, -45.28, 1, 1, 105, 2.65, 16.71, 1, 1, 107, 2.81, 41.79, 1, 1, 107, 8.69, 41.89, 1, 1, 107, 9.71, -20.1, 1, 1, 107, 3.66, -9.55, 1, 1, 107, -1.82, -12.04, 1], "hull": 6, "edges": [2, 4, 8, 10, 4, 6, 6, 8, 6, 12, 12, 14, 2, 0, 0, 10, 14, 0], "width": 62, "height": 25}}, "unit_h4_spellbookF": {"unit_h4_spellbookF": {"type": "mesh", "uvs": [0.80356, 0.54053, 1, 0.53756, 1, 1, 0.3643, 1, 0.29322, 0.76916, 0, 0.398, 0, 0, 0.60199, 0, 0.31187, 0.71571, 0.69637, 0.71571, 0.39576, 0.2822, 0.03456, 0.28517], "triangles": [5, 11, 4, 8, 11, 10, 5, 6, 11, 11, 6, 10, 10, 6, 7, 10, 7, 9, 8, 10, 9, 3, 9, 2, 9, 0, 2, 0, 1, 2, 3, 8, 9, 9, 7, 0, 4, 8, 3, 4, 11, 8], "vertices": [1, 105, 11.38, -11.67, 1, 1, 105, 11.73, -27.19, 1, 1, 105, -2.6, -27.43, 1, 1, 105, -3.43, 22.79, 1, 1, 105, 3.63, 28.52, 1, 1, 108, -3.69, 18.4, 1, 1, 108, 8.65, 18.6, 1, 1, 108, 9.44, -28.95, 1, 1, 105, 5.31, 27.08, 1, 1, 105, 5.81, -3.3, 1, 1, 108, 0.42, -12.8, 1, 1, 108, -0.14, 15.73, 1], "hull": 8, "edges": [4, 2, 2, 0, 12, 14, 0, 14, 10, 12, 10, 8, 4, 6, 8, 6, 8, 16, 16, 18, 18, 0, 20, 22, 20, 18], "width": 79, "height": 31}}, "unit_h4_spellbook_paper1": {"unit_h4_spellbook_paper": {"type": "mesh", "uvs": [0.95533, 0.95163, 0.75, 0.95164, 0.5, 0.95164, 0.25, 0.95164, 0.04344, 0.95164, 0.04344, 0.75001, 0.04344, 0.50001, 0.04344, 0.25001, 0.04344, 0.10154, 0.04344, 0.02165, 0.25, 0.02163, 0.5, 0.02163, 0.75, 0.02163, 0.95533, 0.02163, 0.95533, 0.10476, 0.95533, 0.24999, 0.95533, 0.49999, 0.95533, 0.74999, 0.75, 0.75, 0.5, 0.75, 0.25, 0.75, 0.75, 0.5, 0.5, 0.5, 0.25, 0.5, 0.75, 0.25, 0.5, 0.25, 0.25, 0.25, 0.25019, 0.10361, 0.50044, 0.10568, 0.75159, 0.10464], "triangles": [22, 25, 24, 7, 26, 23, 23, 26, 25, 16, 24, 15, 26, 7, 27, 25, 28, 24, 24, 29, 15, 24, 28, 29, 25, 26, 28, 26, 27, 28, 27, 7, 8, 15, 29, 14, 27, 11, 28, 28, 12, 29, 28, 11, 12, 14, 29, 13, 29, 12, 13, 27, 8, 10, 8, 9, 10, 27, 10, 11, 4, 5, 3, 3, 20, 2, 2, 19, 1, 2, 20, 19, 1, 18, 0, 1, 19, 18, 3, 5, 20, 0, 18, 17, 5, 6, 20, 17, 18, 21, 18, 19, 21, 20, 22, 19, 19, 22, 21, 6, 23, 20, 20, 23, 22, 21, 16, 17, 6, 7, 23, 16, 21, 24, 21, 22, 24, 23, 25, 22], "vertices": [1, 105, 3.53, -15.28, 1, 1, 105, 3.38, -6.45, 1, 1, 105, 3.21, 4.29, 1, 1, 105, 3.03, 15.04, 1, 1, 105, 2.88, 23.92, 1, 2, 105, 9.82, 21.72, 0.89714, 106, -29.66, 16.83, 0.10286, 2, 105, 17.17, 17.07, 0.68571, 106, -22.31, 12.18, 0.31429, 2, 105, 23.08, 10.2, 0.37714, 106, -16.4, 5.31, 0.62286, 2, 105, 25.73, 4.83, 0.13714, 106, -13.74, -0.06, 0.86286, 1, 106, -12.42, -3.13, 1, 1, 106, -12.27, -12.01, 1, 1, 106, -12.09, -22.76, 1, 1, 106, -11.92, -33.51, 1, 1, 106, -11.77, -42.34, 1, 2, 105, 26.26, -34.38, 0.13714, 106, -13.22, -39.27, 0.86286, 2, 105, 23.3, -29.65, 0.34857, 106, -16.17, -34.54, 0.65143, 2, 105, 17.31, -22.92, 0.65143, 106, -22.17, -27.81, 0.34857, 2, 105, 10.47, -17.49, 0.89714, 106, -29.01, -22.38, 0.10286, 2, 105, 10.32, -8.66, 0.89714, 106, -29.16, -13.55, 0.10286, 2, 105, 10.14, 2.09, 0.89714, 106, -29.33, -2.8, 0.10286, 2, 105, 9.97, 12.84, 0.89714, 106, -29.51, 7.95, 0.10286, 2, 105, 17.67, -13.31, 0.68571, 106, -21.8, -18.2, 0.31429, 2, 105, 17.5, -2.56, 0.68571, 106, -21.98, -7.45, 0.31429, 2, 105, 17.32, 8.19, 0.68571, 106, -22.16, 3.3, 0.31429, 2, 105, 23.58, -20.17, 0.37714, 106, -15.9, -25.06, 0.62286, 2, 105, 23.4, -9.43, 0.37714, 106, -16.07, -14.32, 0.62286, 2, 105, 23.23, 1.32, 0.37714, 106, -16.25, -3.57, 0.62286, 2, 105, 25.8, -4.06, 0.13714, 106, -13.68, -8.95, 0.86286, 2, 105, 25.9, -14.82, 0.13714, 106, -13.58, -19.71, 0.86286, 2, 105, 26.12, -25.62, 0.13714, 106, -13.36, -30.51, 0.86286], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 30, 32, 32, 34, 34, 0, 14, 16, 16, 18, 16, 54, 54, 56, 56, 58, 26, 28, 28, 30, 58, 28], "width": 43, "height": 42}}, "unit_h4_spellbook_paper2": {"unit_h4_spellbook_paper": {"type": "mesh", "uvs": [0.95533, 0.95163, 0.75, 0.95164, 0.5, 0.95164, 0.25, 0.95164, 0.04344, 0.95164, 0.04344, 0.75001, 0.04344, 0.50001, 0.04344, 0.25001, 0.04344, 0.10154, 0.04344, 0.02165, 0.25, 0.02163, 0.5, 0.02163, 0.75, 0.02163, 0.95533, 0.02163, 0.95533, 0.10476, 0.95533, 0.24999, 0.95533, 0.49999, 0.95533, 0.74999, 0.75, 0.75, 0.5, 0.75, 0.25, 0.75, 0.75, 0.5, 0.5, 0.5, 0.25, 0.5, 0.75, 0.25, 0.5, 0.25, 0.25, 0.25, 0.25019, 0.10361, 0.50044, 0.10568, 0.75159, 0.10464], "triangles": [16, 24, 15, 23, 26, 25, 7, 26, 23, 22, 25, 24, 27, 10, 11, 8, 9, 10, 29, 12, 13, 28, 11, 12, 27, 8, 10, 14, 29, 13, 28, 12, 29, 27, 11, 28, 15, 29, 14, 27, 7, 8, 26, 27, 28, 24, 28, 29, 25, 26, 28, 24, 29, 15, 25, 28, 24, 26, 7, 27, 23, 25, 22, 21, 22, 24, 16, 21, 24, 6, 7, 23, 21, 16, 17, 20, 23, 22, 6, 23, 20, 19, 22, 21, 20, 22, 19, 18, 19, 21, 17, 18, 21, 5, 6, 20, 0, 18, 17, 3, 5, 20, 1, 19, 18, 1, 18, 0, 2, 20, 19, 2, 19, 1, 3, 20, 2, 4, 5, 3], "vertices": [1, 105, 3.53, -15.28, 1, 1, 105, 3.38, -6.45, 1, 1, 105, 3.21, 4.29, 1, 1, 105, 3.03, 15.04, 1, 1, 105, 2.88, 23.92, 1, 2, 105, 9.45, 20.9, 0.86286, 109, -30.43, 16.27, 0.13714, 2, 105, 16.65, 15.66, 0.62857, 109, -23.23, 11.03, 0.37143, 2, 105, 22.84, 8.98, 0.33143, 109, -17.03, 4.35, 0.66857, 2, 105, 25.55, 3.92, 0.10857, 109, -14.32, -0.71, 0.89143, 1, 109, -12.42, -3.13, 1, 1, 109, -12.27, -12.01, 1, 1, 109, -12.09, -22.76, 1, 1, 109, -11.92, -33.51, 1, 1, 109, -11.77, -42.34, 1, 2, 105, 26.36, -34.77, 0.13143, 109, -13.51, -39.4, 0.86857, 2, 105, 23.47, -30.23, 0.33143, 109, -16.4, -34.86, 0.66857, 2, 105, 17.29, -23.55, 0.62857, 109, -22.59, -28.18, 0.37143, 2, 105, 10.1, -18.31, 0.86286, 109, -29.78, -22.94, 0.13714, 2, 105, 9.95, -9.48, 0.86286, 109, -29.92, -14.11, 0.13714, 2, 105, 9.77, 1.27, 0.86286, 109, -30.1, -3.36, 0.13714, 2, 105, 9.6, 12.01, 0.86286, 109, -30.28, 7.39, 0.13714, 2, 105, 17.15, -14.72, 0.62857, 109, -22.73, -19.35, 0.37143, 2, 105, 16.97, -3.97, 0.62857, 109, -22.9, -8.6, 0.37143, 2, 105, 16.79, 6.78, 0.62857, 109, -23.08, 2.15, 0.37143, 2, 105, 23.34, -21.4, 0.33143, 109, -16.53, -26.03, 0.66857, 2, 105, 23.16, -10.65, 0.33143, 109, -16.71, -15.28, 0.66857, 2, 105, 22.99, 0.09, 0.33143, 109, -16.89, -4.53, 0.66857, 2, 105, 25.62, -4.97, 0.10857, 109, -14.26, -9.6, 0.89143, 2, 105, 25.71, -15.73, 0.10857, 109, -14.16, -20.36, 0.89143, 2, 105, 25.93, -26.53, 0.10857, 109, -13.94, -31.16, 0.89143], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 30, 32, 32, 34, 34, 0, 14, 16, 16, 18, 16, 54, 54, 56, 56, 58, 26, 28, 28, 30, 58, 28], "width": 43, "height": 42}}, "unit_h4_spellbook_shine": {"unit_h4_spellbook_shine": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-14.9, -57.76, -16.94, 65.22, 40.06, 66.17, 42.09, -56.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 123, "height": 57}}, "unit_h4_stun1": {"unit_h4_stun1": {"width": 101, "height": 34}}, "unit_h4_stun2": {"unit_h4_stun2": {"width": 25, "height": 25}}, "unit_h4_stun3": {"unit_h4_stun3": {"width": 25, "height": 25}}, "unit_h4_stunning": {"unit_h4_stunning": {"type": "path", "closed": true, "lengths": [25.08, 41.46, 59.02, 83.48, 110.42, 125.55, 139.82, 168.51], "vertexCount": 24, "vertices": [-12.77, 14.82, 0.85, 14.72, 11.84, 14.64, 21.98, 13.66, 31.86, 10.78, 41.75, 7.9, 48.14, 4.08, 48.06, -0.12, 47.98, -4.33, 39.26, -9.54, 30.31, -11.49, 21.35, -13.43, 8.97, -14.68, -0.06, -14.68, -9.09, -14.68, -25.16, -13.19, -33.41, -10.62, -41.66, -8.05, -48.28, -3.85, -48.2, -0.11, -48.13, 3.63, -43.38, 7.21, -34.58, 10.09, -25.78, 12.97]}}, "wind_eff": {"wind_eff": {"type": "path", "lengths": [1138.7, 2359.73], "vertexCount": 6, "vertices": [-817.15, 158.08, -514.46, 74.92, -327.42, 23.53, 144.33, -39.83, 606.4, 141.52, 1102, 336.02]}}, "wind_effect": {"hero_2_wind_eff": {"x": -1.01, "y": 8.5, "width": 360, "height": 50}}, "wind_effect2": {"hero_2_wind_eff": {"x": -1.01, "y": 8.5, "width": 360, "height": 50}}}}], "events": {"attack": {}}, "animations": {"hero_package_1": {"slots": {"archer_arrow": {"attachment": [{}]}, "archer_body": {"attachment": [{}]}, "archer_bow": {"attachment": [{}]}, "die_skull01": {"attachment": [{}]}, "die_skull02": {"attachment": [{}]}, "die_skull03": {"attachment": [{}]}, "die_skull04": {"attachment": [{}]}, "die_skull05": {"attachment": [{}]}, "die_skull06": {"attachment": [{}]}, "die_skull07": {"attachment": [{}]}, "die_skull08": {"attachment": [{}]}, "die_skull09": {"attachment": [{}]}, "die_smoke": {"attachment": [{}]}, "die_smoke2": {"attachment": [{}]}, "die_smoke3": {"attachment": [{}]}, "die_smoke4": {"attachment": [{}]}, "die_smoke5": {"attachment": [{}]}, "die_smoke6": {"attachment": [{}]}, "die_smoke7": {"attachment": [{}]}, "die_smoke8": {"attachment": [{}]}, "die_smoke9": {"attachment": [{}]}, "hero1_sword_eff": {"rgba": [{"time": 1.9333, "color": "fff4b9c7", "curve": [2, 1, 2.067, 1, 2, 0.96, 2.067, 0.96, 2, 0.73, 2.067, 0.73, 2, 0.78, 2.067, 0]}, {"time": 2.1333, "color": "fff4b900"}], "attachment": [{"time": 1.9333, "name": "hero1_sword_eff"}, {"time": 2.2}]}, "hero_2_axe": {"attachment": [{}]}, "hero_2_body": {"attachment": [{}]}, "hero_2_hair": {"attachment": [{}]}, "hero_3_arrow": {"attachment": [{}]}, "hero_3_arrow2": {"attachment": [{}]}, "hero_3_body": {"attachment": [{}]}, "hero_3_bow_attack": {"attachment": [{}]}, "hero_3_bow_idle": {"attachment": [{}]}, "hero_3_bow_string1": {"attachment": [{}]}, "hero_3_bow_string2": {"attachment": [{}]}, "hero_3_bow_wingB": {"attachment": [{}]}, "hero_3_bow_wingF": {"attachment": [{}]}, "hobbit_W_body": {"attachment": [{}]}, "hobbit_W_sword": {"attachment": [{}]}, "knight_body": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2.0667, "color": "ffffff00"}, {"time": 2.2, "color": "ffffffff", "curve": "stepped"}, {"time": 5.1333, "color": "ffffffff"}, {"time": 5.3333, "color": "ffffff00"}]}, "knight_body2": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2.0667, "color": "ffffff00"}, {"time": 2.2, "color": "ffffffff", "curve": "stepped"}, {"time": 5.1333, "color": "ffffffff"}, {"time": 5.3333, "color": "ffffff00"}]}, "knight_sword": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2.0667, "color": "ffffff00"}, {"time": 2.2, "color": "ffffffff", "curve": "stepped"}, {"time": 5.1333, "color": "ffffffff"}, {"time": 5.3333, "color": "ffffff00"}]}, "knight_sword2": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2.0667, "color": "ffffff00"}, {"time": 2.2, "color": "ffffffff", "curve": "stepped"}, {"time": 5.1333, "color": "ffffffff"}, {"time": 5.3333, "color": "ffffff00"}]}, "shadow": {"attachment": [{"time": 1.3667, "name": "shadow"}]}, "shadow5": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2.0667, "color": "ffffff00"}, {"time": 2.2, "color": "ffffffff", "curve": "stepped"}, {"time": 5.1333, "color": "ffffffff"}, {"time": 5.3333, "color": "ffffff00"}]}, "shadow6": {"attachment": [{}]}, "shadow7": {"attachment": [{}]}, "shadow8": {"attachment": [{}]}, "shadow9": {"attachment": [{}]}, "shadow10": {"attachment": [{}]}, "shadow11": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2.0667, "color": "ffffff00"}, {"time": 2.2, "color": "ffffffff", "curve": "stepped"}, {"time": 5.1333, "color": "ffffffff"}, {"time": 5.3333, "color": "ffffff00"}]}, "spiral_beam1": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": [2.122, 1, 2.144, 1, 2.122, 1, 2.144, 1, 2.122, 1, 2.144, 1, 2.122, 0, 2.144, 1]}, {"time": 2.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5, "color": "ffffffff", "curve": [2.622, 1, 2.744, 1, 2.622, 1, 2.744, 1, 2.622, 1, 2.744, 1, 2.622, 1, 2.744, 0]}, {"time": 2.8667, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_beam1"}]}, "spiral_beam2": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": [2.122, 1, 2.144, 1, 2.122, 1, 2.144, 1, 2.122, 1, 2.144, 1, 2.122, 0, 2.144, 1]}, {"time": 2.1667, "color": "ffffffff", "curve": [2.256, 1, 2.344, 1, 2.256, 1, 2.344, 1, 2.256, 1, 2.344, 1, 2.256, 1, 2.344, 0]}, {"time": 2.4333, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_beam2"}]}, "spiral_beam3": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": [2.122, 1, 2.144, 1, 2.122, 1, 2.144, 1, 2.122, 1, 2.144, 1, 2.122, 0, 2.144, 1]}, {"time": 2.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5, "color": "ffffffff", "curve": [2.622, 1, 2.744, 1, 2.622, 1, 2.744, 1, 2.622, 1, 2.744, 1, 2.622, 1, 2.744, 0]}, {"time": 2.8667, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_beam1"}]}, "spiral_beam4": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": [2.122, 1, 2.144, 1, 2.122, 1, 2.144, 1, 2.122, 1, 2.144, 1, 2.122, 0, 2.144, 1]}, {"time": 2.1667, "color": "ffffffff", "curve": [2.256, 1, 2.344, 1, 2.256, 1, 2.344, 1, 2.256, 1, 2.344, 1, 2.256, 1, 2.344, 0]}, {"time": 2.4333, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_beam2"}]}, "spiral_circle1": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": [2.144, 1, 2.189, 1, 2.144, 1, 2.189, 1, 2.144, 1, 2.189, 1, 2.1, 0.7, 2.189, 1]}, {"time": 2.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6, "color": "ffffffff", "curve": [2.722, 1, 2.767, 1, 2.722, 1, 2.767, 1, 2.722, 1, 2.767, 1, 2.6, 0.3, 2.767, 0]}, {"time": 2.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{}, {"time": 2.1, "name": "spiral_circle1"}, {"time": 3.1}]}, "spiral_circle2": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": [2.133, 1, 2.167, 1, 2.133, 1, 2.167, 1, 2.133, 1, 2.167, 1, 2.1, 0.7, 2.167, 1]}, {"time": 2.2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3333, "color": "ffffffff", "curve": [2.433, 1, 2.533, 1, 2.433, 1, 2.533, 1, 2.433, 1, 2.533, 1, 2.333, 0.3, 2.533, 0]}, {"time": 2.6333, "color": "ffffff00"}], "attachment": [{}, {"time": 2.1, "name": "spiral_circle2"}, {"time": 3.1}]}, "spiral_circle3": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3, "color": "ffffff00", "curve": [2.333, 1, 2.3, 1, 2.333, 1, 2.3, 1, 2.333, 1, 2.3, 1, 2.3, 0.7, 2.3, 1]}, {"time": 2.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4, "color": "ffffffff", "curve": [2.578, 1, 2.689, 1, 2.578, 1, 2.689, 1, 2.578, 1, 2.689, 1, 2.4, 0.3, 2.689, 0]}, {"time": 2.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9333, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_circle2"}, {"time": 3.1}]}, "spiral_circle4": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": [2.144, 1, 2.189, 1, 2.144, 1, 2.189, 1, 2.144, 1, 2.189, 1, 2.1, 0.7, 2.189, 1]}, {"time": 2.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6, "color": "ffffffff", "curve": [2.722, 1, 2.767, 1, 2.722, 1, 2.767, 1, 2.722, 1, 2.767, 1, 2.6, 0.3, 2.767, 0]}, {"time": 2.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{}, {"time": 2.1, "name": "spiral_circle1"}, {"time": 3.1}]}, "spiral_circle5": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": [2.133, 1, 2.167, 1, 2.133, 1, 2.167, 1, 2.133, 1, 2.167, 1, 2.1, 0.7, 2.167, 1]}, {"time": 2.2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3333, "color": "ffffffff", "curve": [2.433, 1, 2.533, 1, 2.433, 1, 2.533, 1, 2.433, 1, 2.533, 1, 2.333, 0.3, 2.533, 0]}, {"time": 2.6333, "color": "ffffff00"}], "attachment": [{}, {"time": 2.1, "name": "spiral_circle2"}, {"time": 3.1}]}, "spiral_circle6": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3, "color": "ffffff00", "curve": [2.333, 1, 2.3, 1, 2.333, 1, 2.3, 1, 2.333, 1, 2.3, 1, 2.3, 0.7, 2.3, 1]}, {"time": 2.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4, "color": "ffffffff", "curve": [2.578, 1, 2.689, 1, 2.578, 1, 2.689, 1, 2.578, 1, 2.689, 1, 2.4, 0.3, 2.689, 0]}, {"time": 2.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9333, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_circle2"}, {"time": 3.1}]}, "spiral_eff1": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": [2.122, 1, 2.144, 1, 2.122, 1, 2.144, 1, 2.122, 1, 2.144, 1, 2.1, 0.7, 2.144, 1]}, {"time": 2.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6, "color": "ffffffff", "curve": [2.722, 1, 2.767, 1, 2.722, 1, 2.767, 1, 2.722, 1, 2.767, 1, 2.6, 0.3, 2.767, 0]}, {"time": 2.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_eff1"}, {"time": 3.1}]}, "spiral_eff2": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": [2.122, 1, 2.144, 1, 2.122, 1, 2.144, 1, 2.122, 1, 2.144, 1, 2.1, 0.7, 2.144, 1]}, {"time": 2.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6, "color": "ffffffff", "curve": [2.722, 1, 2.767, 1, 2.722, 1, 2.767, 1, 2.722, 1, 2.767, 1, 2.6, 0.3, 2.767, 0]}, {"time": 2.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_eff1"}, {"time": 3.1}]}, "spiral_sparkle1": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2333, "color": "ffffff00", "curve": [2.267, 1, 2.3, 1, 2.267, 1, 2.3, 1, 2.267, 1, 2.3, 1, 2.233, 0.7, 2.3, 1]}, {"time": 2.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7, "color": "ffffffff", "curve": [2.833, 1, 2.967, 1, 2.833, 1, 2.967, 1, 2.833, 1, 2.967, 1, 2.7, 0.3, 2.967, 0]}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_sparkle1"}]}, "spiral_sparkle2": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": [2.133, 1, 2.167, 1, 2.133, 1, 2.167, 1, 2.133, 1, 2.167, 1, 2.1, 0.7, 2.167, 1]}, {"time": 2.2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5, "color": "ffffffff", "curve": [2.633, 1, 2.767, 1, 2.633, 1, 2.767, 1, 2.633, 1, 2.767, 1, 2.5, 0.3, 2.767, 0]}, {"time": 2.9, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_sparkle1"}]}, "spiral_sparkle3": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff00", "curve": [2.367, 1, 2.4, 1, 2.367, 1, 2.4, 1, 2.367, 1, 2.4, 1, 2.333, 0.7, 2.4, 1]}, {"time": 2.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7333, "color": "ffffffff", "curve": [2.856, 1, 2.978, 1, 2.856, 1, 2.978, 1, 2.856, 1, 2.978, 1, 2.733, 0.3, 2.978, 0]}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_sparkle1"}]}, "spiral_sparkle4": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4667, "color": "ffffff00", "curve": [2.5, 1, 2.533, 1, 2.5, 1, 2.533, 1, 2.5, 1, 2.533, 1, 2.467, 0.7, 2.533, 1]}, {"time": 2.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7667, "color": "ffffffff", "curve": [2.867, 1, 2.967, 1, 2.867, 1, 2.967, 1, 2.867, 1, 2.967, 1, 2.767, 0.3, 2.967, 0]}, {"time": 3.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_sparkle1"}, {"time": 3.1, "name": "spiral_sparkle1"}]}, "spiral_sparkle5": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1333, "color": "ffffff00", "curve": [2.167, 1, 2.2, 1, 2.167, 1, 2.2, 1, 2.167, 1, 2.2, 1, 2.133, 0.7, 2.2, 1]}, {"time": 2.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6, "color": "ffffffff", "curve": [2.678, 1, 2.756, 1, 2.678, 1, 2.756, 1, 2.678, 1, 2.756, 1, 2.6, 0.65, 2.756, 0.5]}, {"time": 2.8333, "color": "ffffff80", "curve": [2.911, 1, 2.989, 1, 2.911, 1, 2.989, 1, 2.911, 1, 2.989, 1, 2.833, 0.15, 2.989, 0]}, {"time": 3.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_sparkle1"}]}, "spiral_sparkle6": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3667, "color": "ffffff00", "curve": [2.4, 1, 2.433, 1, 2.4, 1, 2.433, 1, 2.4, 1, 2.433, 1, 2.367, 0.7, 2.433, 1]}, {"time": 2.4667, "color": "ffffffff", "curve": [2.556, 1, 2.411, 1, 2.556, 1, 2.411, 1, 2.556, 1, 2.411, 1, 2.556, 1, 2.411, 0]}, {"time": 2.5667, "color": "ffffff00", "curve": [2.6, 1, 2.633, 1, 2.6, 1, 2.633, 1, 2.6, 1, 2.633, 1, 2.567, 0.7, 2.633, 1]}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8333, "color": "ffffffff", "curve": [2.922, 1, 2.956, 1, 2.922, 1, 2.956, 1, 2.922, 1, 2.956, 1, 2.833, 0.3, 2.956, 0]}, {"time": 3.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_sparkle1"}, {"time": 3.1, "name": "spiral_sparkle1"}]}, "spiral_sparkle7": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4, "color": "ffffff00", "curve": [2.433, 1, 2.467, 1, 2.433, 1, 2.467, 1, 2.433, 1, 2.467, 1, 2.4, 0.7, 2.467, 1]}, {"time": 2.5, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7, "color": "ffffffff", "curve": [2.811, 1, 2.922, 1, 2.811, 1, 2.922, 1, 2.811, 1, 2.922, 1, 2.7, 0.3, 2.922, 0]}, {"time": 3.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_sparkle1"}]}, "spiral_sparkle8": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2, "color": "ffffff00", "curve": [2.233, 1, 2.267, 1, 2.233, 1, 2.267, 1, 2.233, 1, 2.267, 1, 2.2, 0.7, 2.267, 1]}, {"time": 2.3, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6333, "color": "ffffffff", "curve": [2.756, 1, 2.878, 1, 2.756, 1, 2.878, 1, 2.756, 1, 2.878, 1, 2.633, 0.3, 2.878, 0]}, {"time": 3, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_sparkle1"}]}, "spiral_sparkle9": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4, "color": "ffffff00", "curve": [2.433, 1, 2.467, 1, 2.433, 1, 2.467, 1, 2.433, 1, 2.467, 1, 2.4, 0.7, 2.467, 1]}, {"time": 2.5, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7, "color": "ffffffff", "curve": [2.833, 1, 2.967, 1, 2.833, 1, 2.967, 1, 2.833, 1, 2.967, 1, 2.7, 0.3, 2.967, 0]}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_sparkle1"}]}, "spiral_sparkle10": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3, "color": "ffffff00", "curve": [2.333, 1, 2.367, 1, 2.333, 1, 2.367, 1, 2.333, 1, 2.367, 1, 2.3, 0.7, 2.367, 1]}, {"time": 2.4, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7333, "color": "ffffffff", "curve": [2.856, 1, 2.978, 1, 2.856, 1, 2.978, 1, 2.856, 1, 2.978, 1, 2.733, 0.3, 2.978, 0]}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_sparkle1"}]}, "spiral_sparkle11": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2333, "color": "ffffff00", "curve": [2.267, 1, 2.3, 1, 2.267, 1, 2.3, 1, 2.267, 1, 2.3, 1, 2.233, 0.7, 2.3, 1]}, {"time": 2.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7, "color": "ffffffff", "curve": [2.833, 1, 2.967, 1, 2.833, 1, 2.967, 1, 2.833, 1, 2.967, 1, 2.7, 0.3, 2.967, 0]}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_sparkle1"}]}, "spiral_sparkle12": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": [2.133, 1, 2.167, 1, 2.133, 1, 2.167, 1, 2.133, 1, 2.167, 1, 2.1, 0.7, 2.167, 1]}, {"time": 2.2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5, "color": "ffffffff", "curve": [2.633, 1, 2.767, 1, 2.633, 1, 2.767, 1, 2.633, 1, 2.767, 1, 2.5, 0.3, 2.767, 0]}, {"time": 2.9, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_sparkle1"}]}, "spiral_sparkle13": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff00", "curve": [2.367, 1, 2.4, 1, 2.367, 1, 2.4, 1, 2.367, 1, 2.4, 1, 2.333, 0.7, 2.4, 1]}, {"time": 2.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7333, "color": "ffffffff", "curve": [2.856, 1, 2.978, 1, 2.856, 1, 2.978, 1, 2.856, 1, 2.978, 1, 2.733, 0.3, 2.978, 0]}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_sparkle1"}]}, "spiral_sparkle14": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4667, "color": "ffffff00", "curve": [2.5, 1, 2.533, 1, 2.5, 1, 2.533, 1, 2.5, 1, 2.533, 1, 2.467, 0.7, 2.533, 1]}, {"time": 2.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7667, "color": "ffffffff", "curve": [2.867, 1, 2.967, 1, 2.867, 1, 2.967, 1, 2.867, 1, 2.967, 1, 2.767, 0.3, 2.967, 0]}, {"time": 3.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_sparkle1"}, {"time": 3.1, "name": "spiral_sparkle1"}]}, "spiral_sparkle15": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1333, "color": "ffffff00", "curve": [2.167, 1, 2.2, 1, 2.167, 1, 2.2, 1, 2.167, 1, 2.2, 1, 2.133, 0.7, 2.2, 1]}, {"time": 2.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6, "color": "ffffffff", "curve": [2.678, 1, 2.756, 1, 2.678, 1, 2.756, 1, 2.678, 1, 2.756, 1, 2.6, 0.65, 2.756, 0.5]}, {"time": 2.8333, "color": "ffffff80", "curve": [2.911, 1, 2.989, 1, 2.911, 1, 2.989, 1, 2.911, 1, 2.989, 1, 2.833, 0.15, 2.989, 0]}, {"time": 3.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_sparkle1"}]}, "spiral_sparkle16": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3667, "color": "ffffff00", "curve": [2.4, 1, 2.433, 1, 2.4, 1, 2.433, 1, 2.4, 1, 2.433, 1, 2.367, 0.7, 2.433, 1]}, {"time": 2.4667, "color": "ffffffff", "curve": [2.556, 1, 2.411, 1, 2.556, 1, 2.411, 1, 2.556, 1, 2.411, 1, 2.556, 1, 2.411, 0]}, {"time": 2.5667, "color": "ffffff00", "curve": [2.6, 1, 2.633, 1, 2.6, 1, 2.633, 1, 2.6, 1, 2.633, 1, 2.567, 0.7, 2.633, 1]}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8333, "color": "ffffffff", "curve": [2.922, 1, 2.956, 1, 2.922, 1, 2.956, 1, 2.922, 1, 2.956, 1, 2.833, 0.3, 2.956, 0]}, {"time": 3.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_sparkle1"}, {"time": 3.1, "name": "spiral_sparkle1"}]}, "spiral_sparkle17": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4, "color": "ffffff00", "curve": [2.433, 1, 2.467, 1, 2.433, 1, 2.467, 1, 2.433, 1, 2.467, 1, 2.4, 0.7, 2.467, 1]}, {"time": 2.5, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7, "color": "ffffffff", "curve": [2.811, 1, 2.922, 1, 2.811, 1, 2.922, 1, 2.811, 1, 2.922, 1, 2.7, 0.3, 2.922, 0]}, {"time": 3.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_sparkle1"}]}, "spiral_sparkle18": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2, "color": "ffffff00", "curve": [2.233, 1, 2.267, 1, 2.233, 1, 2.267, 1, 2.233, 1, 2.267, 1, 2.2, 0.7, 2.267, 1]}, {"time": 2.3, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6333, "color": "ffffffff", "curve": [2.756, 1, 2.878, 1, 2.756, 1, 2.878, 1, 2.756, 1, 2.878, 1, 2.633, 0.3, 2.878, 0]}, {"time": 3, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_sparkle1"}]}, "spiral_sparkle19": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4, "color": "ffffff00", "curve": [2.433, 1, 2.467, 1, 2.433, 1, 2.467, 1, 2.433, 1, 2.467, 1, 2.4, 0.7, 2.467, 1]}, {"time": 2.5, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7, "color": "ffffffff", "curve": [2.833, 1, 2.967, 1, 2.833, 1, 2.967, 1, 2.833, 1, 2.967, 1, 2.7, 0.3, 2.967, 0]}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_sparkle1"}]}, "spiral_sparkle20": {"rgba": [{"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3, "color": "ffffff00", "curve": [2.333, 1, 2.367, 1, 2.333, 1, 2.367, 1, 2.333, 1, 2.367, 1, 2.3, 0.7, 2.367, 1]}, {"time": 2.4, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7333, "color": "ffffffff", "curve": [2.856, 1, 2.978, 1, 2.856, 1, 2.978, 1, 2.856, 1, 2.978, 1, 2.733, 0.3, 2.978, 0]}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 2.1, "name": "spiral_sparkle1"}]}, "unit_h4_body": {"attachment": [{}]}, "unit_h4_hairB": {"attachment": [{}]}, "unit_h4_head1": {"attachment": [{}]}, "unit_h4_spellbook": {"attachment": [{}]}}, "bones": {"hero1": {"translate": [{"x": 68.15}], "scale": [{"curve": [0.222, 1, 0.444, 1, 0.222, 1, 0.444, 0.97]}, {"time": 0.6667, "y": 0.97, "curve": [0.889, 1, 1.111, 1, 0.889, 0.97, 1.111, 1]}, {"time": 1.3333, "curve": "stepped"}, {"time": 4, "curve": [4.222, 1, 4.444, 1, 4.222, 1, 4.444, 0.97]}, {"time": 4.6667, "y": 0.97, "curve": [4.889, 1, 5.111, 1, 4.889, 0.97, 5.111, 1]}, {"time": 5.3333}]}, "hero1_head": {"rotate": [{"value": 0.61, "curve": [0.222, 0.61, 0.444, -1.12]}, {"time": 0.6667, "value": -1.12, "curve": [0.889, -1.12, 1.313, 0]}, {"time": 1.3333, "curve": [1.444, 0, 1.556, 2.63]}, {"time": 1.6667, "value": 2.63, "curve": "stepped"}, {"time": 1.8333, "value": 2.63, "curve": [1.856, 2.63, 1.878, -3.68]}, {"time": 1.9, "value": -3.68, "curve": [1.922, -3.68, 1.944, -3.33]}, {"time": 1.9667, "value": -3.33, "curve": [2, -3.33, 2.033, -3.68]}, {"time": 2.0667, "value": -3.68, "curve": [2.267, -3.68, 2.467, 0.61]}, {"time": 2.6667, "value": 0.61, "curve": [2.889, 0.61, 3.111, -1.12]}, {"time": 3.3333, "value": -1.12, "curve": [3.556, -1.12, 3.778, 0.61]}, {"time": 4, "value": 0.61, "curve": [4.222, 0.61, 4.444, -1.12]}, {"time": 4.6667, "value": -1.12, "curve": [4.889, -1.12, 5.111, 0.61]}, {"time": 5.3333, "value": 0.61}], "translate": [{"x": 1, "curve": [0.222, 1, 0.444, -1.06, 0.222, 0, 0.444, 0]}, {"time": 0.6667, "x": -1.06, "curve": [0.889, -1.06, 1.111, 1, 0.889, 0, 1.111, 0]}, {"time": 1.3333, "x": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "curve": [2.889, 1, 3.111, -1.06, 2.889, 0, 3.111, 0]}, {"time": 3.3333, "x": -1.06, "curve": [3.556, -1.06, 3.778, 1, 3.556, 0, 3.778, 0]}, {"time": 4, "x": 1, "curve": [4.222, 1, 4.444, -1.06, 4.222, 0, 4.444, 0]}, {"time": 4.6667, "x": -1.06, "curve": [4.889, -1.06, 5.111, 1, 4.889, 0, 5.111, 0]}, {"time": 5.3333, "x": 1}], "scale": [{"curve": [0.222, 1, 0.444, 1.029, 0.222, 1, 0.444, 1]}, {"time": 0.6667, "x": 1.029, "curve": [0.889, 1.029, 1.111, 1, 0.889, 1, 1.111, 1]}, {"time": 1.3333, "curve": "stepped"}, {"time": 2.6667, "curve": [2.889, 1, 3.111, 1.029, 2.889, 1, 3.111, 1]}, {"time": 3.3333, "x": 1.029, "curve": [3.556, 1.029, 3.778, 1, 3.556, 1, 3.778, 1]}, {"time": 4, "curve": [4.222, 1, 4.444, 1.029, 4.222, 1, 4.444, 1]}, {"time": 4.6667, "x": 1.029, "curve": [4.889, 1.029, 5.111, 1, 4.889, 1, 5.111, 1]}, {"time": 5.3333}]}, "hero1_sword": {"rotate": [{"curve": [0.222, 0, 0.444, -3.62]}, {"time": 0.6667, "value": -3.62, "curve": [0.889, -3.62, 1.111, 0]}, {"time": 1.3333, "curve": [1.378, 0, 1.422, 31.95]}, {"time": 1.4667, "value": 31.95, "curve": [1.5, 31.95, 1.533, 102.86]}, {"time": 1.5667, "value": 102.86, "curve": "stepped"}, {"time": 1.8333, "value": 102.86, "curve": [1.856, 102.86, 1.878, -32.96]}, {"time": 1.9, "value": -32.96, "curve": [1.922, -32.96, 1.944, -28.96]}, {"time": 1.9667, "value": -28.96, "curve": [2, -28.96, 2.033, -32.96]}, {"time": 2.0667, "value": -32.96, "curve": [2.1, -32.96, 2.133, -32.31]}, {"time": 2.1667, "value": -32.31, "curve": [2.211, -32.31, 2.256, -32.96]}, {"time": 2.3, "value": -32.96, "curve": [2.422, -32.96, 2.544, 0]}, {"time": 2.6667, "curve": [2.889, 0, 3.111, -3.62]}, {"time": 3.3333, "value": -3.62, "curve": [3.556, -3.62, 3.778, 0]}, {"time": 4, "curve": [4.222, 0, 4.444, -3.62]}, {"time": 4.6667, "value": -3.62, "curve": [4.889, -3.62, 5.111, 0]}, {"time": 5.3333}], "translate": [{"curve": [0.222, 0, 0.444, -1.35, 0.222, 0, 0.444, 0]}, {"time": 0.6667, "x": -1.35, "curve": [0.889, -1.35, 1.111, 0, 0.889, 0, 1.111, 0]}, {"time": 1.3333, "curve": [1.378, 0, 1.422, 57.32, 1.378, 0, 1.422, 13.38]}, {"time": 1.4667, "x": 57.32, "y": 13.38, "curve": [1.5, 57.32, 1.533, 74.79, 1.5, 13.38, 1.533, 28.86]}, {"time": 1.5667, "x": 74.79, "y": 28.86, "curve": "stepped"}, {"time": 1.8333, "x": 74.79, "y": 28.86, "curve": [1.844, 74.79, 1.856, 45.81, 1.844, 28.86, 1.856, 11.83]}, {"time": 1.8667, "x": 45.81, "y": 11.83, "curve": [1.878, 45.81, 1.889, 16.17, 1.878, 11.83, 1.889, -20.47]}, {"time": 1.9, "x": 16.17, "y": -20.47, "curve": [1.922, 16.17, 1.944, 20.1, 1.922, -20.47, 1.944, -20.24]}, {"time": 1.9667, "x": 20.1, "y": -20.24, "curve": [2, 20.1, 2.033, 16.17, 2, -20.24, 2.033, -20.47]}, {"time": 2.0667, "x": 16.17, "y": -20.47, "curve": [2.1, 16.17, 2.133, 17.16, 2.1, -20.47, 2.133, -20.4]}, {"time": 2.1667, "x": 17.16, "y": -20.4, "curve": [2.211, 17.16, 2.256, 16.17, 2.211, -20.4, 2.256, -20.47]}, {"time": 2.3, "x": 16.17, "y": -20.47, "curve": [2.422, 16.17, 2.544, 0, 2.422, -20.47, 2.544, 0]}, {"time": 2.6667, "curve": [2.889, 0, 3.111, -1.35, 2.889, 0, 3.111, 0]}, {"time": 3.3333, "x": -1.35, "curve": [3.556, -1.35, 3.778, 0, 3.556, 0, 3.778, 0]}, {"time": 4, "curve": [4.222, 0, 4.444, -1.35, 4.222, 0, 4.444, 0]}, {"time": 4.6667, "x": -1.35, "curve": [4.889, -1.35, 5.111, 0, 4.889, 0, 5.111, 0]}, {"time": 5.3333}]}, "hero1_shield": {"rotate": [{"curve": [0.222, 0, 0.444, 0.19]}, {"time": 0.6667, "value": 0.19, "curve": [0.889, 0.19, 1.111, 0]}, {"time": 1.3333, "curve": [1.411, 0, 1.489, 1.77]}, {"time": 1.5667, "value": 1.77, "curve": "stepped"}, {"time": 1.6667, "value": 1.77, "curve": "stepped"}, {"time": 1.8333, "value": 1.77, "curve": [1.856, 1.77, 1.878, 5.18]}, {"time": 1.9, "value": 5.18, "curve": [2.156, 5.18, 2.411, 0]}, {"time": 2.6667, "curve": [2.889, 0, 3.111, 0.19]}, {"time": 3.3333, "value": 0.19, "curve": [3.556, 0.19, 3.778, 0]}, {"time": 4, "curve": [4.222, 0, 4.444, 0.19]}, {"time": 4.6667, "value": 0.19, "curve": [4.889, 0.19, 5.111, 0]}, {"time": 5.3333}], "translate": [{"curve": [0.222, 0, 0.444, -0.7, 0.222, 0, 0.444, 2.48]}, {"time": 0.6667, "x": -0.7, "y": 2.48, "curve": [0.889, -0.7, 1.111, 0, 0.889, 2.48, 1.111, 0]}, {"time": 1.3333, "curve": [1.411, 0, 1.489, -0.4, 1.411, 0, 1.489, -12.71]}, {"time": 1.5667, "x": -0.4, "y": -12.71, "curve": "stepped"}, {"time": 1.6667, "x": -0.4, "y": -12.71, "curve": "stepped"}, {"time": 1.8333, "x": -0.4, "y": -12.71, "curve": [1.856, -0.4, 1.878, -2.63, 1.856, -12.71, 1.878, 2.95]}, {"time": 1.9, "x": -2.63, "y": 2.95, "curve": [2.156, -2.63, 2.411, 0, 2.156, 2.95, 2.411, 0]}, {"time": 2.6667, "curve": [2.889, 0, 3.111, -0.7, 2.889, 0, 3.111, 2.48]}, {"time": 3.3333, "x": -0.7, "y": 2.48, "curve": [3.556, -0.7, 3.778, 0, 3.556, 2.48, 3.778, 0]}, {"time": 4, "curve": [4.222, 0, 4.444, -0.7, 4.222, 0, 4.444, 2.48]}, {"time": 4.6667, "x": -0.7, "y": 2.48, "curve": [4.889, -0.7, 5.111, 0, 4.889, 2.48, 5.111, 0]}, {"time": 5.3333}]}, "undead_knight_body": {"scale": [{"y": 0.985, "curve": [0.113, 1, 0.223, 1, 0.113, 0.992, 0.223, 1]}, {"time": 0.3333, "curve": [0.556, 1, 0.778, 1, 0.556, 1, 0.778, 0.97]}, {"time": 1, "y": 0.97, "curve": [1.112, 1, 1.222, 1, 1.112, 0.97, 1.222, 0.978]}, {"time": 1.3333, "y": 0.985, "curve": [1.446, 1, 1.556, 1, 1.446, 0.992, 1.556, 1]}, {"time": 1.6667, "curve": [1.889, 1, 2.111, 1, 1.889, 1, 2.111, 0.97]}, {"time": 2.3333, "y": 0.97, "curve": [2.445, 1, 2.556, 1, 2.445, 0.97, 2.556, 0.978]}, {"time": 2.6667, "y": 0.985, "curve": [2.779, 1, 2.89, 1, 2.779, 0.992, 2.89, 1]}, {"time": 3, "curve": [3.222, 1, 3.444, 1, 3.222, 1, 3.444, 0.97]}, {"time": 3.6667, "y": 0.97, "curve": [3.779, 1, 3.889, 1, 3.779, 0.97, 3.889, 1]}, {"time": 4, "curve": [4.222, 1, 4.444, 1, 4.222, 1, 4.444, 0.97]}, {"time": 4.6667, "y": 0.97, "curve": [4.889, 1, 5.111, 1, 4.889, 0.97, 5.111, 0.97]}, {"time": 5.3333, "y": 0.985}]}, "undead_knight_sword": {"rotate": [{"value": 21.83, "curve": [0.113, 20.92, 0.223, 20]}, {"time": 0.3333, "value": 20, "curve": [0.556, 20, 0.778, 23.65]}, {"time": 1, "value": 23.65, "curve": [1.112, 23.65, 1.222, 22.72]}, {"time": 1.3333, "value": 21.83, "curve": [1.446, 20.92, 1.556, 20]}, {"time": 1.6667, "value": 20, "curve": [1.889, 20, 2.111, 23.65]}, {"time": 2.3333, "value": 23.65, "curve": [2.445, 23.65, 2.556, 22.72]}, {"time": 2.6667, "value": 21.83, "curve": [2.779, 20.92, 2.89, 20]}, {"time": 3, "value": 20, "curve": [3.222, 20, 3.444, 23.65]}, {"time": 3.6667, "value": 23.65, "curve": [3.779, 23.65, 3.889, 20]}, {"time": 4, "value": 20, "curve": [4.222, 20, 4.444, 23.65]}, {"time": 4.6667, "value": 23.65, "curve": [4.889, 23.65, 5.111, 23.62]}, {"time": 5.3333, "value": 21.83}], "translate": [{"x": 33.79, "y": -1.93, "curve": [0.113, 33.79, 0.223, 33.79, 0.113, -1.2, 0.223, -0.45]}, {"time": 0.3333, "x": 33.79, "y": -0.45, "curve": [0.556, 33.79, 0.778, 33.79, 0.556, -0.45, 0.778, -3.41]}, {"time": 1, "x": 33.79, "y": -3.41, "curve": [1.112, 33.79, 1.222, 33.79, 1.112, -3.41, 1.222, -2.66]}, {"time": 1.3333, "x": 33.79, "y": -1.93, "curve": [1.446, 33.79, 1.556, 33.79, 1.446, -1.2, 1.556, -0.45]}, {"time": 1.6667, "x": 33.79, "y": -0.45, "curve": [1.889, 33.79, 2.111, 33.79, 1.889, -0.45, 2.111, -3.41]}, {"time": 2.3333, "x": 33.79, "y": -3.41, "curve": [2.445, 33.79, 2.556, 33.79, 2.445, -3.41, 2.556, -2.66]}, {"time": 2.6667, "x": 33.79, "y": -1.93, "curve": [2.779, 33.79, 2.89, 33.79, 2.779, -1.2, 2.89, -0.45]}, {"time": 3, "x": 33.79, "y": -0.45, "curve": [3.222, 33.79, 3.444, 33.79, 3.222, -0.45, 3.444, -3.41]}, {"time": 3.6667, "x": 33.79, "y": -3.41, "curve": [3.779, 33.79, 3.889, 33.79, 3.779, -3.41, 3.889, -0.45]}, {"time": 4, "x": 33.79, "y": -0.45, "curve": [4.222, 33.79, 4.444, 33.79, 4.222, -0.45, 4.444, -3.41]}, {"time": 4.6667, "x": 33.79, "y": -3.41, "curve": [4.889, 33.79, 5.111, 33.79, 4.889, -3.41, 5.111, -3.38]}, {"time": 5.3333, "x": 33.79, "y": -1.93}]}, "undead_knight_body2": {"scale": [{"y": 0.985, "curve": [0.113, 1, 0.223, 1, 0.113, 0.992, 0.223, 1]}, {"time": 0.3333, "curve": [0.556, 1, 0.778, 1, 0.556, 1, 0.778, 0.97]}, {"time": 1, "y": 0.97, "curve": [1.112, 1, 1.222, 1, 1.112, 0.97, 1.222, 0.978]}, {"time": 1.3333, "y": 0.985, "curve": [1.446, 1, 1.556, 1, 1.446, 0.992, 1.556, 1]}, {"time": 1.6667, "curve": [1.889, 1, 2.111, 1, 1.889, 1, 2.111, 0.97]}, {"time": 2.3333, "y": 0.97, "curve": [2.445, 1, 2.556, 1, 2.445, 0.97, 2.556, 0.978]}, {"time": 2.6667, "y": 0.985, "curve": [2.779, 1, 2.89, 1, 2.779, 0.992, 2.89, 1]}, {"time": 3, "curve": [3.222, 1, 3.444, 1, 3.222, 1, 3.444, 0.97]}, {"time": 3.6667, "y": 0.97, "curve": [3.779, 1, 3.889, 1, 3.779, 0.97, 3.889, 1]}, {"time": 4, "curve": [4.222, 1, 4.444, 1, 4.222, 1, 4.444, 0.97]}, {"time": 4.6667, "y": 0.97, "curve": [4.889, 1, 5.111, 1, 4.889, 0.97, 5.111, 0.97]}, {"time": 5.3333, "y": 0.985}]}, "undead_knight_sword2": {"rotate": [{"value": 21.83, "curve": [0.113, 20.92, 0.223, 20]}, {"time": 0.3333, "value": 20, "curve": [0.556, 20, 0.778, 23.65]}, {"time": 1, "value": 23.65, "curve": [1.112, 23.65, 1.222, 22.72]}, {"time": 1.3333, "value": 21.83, "curve": [1.446, 20.92, 1.556, 20]}, {"time": 1.6667, "value": 20, "curve": [1.889, 20, 2.111, 23.65]}, {"time": 2.3333, "value": 23.65, "curve": [2.445, 23.65, 2.556, 22.72]}, {"time": 2.6667, "value": 21.83, "curve": [2.779, 20.92, 2.89, 20]}, {"time": 3, "value": 20, "curve": [3.222, 20, 3.444, 23.65]}, {"time": 3.6667, "value": 23.65, "curve": [3.779, 23.65, 3.889, 20]}, {"time": 4, "value": 20, "curve": [4.222, 20, 4.444, 23.65]}, {"time": 4.6667, "value": 23.65, "curve": [4.889, 23.65, 5.111, 23.62]}, {"time": 5.3333, "value": 21.83}], "translate": [{"x": 33.79, "y": -1.93, "curve": [0.113, 33.79, 0.223, 33.79, 0.113, -1.2, 0.223, -0.45]}, {"time": 0.3333, "x": 33.79, "y": -0.45, "curve": [0.556, 33.79, 0.778, 33.79, 0.556, -0.45, 0.778, -3.41]}, {"time": 1, "x": 33.79, "y": -3.41, "curve": [1.112, 33.79, 1.222, 33.79, 1.112, -3.41, 1.222, -2.66]}, {"time": 1.3333, "x": 33.79, "y": -1.93, "curve": [1.446, 33.79, 1.556, 33.79, 1.446, -1.2, 1.556, -0.45]}, {"time": 1.6667, "x": 33.79, "y": -0.45, "curve": [1.889, 33.79, 2.111, 33.79, 1.889, -0.45, 2.111, -3.41]}, {"time": 2.3333, "x": 33.79, "y": -3.41, "curve": [2.445, 33.79, 2.556, 33.79, 2.445, -3.41, 2.556, -2.66]}, {"time": 2.6667, "x": 33.79, "y": -1.93, "curve": [2.779, 33.79, 2.89, 33.79, 2.779, -1.2, 2.89, -0.45]}, {"time": 3, "x": 33.79, "y": -0.45, "curve": [3.222, 33.79, 3.444, 33.79, 3.222, -0.45, 3.444, -3.41]}, {"time": 3.6667, "x": 33.79, "y": -3.41, "curve": [3.779, 33.79, 3.889, 33.79, 3.779, -3.41, 3.889, -0.45]}, {"time": 4, "x": 33.79, "y": -0.45, "curve": [4.222, 33.79, 4.444, 33.79, 4.222, -0.45, 4.444, -3.41]}, {"time": 4.6667, "x": 33.79, "y": -3.41, "curve": [4.889, 33.79, 5.111, 33.79, 4.889, -3.41, 5.111, -3.38]}, {"time": 5.3333, "x": 33.79, "y": -1.93}]}, "undead_knight_body3": {"scale": [{"y": 0.985, "curve": [0.113, 1, 0.223, 1, 0.113, 0.992, 0.223, 1]}, {"time": 0.3333, "curve": [0.556, 1, 0.778, 1, 0.556, 1, 0.778, 0.97]}, {"time": 1, "y": 0.97, "curve": [1.112, 1, 1.222, 1, 1.112, 0.97, 1.222, 0.978]}, {"time": 1.3333, "y": 0.985, "curve": [1.446, 1, 1.556, 1, 1.446, 0.992, 1.556, 1]}, {"time": 1.6667, "curve": [1.889, 1, 2.111, 1, 1.889, 1, 2.111, 0.97]}, {"time": 2.3333, "y": 0.97, "curve": [2.445, 1, 2.556, 1, 2.445, 0.97, 2.556, 0.978]}, {"time": 2.6667, "y": 0.985, "curve": [2.779, 1, 2.89, 1, 2.779, 0.992, 2.89, 1]}, {"time": 3, "curve": [3.222, 1, 3.444, 1, 3.222, 1, 3.444, 0.97]}, {"time": 3.6667, "y": 0.97, "curve": [3.779, 1, 3.889, 1, 3.779, 0.97, 3.889, 1]}, {"time": 4, "curve": [4.222, 1, 4.444, 1, 4.222, 1, 4.444, 0.97]}, {"time": 4.6667, "y": 0.97, "curve": [4.889, 1, 5.111, 1, 4.889, 0.97, 5.111, 0.97]}, {"time": 5.3333, "y": 0.985}]}, "undead_knight_sword3": {"rotate": [{"value": 21.83, "curve": [0.113, 20.92, 0.223, 20]}, {"time": 0.3333, "value": 20, "curve": [0.556, 20, 0.778, 23.65]}, {"time": 1, "value": 23.65, "curve": [1.112, 23.65, 1.222, 22.72]}, {"time": 1.3333, "value": 21.83, "curve": [1.446, 20.92, 1.556, 20]}, {"time": 1.6667, "value": 20, "curve": [1.889, 20, 2.111, 23.65]}, {"time": 2.3333, "value": 23.65, "curve": [2.445, 23.65, 2.556, 22.72]}, {"time": 2.6667, "value": 21.83, "curve": [2.779, 20.92, 2.89, 20]}, {"time": 3, "value": 20, "curve": [3.222, 20, 3.444, 23.65]}, {"time": 3.6667, "value": 23.65, "curve": [3.779, 23.65, 3.889, 20]}, {"time": 4, "value": 20, "curve": [4.222, 20, 4.444, 23.65]}, {"time": 4.6667, "value": 23.65, "curve": [4.889, 23.65, 5.111, 23.62]}, {"time": 5.3333, "value": 21.83}], "translate": [{"x": 33.79, "y": -1.93, "curve": [0.113, 33.79, 0.223, 33.79, 0.113, -1.2, 0.223, -0.45]}, {"time": 0.3333, "x": 33.79, "y": -0.45, "curve": [0.556, 33.79, 0.778, 33.79, 0.556, -0.45, 0.778, -3.41]}, {"time": 1, "x": 33.79, "y": -3.41, "curve": [1.112, 33.79, 1.222, 33.79, 1.112, -3.41, 1.222, -2.66]}, {"time": 1.3333, "x": 33.79, "y": -1.93, "curve": [1.446, 33.79, 1.556, 33.79, 1.446, -1.2, 1.556, -0.45]}, {"time": 1.6667, "x": 33.79, "y": -0.45, "curve": [1.889, 33.79, 2.111, 33.79, 1.889, -0.45, 2.111, -3.41]}, {"time": 2.3333, "x": 33.79, "y": -3.41, "curve": [2.445, 33.79, 2.556, 33.79, 2.445, -3.41, 2.556, -2.66]}, {"time": 2.6667, "x": 33.79, "y": -1.93, "curve": [2.779, 33.79, 2.89, 33.79, 2.779, -1.2, 2.89, -0.45]}, {"time": 3, "x": 33.79, "y": -0.45, "curve": [3.222, 33.79, 3.444, 33.79, 3.222, -0.45, 3.444, -3.41]}, {"time": 3.6667, "x": 33.79, "y": -3.41, "curve": [3.779, 33.79, 3.889, 33.79, 3.779, -3.41, 3.889, -0.45]}, {"time": 4, "x": 33.79, "y": -0.45, "curve": [4.222, 33.79, 4.444, 33.79, 4.222, -0.45, 4.444, -3.41]}, {"time": 4.6667, "x": 33.79, "y": -3.41, "curve": [4.889, 33.79, 5.111, 33.79, 4.889, -3.41, 5.111, -3.38]}, {"time": 5.3333, "x": 33.79, "y": -1.93}]}, "knight": {"translate": [{"x": 71.88, "y": -13.04}]}, "knight_body": {"scale": [{"time": 1.3333, "curve": [1.571, 1, 1.862, 1, 1.571, 1, 1.862, 0.97]}, {"time": 2.1, "y": 0.97, "curve": [2.311, 1, 2.556, 1, 2.311, 0.97, 2.556, 1]}, {"time": 2.7667, "curve": [2.777, 1, 3.129, 1, 2.777, 1, 3.129, 0.97]}, {"time": 3.3333, "y": 0.97, "curve": [3.556, 1, 3.778, 1, 3.556, 0.97, 3.778, 1]}, {"time": 4, "curve": [4.222, 1, 4.444, 1, 4.222, 1, 4.444, 0.97]}, {"time": 4.6667, "y": 0.97, "curve": [4.889, 1, 5.111, 1, 4.889, 0.97, 5.111, 1]}, {"time": 5.3333}]}, "knight_sword": {"rotate": [{"time": 1.3333, "value": 20, "curve": [1.571, 20, 1.862, 23.65]}, {"time": 2.1, "value": 23.65, "curve": [2.311, 23.65, 2.556, 20]}, {"time": 2.7667, "value": 20, "curve": [2.777, 20, 3.129, 23.65]}, {"time": 3.3333, "value": 23.65, "curve": [3.556, 23.65, 3.778, 20]}, {"time": 4, "value": 20, "curve": [4.222, 20, 4.444, 23.65]}, {"time": 4.6667, "value": 23.65, "curve": [4.889, 23.65, 5.111, 20]}, {"time": 5.3333, "value": 20}], "translate": [{"time": 1.3333, "x": 33.79, "y": -0.45, "curve": [1.571, 33.79, 1.862, 33.79, 1.571, -0.45, 1.862, -3.41]}, {"time": 2.1, "x": 33.79, "y": -3.41, "curve": [2.311, 33.79, 2.556, 33.79, 2.311, -3.41, 2.556, -0.45]}, {"time": 2.7667, "x": 33.79, "y": -0.45, "curve": [2.777, 33.79, 3.129, 33.79, 2.777, -0.45, 3.129, -3.41]}, {"time": 3.3333, "x": 33.79, "y": -3.41, "curve": [3.556, 33.79, 3.778, 33.79, 3.556, -3.41, 3.778, -0.45]}, {"time": 4, "x": 33.79, "y": -0.45, "curve": [4.222, 33.79, 4.444, 33.79, 4.222, -0.45, 4.444, -3.41]}, {"time": 4.6667, "x": 33.79, "y": -3.41, "curve": [4.889, 33.79, 5.111, 33.79, 4.889, -3.41, 5.111, -0.45]}, {"time": 5.3333, "x": 33.79, "y": -0.45}]}, "hero1_body": {"rotate": [{"time": 1.3667, "curve": [1.478, 0, 1.589, 2.63]}, {"time": 1.7, "value": 2.63, "curve": "stepped"}, {"time": 1.8667, "value": 2.63, "curve": [1.889, 2.63, 1.911, -3.68]}, {"time": 1.9333, "value": -3.68, "curve": [1.956, -3.68, 1.978, -3.33]}, {"time": 2, "value": -3.33, "curve": [2.033, -3.33, 2.067, -3.68]}, {"time": 2.1, "value": -3.68, "curve": "stepped"}, {"time": 2.3333, "value": -3.68, "curve": [2.522, -3.68, 2.478, 0]}, {"time": 2.6667}], "scale": [{"time": 1.3667, "curve": [1.478, 1, 1.589, 1.02, 1.478, 1, 1.589, 1]}, {"time": 1.7, "x": 1.02, "curve": "stepped"}, {"time": 1.8667, "x": 1.02, "curve": [1.889, 1.02, 1.911, 1, 1.889, 1, 1.911, 1]}, {"time": 1.9333}]}, "hero1_sword_eff": {"rotate": [{"time": 1.9333, "value": -6.79}], "translate": [{"time": 1.9333, "x": 146.44, "y": 21.5}], "scale": [{"time": 1.9333, "x": 1.261, "y": 1.448}]}, "knight_regen": {"translate": [{"x": 71.88, "y": -13.04}], "scale": [{"time": 2.1, "x": 1.05, "y": 1.05}, {"time": 2.2667, "x": 1.2, "y": 1.3}]}, "spiral_eff": {"scale": [{"time": 2.1}, {"time": 2.2667, "x": 1.15}]}, "spiral_circle1": {"rotate": [{"time": 2.1, "value": -26.31, "curve": [2.1, 226.32, 2.656, 360]}, {"time": 2.9333, "value": 360, "curve": [2.933, 107.37, 2.956, 0]}, {"time": 2.9667, "curve": "stepped"}, {"time": 3.1}], "scale": [{"time": 2.1, "x": 0.3, "y": 0.3, "curve": [2.1, 1.037, 2.656, 1.35, 2.1, 1.037, 2.656, 1.35]}, {"time": 2.9333, "x": 1.35, "y": 1.35, "curve": [2.933, 0.613, 2.956, 0.3, 2.933, 0.613, 2.956, 0.3]}, {"time": 2.9667, "x": 0.3, "y": 0.3, "curve": "stepped"}, {"time": 3.1, "x": 0.3, "y": 0.3}]}, "spiral_circle2": {"rotate": [{"time": 2.1, "value": 90, "curve": [2.1, 153.16, 2.433, 90]}, {"time": 2.6, "value": 90}], "translate": [{"time": 2.1, "y": 166.97, "curve": [2.1, 0, 2.433, 0, 2.1, 370.51, 2.433, 290.05]}, {"time": 2.6, "y": 290.05}], "scale": [{"time": 2.1, "x": 0.979, "y": 0.994, "curve": [2.1, 1.036, 2.211, 1.061, 2.1, 1.05, 2.211, 1.073]}, {"time": 2.2667, "x": 1.061, "y": 1.073}]}, "spiral_circle3": {"rotate": [{"time": 2.1, "value": 80.81}, {"time": 2.2333, "curve": "stepped"}, {"time": 2.3, "curve": [2.3, 63.16, 2.656, 90]}, {"time": 2.8667, "value": 90, "curve": "stepped"}, {"time": 2.9333, "value": 90}], "translate": [{"time": 2.1, "y": 152.53}, {"time": 2.2333, "curve": "stepped"}, {"time": 2.3, "curve": [2.511, 0, 2.656, 0, 2.3, 127.97, 2.656, 166.97]}, {"time": 2.8667, "y": 166.97}, {"time": 2.9333, "y": 182.35}], "scale": [{"time": 2.1, "x": 1.235, "y": 1.226, "curve": "stepped"}, {"time": 2.3, "x": 1.235, "y": 1.226, "curve": [2.3, 1.277, 2.411, 1.295, 2.3, 1.259, 2.411, 1.272]}, {"time": 2.4667, "x": 1.295, "y": 1.272}]}, "spiral_eff1": {"rotate": [{"time": 2.1, "value": 53.51, "curve": [2.1, 369.3, 2.633, 450]}, {"time": 2.9, "value": 450, "curve": [2.9, 134.21, 2.922, 0]}, {"time": 2.9333, "curve": "stepped"}, {"time": 3.1}], "scale": [{"time": 2.1, "x": 0.1, "y": 0.1, "curve": [2.1, 0.696, 2.633, 0.95, 2.1, 0.696, 2.633, 0.95]}, {"time": 2.9, "x": 0.95, "y": 0.95}]}, "spiral_sparkle1": {"translate": [{"time": 2.1, "x": -9.25, "y": -5.63, "curve": "stepped"}, {"time": 2.2, "x": -9.25, "y": -5.63, "curve": "stepped"}, {"time": 2.2333, "x": -9.25, "y": -5.63, "curve": [2.522, -9.25, 2.7, -9.25, 2.522, -5.63, 2.7, -36.4]}, {"time": 2.9333, "x": -9.25, "y": -36.4, "curve": [2.989, -9.25, 2.811, -9.25, 2.989, -36.4, 2.811, -5.63]}, {"time": 3.1, "x": -9.25, "y": -5.63}], "scale": [{"time": 2.1, "x": 1.15, "y": 1.15}]}, "spiral_sparkle2": {"translate": [{"time": 2.1, "x": -35.55, "y": -1.06, "curve": "stepped"}, {"time": 2.9, "x": -35.55, "y": -1.06, "curve": [2.967, -35.55, 2.922, -35.55, 2.967, -1.06, 2.922, -31.83]}, {"time": 2.9333, "x": -35.55, "y": -31.83, "curve": [2.989, -35.55, 3.033, -35.55, 2.989, -31.83, 3.033, -1.06]}, {"time": 3.1, "x": -35.55, "y": -1.06}], "scale": [{"time": 2.1, "x": 1.15, "y": 1.15}]}, "spiral_sparkle3": {"translate": [{"time": 2.1, "x": 35.87, "y": -9.27, "curve": "stepped"}, {"time": 2.2333, "x": 35.87, "y": -9.27, "curve": "stepped"}, {"time": 2.3333, "x": 35.87, "y": -9.27, "curve": [2.589, 35.87, 2.656, 35.87, 2.589, -9.27, 2.656, -40.03]}, {"time": 2.9333, "x": 35.87, "y": -40.03, "curve": [2.989, 35.87, 3.044, 35.87, 2.989, -40.03, 3.044, -9.27]}, {"time": 3.1, "x": 35.87, "y": -9.27}], "scale": [{"time": 2.1, "x": 1.15, "y": 1.15}]}, "spiral_sparkle4": {"translate": [{"time": 2.1, "x": -52.21, "y": 9.88, "curve": "stepped"}, {"time": 2.2333, "x": -52.21, "y": 9.88, "curve": "stepped"}, {"time": 2.4667, "x": -52.21, "y": 9.88, "curve": [2.678, -52.21, 2.656, -52.21, 2.678, 9.88, 2.656, -20.89]}, {"time": 2.9333, "x": -52.21, "y": -20.89, "curve": [2.989, -52.21, 3.044, -52.21, 2.989, -20.89, 3.044, 9.88]}, {"time": 3.1, "x": -52.21, "y": 9.88}], "scale": [{"time": 2.1, "x": 1.15, "y": 1.15, "curve": "stepped"}, {"time": 3.1, "x": 1.15, "y": 1.15}]}, "spiral_sparkle5": {"translate": [{"time": 2.1, "x": 54.53, "y": 2.44, "curve": "stepped"}, {"time": 2.1333, "x": 54.53, "y": 2.44, "curve": [2.444, 54.53, 2.667, 54.53, 2.444, 2.44, 2.667, -28.33]}, {"time": 2.9333, "x": 54.53, "y": -28.33, "curve": [2.978, 54.53, 2.756, 54.53, 2.978, -28.33, 2.756, 2.44]}, {"time": 3.0667, "x": 54.53, "y": 2.44, "curve": "stepped"}, {"time": 3.1, "x": 54.53, "y": 2.44}], "scale": [{"time": 2.1, "x": 1.15, "y": 1.15}]}, "spiral_sparkle6": {"translate": [{"time": 2.1, "x": -66.92, "y": -13.24, "curve": "stepped"}, {"time": 2.4, "x": -66.92, "y": -13.24, "curve": "stepped"}, {"time": 2.5667, "x": -66.92, "y": -13.24, "curve": [2.744, -66.92, 2.656, -66.92, 2.744, -13.24, 2.656, -44.01]}, {"time": 2.9333, "x": -66.92, "y": -44.01, "curve": [2.989, -66.92, 3.044, -66.92, 2.989, -44.01, 3.044, -13.24]}, {"time": 3.1, "x": -66.92, "y": -13.24}], "scale": [{"time": 2.1, "x": 1.15, "y": 1.15, "curve": "stepped"}, {"time": 3.1, "x": 1.15, "y": 1.15}]}, "spiral_sparkle7": {"translate": [{"time": 2.1, "x": 67.12, "y": -16.06, "curve": "stepped"}, {"time": 2.3333, "x": 67.12, "y": -16.06, "curve": "stepped"}, {"time": 2.4, "x": 67.12, "y": -16.06, "curve": [2.633, 67.12, 2.756, 67.12, 2.633, -16.06, 2.756, -46.83]}, {"time": 2.9333, "x": 67.12, "y": -46.83, "curve": [2.989, 67.12, 2.867, 67.12, 2.989, -46.83, 2.867, -16.06]}, {"time": 3.1, "x": 67.12, "y": -16.06}], "scale": [{"time": 2.1, "x": 1.15, "y": 1.15}]}, "spiral_sparkle8": {"translate": [{"time": 2.1, "x": -23.44, "y": -17.49, "curve": "stepped"}, {"time": 2.2, "x": -23.44, "y": -17.49, "curve": [2.467, -23.44, 2.689, -23.44, 2.467, -17.49, 2.689, -48.26]}, {"time": 2.9333, "x": -23.44, "y": -48.26, "curve": [2.956, -23.44, 2.733, -23.44, 2.956, -48.26, 2.733, -17.49]}, {"time": 3, "x": -23.44, "y": -17.49, "curve": "stepped"}, {"time": 3.1, "x": -23.44, "y": -17.49}], "scale": [{"time": 2.1, "x": 1.15, "y": 1.15}]}, "spiral_sparkle9": {"translate": [{"time": 2.1, "x": 23.16, "y": 7.73, "curve": "stepped"}, {"time": 2.4, "x": 23.16, "y": 7.73, "curve": [2.633, 23.16, 2.756, 23.16, 2.633, 7.73, 2.756, -23.04]}, {"time": 2.9333, "x": 23.16, "y": -23.04, "curve": [2.989, 23.16, 2.867, 23.16, 2.989, -23.04, 2.867, 7.73]}, {"time": 3.1, "x": 23.16, "y": 7.73}], "scale": [{"time": 2.1, "x": 1.15, "y": 1.15}]}, "spiral_sparkle10": {"translate": [{"time": 2.1, "x": 3.97, "y": -4.44, "curve": "stepped"}, {"time": 2.2, "x": 3.97, "y": -4.44, "curve": "stepped"}, {"time": 2.3, "x": 3.97, "y": -4.44, "curve": [2.567, 3.97, 2.722, 3.97, 2.567, -4.44, 2.722, -35.21]}, {"time": 2.9333, "x": 3.97, "y": -35.21, "curve": [2.989, 3.97, 2.833, 3.97, 2.989, -35.21, 2.833, -4.44]}, {"time": 3.1, "x": 3.97, "y": -4.44}], "scale": [{"time": 2.1, "x": 1.15, "y": 1.15}]}, "spiral_beam1": {"translate": [{"time": 2.7333, "curve": [2.778, 0, 2.822, 0, 2.778, 0, 2.867, 24.43]}, {"time": 2.8667, "y": 61.54}], "scale": [{"time": 2.1, "y": 0.839, "curve": [2.156, 1, 2.211, 1, 2.156, 0.839, 2.211, 0.94]}, {"time": 2.2667, "y": 0.94, "curve": "stepped"}, {"time": 2.7333, "y": 0.94, "curve": [2.778, 1, 2.867, 0.628, 2.778, 0.94, 2.867, 0.9]}, {"time": 2.8667, "x": 0.062, "y": 0.839}]}, "spiral_beam2": {"translate": [{"time": 2.7333, "curve": [2.778, 0, 2.822, 0, 2.778, 0, 2.867, 24.43]}, {"time": 2.8667, "y": 61.54}], "scale": [{"time": 2.1, "y": 0.805, "curve": [2.156, 1, 2.211, 1, 2.156, 0.805, 2.211, 0.902]}, {"time": 2.2667, "y": 0.902, "curve": "stepped"}, {"time": 2.7333, "y": 0.902, "curve": [2.778, 1, 2.867, 0.628, 2.778, 0.902, 2.867, 0.863]}, {"time": 2.8667, "x": 0.062, "y": 0.805}]}, "spiral_eff3": {"rotate": [{"time": 2.1, "value": 53.51, "curve": [2.1, 369.3, 2.633, 450]}, {"time": 2.9, "value": 450, "curve": [2.9, 134.21, 2.922, 0]}, {"time": 2.9333, "curve": "stepped"}, {"time": 3.1}], "scale": [{"time": 2.1, "x": 0.1, "y": 0.1, "curve": [2.1, 0.696, 2.633, 0.95, 2.1, 0.696, 2.633, 0.95]}, {"time": 2.9, "x": 0.95, "y": 0.95}]}, "spiral_circle4": {"rotate": [{"time": 2.1, "value": -26.31, "curve": [2.1, 226.32, 2.656, 360]}, {"time": 2.9333, "value": 360, "curve": [2.933, 107.37, 2.956, 0]}, {"time": 2.9667, "curve": "stepped"}, {"time": 3.1}], "scale": [{"time": 2.1, "x": 0.3, "y": 0.3, "curve": [2.1, 1.037, 2.656, 1.35, 2.1, 1.037, 2.656, 1.35]}, {"time": 2.9333, "x": 1.35, "y": 1.35, "curve": [2.933, 0.613, 2.956, 0.3, 2.933, 0.613, 2.956, 0.3]}, {"time": 2.9667, "x": 0.3, "y": 0.3, "curve": "stepped"}, {"time": 3.1, "x": 0.3, "y": 0.3}]}, "spiral_beam3": {"translate": [{"time": 2.7333, "curve": [2.778, 0, 2.822, 0, 2.778, 0, 2.867, 24.43]}, {"time": 2.8667, "y": 61.54}], "scale": [{"time": 2.1, "y": 0.839, "curve": [2.156, 1, 2.211, 1, 2.156, 0.839, 2.211, 0.94]}, {"time": 2.2667, "y": 0.94, "curve": "stepped"}, {"time": 2.7333, "y": 0.94, "curve": [2.778, 1, 2.867, 0.628, 2.778, 0.94, 2.867, 0.9]}, {"time": 2.8667, "x": 0.062, "y": 0.839}]}, "spiral_circle5": {"rotate": [{"time": 2.1, "value": 90, "curve": [2.1, 153.16, 2.433, 90]}, {"time": 2.6, "value": 90}], "translate": [{"time": 2.1, "y": 166.97, "curve": [2.1, 0, 2.433, 0, 2.1, 370.51, 2.433, 290.05]}, {"time": 2.6, "y": 290.05}], "scale": [{"time": 2.1, "x": 0.979, "y": 0.994, "curve": [2.1, 1.036, 2.211, 1.061, 2.1, 1.05, 2.211, 1.073]}, {"time": 2.2667, "x": 1.061, "y": 1.073}]}, "spiral_circle6": {"rotate": [{"time": 2.1, "value": 80.81}, {"time": 2.2333, "curve": "stepped"}, {"time": 2.3, "curve": [2.3, 63.16, 2.656, 90]}, {"time": 2.8667, "value": 90, "curve": "stepped"}, {"time": 2.9333, "value": 90}], "translate": [{"time": 2.1, "y": 152.53}, {"time": 2.2333, "curve": "stepped"}, {"time": 2.3, "curve": [2.511, 0, 2.656, 0, 2.3, 127.97, 2.656, 166.97]}, {"time": 2.8667, "y": 166.97}, {"time": 2.9333, "y": 182.35}], "scale": [{"time": 2.1, "x": 1.235, "y": 1.226, "curve": "stepped"}, {"time": 2.3, "x": 1.235, "y": 1.226, "curve": [2.3, 1.277, 2.411, 1.295, 2.3, 1.259, 2.411, 1.272]}, {"time": 2.4667, "x": 1.295, "y": 1.272}]}, "spiral_beam4": {"translate": [{"time": 2.7333, "curve": [2.778, 0, 2.822, 0, 2.778, 0, 2.867, 24.43]}, {"time": 2.8667, "y": 61.54}], "scale": [{"time": 2.1, "y": 0.805, "curve": [2.156, 1, 2.211, 1, 2.156, 0.805, 2.211, 0.902]}, {"time": 2.2667, "y": 0.902, "curve": "stepped"}, {"time": 2.7333, "y": 0.902, "curve": [2.778, 1, 2.867, 0.628, 2.778, 0.902, 2.867, 0.863]}, {"time": 2.8667, "x": 0.062, "y": 0.805}]}, "spiral_eff2": {"scale": [{"time": 2.1}, {"time": 2.2667, "x": 1.15}]}, "spiral_sparkle11": {"translate": [{"time": 2.1, "x": -9.25, "y": -5.63, "curve": "stepped"}, {"time": 2.2, "x": -9.25, "y": -5.63, "curve": "stepped"}, {"time": 2.2333, "x": -9.25, "y": -5.63, "curve": [2.522, -9.25, 2.7, -9.25, 2.522, -5.63, 2.7, -36.4]}, {"time": 2.9333, "x": -9.25, "y": -36.4, "curve": [2.989, -9.25, 2.811, -9.25, 2.989, -36.4, 2.811, -5.63]}, {"time": 3.1, "x": -9.25, "y": -5.63}], "scale": [{"time": 2.1, "x": 1.15, "y": 1.15}]}, "spiral_sparkle12": {"translate": [{"time": 2.1, "x": -35.55, "y": -1.06, "curve": "stepped"}, {"time": 2.9, "x": -35.55, "y": -1.06, "curve": [2.967, -35.55, 2.922, -35.55, 2.967, -1.06, 2.922, -31.83]}, {"time": 2.9333, "x": -35.55, "y": -31.83, "curve": [2.989, -35.55, 3.033, -35.55, 2.989, -31.83, 3.033, -1.06]}, {"time": 3.1, "x": -35.55, "y": -1.06}], "scale": [{"time": 2.1, "x": 1.15, "y": 1.15}]}, "spiral_sparkle13": {"translate": [{"time": 2.1, "x": 35.87, "y": -9.27, "curve": "stepped"}, {"time": 2.2333, "x": 35.87, "y": -9.27, "curve": "stepped"}, {"time": 2.3333, "x": 35.87, "y": -9.27, "curve": [2.589, 35.87, 2.656, 35.87, 2.589, -9.27, 2.656, -40.03]}, {"time": 2.9333, "x": 35.87, "y": -40.03, "curve": [2.989, 35.87, 3.044, 35.87, 2.989, -40.03, 3.044, -9.27]}, {"time": 3.1, "x": 35.87, "y": -9.27}], "scale": [{"time": 2.1, "x": 1.15, "y": 1.15}]}, "spiral_sparkle14": {"translate": [{"time": 2.1, "x": -52.21, "y": 9.88, "curve": "stepped"}, {"time": 2.2333, "x": -52.21, "y": 9.88, "curve": "stepped"}, {"time": 2.4667, "x": -52.21, "y": 9.88, "curve": [2.678, -52.21, 2.656, -52.21, 2.678, 9.88, 2.656, -20.89]}, {"time": 2.9333, "x": -52.21, "y": -20.89, "curve": [2.989, -52.21, 3.044, -52.21, 2.989, -20.89, 3.044, 9.88]}, {"time": 3.1, "x": -52.21, "y": 9.88}], "scale": [{"time": 2.1, "x": 1.15, "y": 1.15, "curve": "stepped"}, {"time": 3.1, "x": 1.15, "y": 1.15}]}, "spiral_sparkle15": {"translate": [{"time": 2.1, "x": 54.53, "y": 2.44, "curve": "stepped"}, {"time": 2.1333, "x": 54.53, "y": 2.44, "curve": [2.444, 54.53, 2.667, 54.53, 2.444, 2.44, 2.667, -28.33]}, {"time": 2.9333, "x": 54.53, "y": -28.33, "curve": [2.978, 54.53, 2.756, 54.53, 2.978, -28.33, 2.756, 2.44]}, {"time": 3.0667, "x": 54.53, "y": 2.44, "curve": "stepped"}, {"time": 3.1, "x": 54.53, "y": 2.44}], "scale": [{"time": 2.1, "x": 1.15, "y": 1.15}]}, "spiral_sparkle16": {"translate": [{"time": 2.1, "x": -66.92, "y": -13.24, "curve": "stepped"}, {"time": 2.4, "x": -66.92, "y": -13.24, "curve": "stepped"}, {"time": 2.5667, "x": -66.92, "y": -13.24, "curve": [2.744, -66.92, 2.656, -66.92, 2.744, -13.24, 2.656, -44.01]}, {"time": 2.9333, "x": -66.92, "y": -44.01, "curve": [2.989, -66.92, 3.044, -66.92, 2.989, -44.01, 3.044, -13.24]}, {"time": 3.1, "x": -66.92, "y": -13.24}], "scale": [{"time": 2.1, "x": 1.15, "y": 1.15, "curve": "stepped"}, {"time": 3.1, "x": 1.15, "y": 1.15}]}, "spiral_sparkle17": {"translate": [{"time": 2.1, "x": 67.12, "y": -16.06, "curve": "stepped"}, {"time": 2.3333, "x": 67.12, "y": -16.06, "curve": "stepped"}, {"time": 2.4, "x": 67.12, "y": -16.06, "curve": [2.633, 67.12, 2.756, 67.12, 2.633, -16.06, 2.756, -46.83]}, {"time": 2.9333, "x": 67.12, "y": -46.83, "curve": [2.989, 67.12, 2.867, 67.12, 2.989, -46.83, 2.867, -16.06]}, {"time": 3.1, "x": 67.12, "y": -16.06}], "scale": [{"time": 2.1, "x": 1.15, "y": 1.15}]}, "spiral_sparkle18": {"translate": [{"time": 2.1, "x": -23.44, "y": -17.49, "curve": "stepped"}, {"time": 2.2, "x": -23.44, "y": -17.49, "curve": [2.467, -23.44, 2.689, -23.44, 2.467, -17.49, 2.689, -48.26]}, {"time": 2.9333, "x": -23.44, "y": -48.26, "curve": [2.956, -23.44, 2.733, -23.44, 2.956, -48.26, 2.733, -17.49]}, {"time": 3, "x": -23.44, "y": -17.49, "curve": "stepped"}, {"time": 3.1, "x": -23.44, "y": -17.49}], "scale": [{"time": 2.1, "x": 1.15, "y": 1.15}]}, "spiral_sparkle19": {"translate": [{"time": 2.1, "x": 23.16, "y": 7.73, "curve": "stepped"}, {"time": 2.4, "x": 23.16, "y": 7.73, "curve": [2.633, 23.16, 2.756, 23.16, 2.633, 7.73, 2.756, -23.04]}, {"time": 2.9333, "x": 23.16, "y": -23.04, "curve": [2.989, 23.16, 2.867, 23.16, 2.989, -23.04, 2.867, 7.73]}, {"time": 3.1, "x": 23.16, "y": 7.73}], "scale": [{"time": 2.1, "x": 1.15, "y": 1.15}]}, "spiral_sparkle20": {"translate": [{"time": 2.1, "x": 3.97, "y": -4.44, "curve": "stepped"}, {"time": 2.2, "x": 3.97, "y": -4.44, "curve": "stepped"}, {"time": 2.3, "x": 3.97, "y": -4.44, "curve": [2.567, 3.97, 2.722, 3.97, 2.567, -4.44, 2.722, -35.21]}, {"time": 2.9333, "x": 3.97, "y": -35.21, "curve": [2.989, 3.97, 2.833, 3.97, 2.989, -35.21, 2.833, -4.44]}, {"time": 3.1, "x": 3.97, "y": -4.44}], "scale": [{"time": 2.1, "x": 1.15, "y": 1.15}]}, "knight_regen2": {"translate": [{"x": -6.39, "y": 44.72}], "scale": [{"time": 2.1, "x": 1.05, "y": 1.05}, {"time": 2.2667, "x": 1.2, "y": 1.3}]}, "knight2": {"translate": [{"x": -6.39, "y": 44.72}]}, "knight_body2": {"scale": [{"time": 1.3333, "curve": [1.571, 1, 1.862, 1, 1.571, 1, 1.862, 0.97]}, {"time": 2.1, "y": 0.97, "curve": [2.311, 1, 2.556, 1, 2.311, 0.97, 2.556, 1]}, {"time": 2.7667, "curve": [2.777, 1, 3.129, 1, 2.777, 1, 3.129, 0.97]}, {"time": 3.3333, "y": 0.97, "curve": [3.556, 1, 3.778, 1, 3.556, 0.97, 3.778, 1]}, {"time": 4, "curve": [4.222, 1, 4.444, 1, 4.222, 1, 4.444, 0.97]}, {"time": 4.6667, "y": 0.97, "curve": [4.889, 1, 5.111, 1, 4.889, 0.97, 5.111, 1]}, {"time": 5.3333}]}, "knight_sword2": {"rotate": [{"time": 1.3333, "value": 20, "curve": [1.571, 20, 1.862, 23.65]}, {"time": 2.1, "value": 23.65, "curve": [2.311, 23.65, 2.556, 20]}, {"time": 2.7667, "value": 20, "curve": [2.777, 20, 3.129, 23.65]}, {"time": 3.3333, "value": 23.65, "curve": [3.556, 23.65, 3.778, 20]}, {"time": 4, "value": 20, "curve": [4.222, 20, 4.444, 23.65]}, {"time": 4.6667, "value": 23.65, "curve": [4.889, 23.65, 5.111, 20]}, {"time": 5.3333, "value": 20}], "translate": [{"time": 1.3333, "x": 33.79, "y": -0.45, "curve": [1.571, 33.79, 1.862, 33.79, 1.571, -0.45, 1.862, -3.41]}, {"time": 2.1, "x": 33.79, "y": -3.41, "curve": [2.311, 33.79, 2.556, 33.79, 2.311, -3.41, 2.556, -0.45]}, {"time": 2.7667, "x": 33.79, "y": -0.45, "curve": [2.777, 33.79, 3.129, 33.79, 2.777, -0.45, 3.129, -3.41]}, {"time": 3.3333, "x": 33.79, "y": -3.41, "curve": [3.556, 33.79, 3.778, 33.79, 3.556, -3.41, 3.778, -0.45]}, {"time": 4, "x": 33.79, "y": -0.45, "curve": [4.222, 33.79, 4.444, 33.79, 4.222, -0.45, 4.444, -3.41]}, {"time": 4.6667, "x": 33.79, "y": -3.41, "curve": [4.889, 33.79, 5.111, 33.79, 4.889, -3.41, 5.111, -0.45]}, {"time": 5.3333, "x": 33.79, "y": -0.45}]}, "undead_night": {"translate": [{"x": -89.72}]}, "undead_night2": {"translate": [{"x": -89.72}]}, "undead_night3": {"translate": [{"x": -89.72}]}, "die": {"translate": [{"x": -89.72}]}, "die2": {"translate": [{"x": -89.72}]}, "die3": {"translate": [{"x": -89.72}]}}, "attachments": {"default": {"hero1_sword_eff": {"hero1_sword_eff": {"deform": [{"time": 1.9333, "curve": [1.944, 0, 1.956, 1]}, {"time": 1.9667, "vertices": [-0.38349, 1.89096, -0.28444, 5.22215], "curve": [1.978, 0, 1.989, 1]}, {"time": 2, "vertices": [-0.76698, 3.78191, -6.24885, 9.46617], "curve": [2.022, 0, 2.044, 1]}, {"time": 2.0667, "vertices": [-0.25566, 1.26064, -0.20373, 3.15192], "curve": [2.078, 0, 2.089, 1]}, {"time": 2.1}]}}, "spiral_beam1": {"spiral_beam1": {"deform": [{"time": 2.1, "offset": 3, "vertices": [6, 0, 6, 0, 0, -2e-05, -268.78268, -2e-05, -268.78268], "curve": [2.156, 0, 2.211, 1]}, {"time": 2.2667, "offset": 3, "vertices": [6, 0, 6, 0, 0, 7e-05, 698.5728, 7e-05, 698.5728], "curve": [2.3, 0, 2.333, 1]}, {"time": 2.3667, "offset": 3, "vertices": [6, 0, 6, 0, 0, 4e-05, 458.57254, 4e-05, 458.57254], "curve": [2.4, 0, 2.433, 1]}, {"time": 2.4667, "offset": 3, "vertices": [6, 0, 6, 0, 0, 7e-05, 365.66266, 7e-05, 365.66266], "curve": [2.511, 0, 2.556, 1]}, {"time": 2.6, "offset": 3, "vertices": [6, 0, 6, 0, 0, 4e-05, 401.88538, 4e-05, 401.88538], "curve": [2.644, 0, 2.689, 1]}, {"time": 2.7333, "offset": 3, "vertices": [6, 0, 6, 0, 0, 5e-05, 518.5725, 5e-05, 518.5725], "curve": [2.778, 0, 2.822, 1]}, {"time": 2.8667, "offset": 3, "vertices": [6, 0, 6, 0, 0, 0.00028, 583.66504, 0.00028, 583.66504]}]}}, "spiral_beam2": {"spiral_beam2": {"deform": [{"time": 2.1, "offset": 3, "vertices": [6, 0, 6, 0, 0, -2e-05, -268.78268, -2e-05, -268.78268], "curve": [2.156, 0, 2.211, 1]}, {"time": 2.2667, "offset": 3, "vertices": [6, 0, 6, 0, 0, 1e-05, 513.5508, 1e-05, 513.5508], "curve": [2.3, 0, 2.333, 1]}, {"time": 2.3667, "offset": 3, "vertices": [6, 0, 6, 0, 0, 4e-05, 400.7543, 4e-05, 400.7543], "curve": [2.4, 0, 2.433, 1]}, {"time": 2.4667, "offset": 3, "vertices": [6, 0, 6, 0, 0, -0.00023, 318.30823, -0.00023, 318.30823], "curve": [2.511, 0, 2.556, 1]}, {"time": 2.6, "offset": 3, "vertices": [6, 0, 6, 0, 0, 3e-05, 304.26874, 3e-05, 304.26874], "curve": [2.644, 0, 2.689, 1]}, {"time": 2.7333, "offset": 3, "vertices": [6, 0, 6, 0, 0, 4e-05, 441.63287, 4e-05, 441.63287], "curve": [2.778, 0, 2.822, 1]}, {"time": 2.8667, "offset": 3, "vertices": [6, 0, 6, 0, 0, 0.0005, 513.7255, 0.0005, 513.7255]}]}}, "spiral_beam3": {"spiral_beam1": {"deform": [{"time": 2.1, "offset": 3, "vertices": [6, 0, 6, 0, 0, -2e-05, -268.78268, -2e-05, -268.78268], "curve": [2.156, 0, 2.211, 1]}, {"time": 2.2667, "offset": 3, "vertices": [6, 0, 6, 0, 0, 7e-05, 698.5728, 7e-05, 698.5728], "curve": [2.3, 0, 2.333, 1]}, {"time": 2.3667, "offset": 3, "vertices": [6, 0, 6, 0, 0, 4e-05, 458.57254, 4e-05, 458.57254], "curve": [2.4, 0, 2.433, 1]}, {"time": 2.4667, "offset": 3, "vertices": [6, 0, 6, 0, 0, 7e-05, 365.66266, 7e-05, 365.66266], "curve": [2.511, 0, 2.556, 1]}, {"time": 2.6, "offset": 3, "vertices": [6, 0, 6, 0, 0, 4e-05, 401.88538, 4e-05, 401.88538], "curve": [2.644, 0, 2.689, 1]}, {"time": 2.7333, "offset": 3, "vertices": [6, 0, 6, 0, 0, 5e-05, 518.5725, 5e-05, 518.5725], "curve": [2.778, 0, 2.822, 1]}, {"time": 2.8667, "offset": 3, "vertices": [6, 0, 6, 0, 0, 0.00028, 583.66504, 0.00028, 583.66504]}]}}, "spiral_beam4": {"spiral_beam2": {"deform": [{"time": 2.1, "offset": 3, "vertices": [6, 0, 6, 0, 0, -2e-05, -268.78268, -2e-05, -268.78268], "curve": [2.156, 0, 2.211, 1]}, {"time": 2.2667, "offset": 3, "vertices": [6, 0, 6, 0, 0, 1e-05, 513.5508, 1e-05, 513.5508], "curve": [2.3, 0, 2.333, 1]}, {"time": 2.3667, "offset": 3, "vertices": [6, 0, 6, 0, 0, 4e-05, 400.7543, 4e-05, 400.7543], "curve": [2.4, 0, 2.433, 1]}, {"time": 2.4667, "offset": 3, "vertices": [6, 0, 6, 0, 0, -0.00023, 318.30823, -0.00023, 318.30823], "curve": [2.511, 0, 2.556, 1]}, {"time": 2.6, "offset": 3, "vertices": [6, 0, 6, 0, 0, 3e-05, 304.26874, 3e-05, 304.26874], "curve": [2.644, 0, 2.689, 1]}, {"time": 2.7333, "offset": 3, "vertices": [6, 0, 6, 0, 0, 4e-05, 441.63287, 4e-05, 441.63287], "curve": [2.778, 0, 2.822, 1]}, {"time": 2.8667, "offset": 3, "vertices": [6, 0, 6, 0, 0, 0.0005, 513.7255, 0.0005, 513.7255]}]}}, "spiral_sparkle1": {"spiral_sparkle1": {"deform": [{"time": 2.1, "offset": 1, "vertices": [160, 2e-05, 160, 2e-05, 160, 2e-05, 160], "curve": "stepped"}, {"time": 2.2, "offset": 1, "vertices": [160, 2e-05, 160, 2e-05, 160, 2e-05, 160], "curve": [2.2, 0.7, 2.233, 0.33]}, {"time": 2.2333, "vertices": [1.90251, -1.90251, -1.90252, -1.90251, -1.90251, 1.90251, 1.90251, 1.90251], "curve": [2.233, 0.7, 2.811, 1]}, {"time": 3.1, "offset": 1, "vertices": [197.08678, 2e-05, 197.08678, 2e-05, 197.08678, 2e-05, 197.08678]}]}}, "spiral_sparkle2": {"spiral_sparkle1": {"deform": [{"time": 2.1, "curve": [2.1, 0.7, 2.633, 1]}, {"time": 2.9, "offset": 1, "vertices": [150.01262, 1e-05, 150.01262, 1e-05, 150.01262, 1e-05, 150.01262], "curve": "stepped"}, {"time": 3.1}]}}, "spiral_sparkle3": {"spiral_sparkle1": {"deform": [{"time": 2.1, "offset": 1, "vertices": [102.33028, 1e-05, 102.33028, 1e-05, 102.33028, 1e-05, 102.33028], "curve": "stepped"}, {"time": 2.2333, "offset": 1, "vertices": [102.33028, 1e-05, 102.33028, 1e-05, 102.33028, 1e-05, 102.33028], "curve": [2.233, 0.7, 2.333, 0.33]}, {"time": 2.3333, "curve": [2.333, 0.7, 2.844, 1]}, {"time": 3.1, "offset": 1, "vertices": [138.96707, 1e-05, 138.96707, 1e-05, 138.96707, 1e-05, 138.96707]}]}}, "spiral_sparkle4": {"spiral_sparkle1": {"deform": [{"time": 2.1, "offset": 1, "vertices": [131.61737, 1e-05, 131.61737, 1e-05, 131.61737, 1e-05, 131.61737], "curve": "stepped"}, {"time": 2.2333, "offset": 1, "vertices": [113.03417, 1e-05, 113.03417, 1e-05, 113.03417, 1e-05, 113.03417], "curve": [2.233, 0.7, 2.344, 1]}, {"time": 2.4, "offset": 1, "vertices": [131.61737, 1e-05, 131.61737, 1e-05, 131.61737, 1e-05, 131.61737], "curve": [2.4, 0.7, 2.467, 0.33]}, {"time": 2.4667, "curve": [2.467, 0.7, 2.867, 1]}, {"time": 3.0667, "offset": 1, "vertices": [148.82704, 3e-05, 148.82704, 3e-05, 148.82704, 3e-05, 148.82704], "curve": "stepped"}, {"time": 3.1, "offset": 1, "vertices": [131.61737, 1e-05, 131.61737, 1e-05, 131.61737, 1e-05, 131.61737]}]}}, "spiral_sparkle5": {"spiral_sparkle1": {"deform": [{"time": 2.1, "offset": 1, "vertices": [16.78611, 0, 16.78611, 0, 16.78611, 0, 16.78611], "curve": "stepped"}, {"time": 2.1333, "curve": [2.133, 0.7, 2.756, 1]}, {"time": 3.0667, "offset": 1, "vertices": [155.1505, 0, 155.1505, 0, 155.1505, 0, 155.1505], "curve": "stepped"}, {"time": 3.1, "offset": 1, "vertices": [16.78611, 0, 16.78611, 0, 16.78611, 0, 16.78611]}]}}, "spiral_sparkle6": {"spiral_sparkle1": {"deform": [{"time": 2.1, "offset": 1, "vertices": [82.59259, 0, 82.59259, 0, 82.59259, 0, 82.59259], "curve": "stepped"}, {"time": 2.3667, "curve": [2.367, 0.7, 2.3, 1]}, {"time": 2.4, "offset": 1, "vertices": [90, 0, 90, 0, 90, 0, 90], "curve": [2.4, 0.7, 2.511, 1]}, {"time": 2.5667, "curve": [2.567, 0.7, 2.833, 1]}, {"time": 3.0667, "offset": 1, "vertices": [121.05412, 0, 121.05412, 0, 121.05412, 0, 121.05412], "curve": [3.067, 0.7, 3.089, 1]}, {"time": 3.1, "offset": 1, "vertices": [82.59259, 0, 82.59259, 0, 82.59259, 0, 82.59259]}]}}, "spiral_sparkle7": {"spiral_sparkle1": {"deform": [{"time": 2.1, "offset": 1, "vertices": [97.38105, 1e-05, 97.38105, 1e-05, 97.38105, 1e-05, 97.38105], "curve": "stepped"}, {"time": 2.3333, "offset": 1, "vertices": [97.38105, 1e-05, 97.38105, 1e-05, 97.38105, 1e-05, 97.38105], "curve": [2.333, 0.7, 2.4, 0.33]}, {"time": 2.4, "curve": [2.4, 0.7, 2.867, 1]}, {"time": 3.1, "offset": 1, "vertices": [147.92696, 1e-05, 147.92696, 1e-05, 147.92696, 1e-05, 147.92696]}]}}, "spiral_sparkle8": {"spiral_sparkle1": {"deform": [{"time": 2.1, "vertices": [-1.11381, 16.95254, -1.11381, 16.95254, -1.11381, 16.95254, -1.11381, 16.95254], "curve": "stepped"}, {"time": 2.2, "vertices": [-1.30701, 2.83272, -1.30701, 2.83272, -1.30701, 2.83272, -1.30701, 2.83272], "curve": [2.2, 0.7, 2.733, 1]}, {"time": 3, "vertices": [-0.04098, 133.82173, -0.04098, 133.82173, -0.04098, 133.82173, -0.04098, 133.82173], "curve": "stepped"}, {"time": 3.1, "vertices": [-1.11381, 16.95254, -1.11381, 16.95254, -1.11381, 16.95254, -1.11381, 16.95254]}]}}, "spiral_sparkle9": {"spiral_sparkle1": {"deform": [{"time": 2.1, "offset": 1, "vertices": [125.11292, 0, 125.11292, 0, 125.11292, 0, 125.11292], "curve": "stepped"}, {"time": 2.4, "curve": [2.4, 0.7, 2.867, 1]}, {"time": 3.1, "offset": 1, "vertices": [163.57446, 0, 163.57446, 0, 163.57446, 0, 163.57446]}]}}, "spiral_sparkle10": {"spiral_sparkle1": {"deform": [{"time": 2.1, "offset": 1, "vertices": [70, 0, 70, 0, 70, 0, 70], "curve": "stepped"}, {"time": 2.2, "offset": 1, "vertices": [70, 0, 70, 0, 70, 0, 70], "curve": [2.2, 0.7, 2.3, 0.33]}, {"time": 2.3, "curve": [2.3, 0.7, 2.833, 1]}, {"time": 3.1, "offset": 1, "vertices": [107.79466, 1e-05, 107.79466, 1e-05, 107.79466, 1e-05, 107.79466]}]}}, "spiral_sparkle11": {"spiral_sparkle1": {"deform": [{"time": 2.1, "offset": 1, "vertices": [160, 2e-05, 160, 2e-05, 160, 2e-05, 160], "curve": "stepped"}, {"time": 2.2, "offset": 1, "vertices": [160, 2e-05, 160, 2e-05, 160, 2e-05, 160], "curve": [2.2, 0.7, 2.233, 0.33]}, {"time": 2.2333, "vertices": [1.90251, -1.90251, -1.90252, -1.90251, -1.90251, 1.90251, 1.90251, 1.90251], "curve": [2.233, 0.7, 2.811, 1]}, {"time": 3.1, "offset": 1, "vertices": [197.08678, 2e-05, 197.08678, 2e-05, 197.08678, 2e-05, 197.08678]}]}}, "spiral_sparkle12": {"spiral_sparkle1": {"deform": [{"time": 2.1, "curve": [2.1, 0.7, 2.633, 1]}, {"time": 2.9, "offset": 1, "vertices": [150.01262, 1e-05, 150.01262, 1e-05, 150.01262, 1e-05, 150.01262], "curve": "stepped"}, {"time": 3.1}]}}, "spiral_sparkle13": {"spiral_sparkle1": {"deform": [{"time": 2.1, "offset": 1, "vertices": [102.33028, 1e-05, 102.33028, 1e-05, 102.33028, 1e-05, 102.33028], "curve": "stepped"}, {"time": 2.2333, "offset": 1, "vertices": [102.33028, 1e-05, 102.33028, 1e-05, 102.33028, 1e-05, 102.33028], "curve": [2.233, 0.7, 2.333, 0.33]}, {"time": 2.3333, "curve": [2.333, 0.7, 2.844, 1]}, {"time": 3.1, "offset": 1, "vertices": [138.96707, 1e-05, 138.96707, 1e-05, 138.96707, 1e-05, 138.96707]}]}}, "spiral_sparkle14": {"spiral_sparkle1": {"deform": [{"time": 2.1, "offset": 1, "vertices": [131.61737, 1e-05, 131.61737, 1e-05, 131.61737, 1e-05, 131.61737], "curve": "stepped"}, {"time": 2.2333, "offset": 1, "vertices": [113.03417, 1e-05, 113.03417, 1e-05, 113.03417, 1e-05, 113.03417], "curve": [2.233, 0.7, 2.344, 1]}, {"time": 2.4, "offset": 1, "vertices": [131.61737, 1e-05, 131.61737, 1e-05, 131.61737, 1e-05, 131.61737], "curve": [2.4, 0.7, 2.467, 0.33]}, {"time": 2.4667, "curve": [2.467, 0.7, 2.867, 1]}, {"time": 3.0667, "offset": 1, "vertices": [148.82704, 3e-05, 148.82704, 3e-05, 148.82704, 3e-05, 148.82704], "curve": "stepped"}, {"time": 3.1, "offset": 1, "vertices": [131.61737, 1e-05, 131.61737, 1e-05, 131.61737, 1e-05, 131.61737]}]}}, "spiral_sparkle15": {"spiral_sparkle1": {"deform": [{"time": 2.1, "offset": 1, "vertices": [16.78611, 0, 16.78611, 0, 16.78611, 0, 16.78611], "curve": "stepped"}, {"time": 2.1333, "curve": [2.133, 0.7, 2.756, 1]}, {"time": 3.0667, "offset": 1, "vertices": [155.1505, 0, 155.1505, 0, 155.1505, 0, 155.1505], "curve": "stepped"}, {"time": 3.1, "offset": 1, "vertices": [16.78611, 0, 16.78611, 0, 16.78611, 0, 16.78611]}]}}, "spiral_sparkle16": {"spiral_sparkle1": {"deform": [{"time": 2.1, "offset": 1, "vertices": [82.59259, 0, 82.59259, 0, 82.59259, 0, 82.59259], "curve": "stepped"}, {"time": 2.3667, "curve": [2.367, 0.7, 2.3, 1]}, {"time": 2.4, "offset": 1, "vertices": [90, 0, 90, 0, 90, 0, 90], "curve": [2.4, 0.7, 2.511, 1]}, {"time": 2.5667, "curve": [2.567, 0.7, 2.833, 1]}, {"time": 3.0667, "offset": 1, "vertices": [121.05412, 0, 121.05412, 0, 121.05412, 0, 121.05412], "curve": [3.067, 0.7, 3.089, 1]}, {"time": 3.1, "offset": 1, "vertices": [82.59259, 0, 82.59259, 0, 82.59259, 0, 82.59259]}]}}, "spiral_sparkle17": {"spiral_sparkle1": {"deform": [{"time": 2.1, "offset": 1, "vertices": [97.38105, 1e-05, 97.38105, 1e-05, 97.38105, 1e-05, 97.38105], "curve": "stepped"}, {"time": 2.3333, "offset": 1, "vertices": [97.38105, 1e-05, 97.38105, 1e-05, 97.38105, 1e-05, 97.38105], "curve": [2.333, 0.7, 2.4, 0.33]}, {"time": 2.4, "curve": [2.4, 0.7, 2.867, 1]}, {"time": 3.1, "offset": 1, "vertices": [147.92696, 1e-05, 147.92696, 1e-05, 147.92696, 1e-05, 147.92696]}]}}, "spiral_sparkle18": {"spiral_sparkle1": {"deform": [{"time": 2.1, "vertices": [-1.11381, 16.95254, -1.11381, 16.95254, -1.11381, 16.95254, -1.11381, 16.95254], "curve": "stepped"}, {"time": 2.2, "vertices": [-1.30701, 2.83272, -1.30701, 2.83272, -1.30701, 2.83272, -1.30701, 2.83272], "curve": [2.2, 0.7, 2.733, 1]}, {"time": 3, "vertices": [-0.04098, 133.82173, -0.04098, 133.82173, -0.04098, 133.82173, -0.04098, 133.82173], "curve": "stepped"}, {"time": 3.1, "vertices": [-1.11381, 16.95254, -1.11381, 16.95254, -1.11381, 16.95254, -1.11381, 16.95254]}]}}, "spiral_sparkle19": {"spiral_sparkle1": {"deform": [{"time": 2.1, "offset": 1, "vertices": [125.11292, 0, 125.11292, 0, 125.11292, 0, 125.11292], "curve": "stepped"}, {"time": 2.4, "curve": [2.4, 0.7, 2.867, 1]}, {"time": 3.1, "offset": 1, "vertices": [163.57446, 0, 163.57446, 0, 163.57446, 0, 163.57446]}]}}, "spiral_sparkle20": {"spiral_sparkle1": {"deform": [{"time": 2.1, "offset": 1, "vertices": [70, 0, 70, 0, 70, 0, 70], "curve": "stepped"}, {"time": 2.2, "offset": 1, "vertices": [70, 0, 70, 0, 70, 0, 70], "curve": [2.2, 0.7, 2.3, 0.33]}, {"time": 2.3, "curve": [2.3, 0.7, 2.833, 1]}, {"time": 3.1, "offset": 1, "vertices": [107.79466, 1e-05, 107.79466, 1e-05, 107.79466, 1e-05, 107.79466]}]}}}}, "events": [{"time": 1.9333, "name": "attack"}]}, "hero_package_2": {"slots": {"archer_arrow": {"attachment": [{}]}, "archer_body": {"attachment": [{}]}, "archer_bow": {"attachment": [{}]}, "die_skull01": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6333, "color": "ffffffff"}, {"time": 3.8, "color": "ffffff00"}]}, "die_skull02": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6333, "color": "ffffffff"}, {"time": 3.8, "color": "ffffff00"}]}, "die_skull03": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6333, "color": "ffffffff"}, {"time": 3.8, "color": "ffffff00"}]}, "die_skull04": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6333, "color": "ffffffff"}, {"time": 3.8, "color": "ffffff00"}]}, "die_skull05": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6333, "color": "ffffffff"}, {"time": 3.8, "color": "ffffff00"}]}, "die_skull06": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6333, "color": "ffffffff"}, {"time": 3.8, "color": "ffffff00"}]}, "die_skull07": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6333, "color": "ffffffff"}, {"time": 3.8, "color": "ffffff00"}]}, "die_skull08": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6333, "color": "ffffffff"}, {"time": 3.8, "color": "ffffff00"}]}, "die_skull09": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6333, "color": "ffffffff"}, {"time": 3.8, "color": "ffffff00"}]}, "die_smoke": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1667, "color": "fffffffe", "curve": "stepped"}, {"time": 3.5, "color": "ffffffff"}, {"time": 3.7333, "color": "ffffff00"}]}, "die_smoke2": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1, "color": "ffffffff", "curve": "stepped"}, {"time": 3.4, "color": "ffffffff"}, {"time": 3.6667, "color": "ffffff00"}]}, "die_smoke3": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.4667, "color": "ffffffff"}, {"time": 3.8333, "color": "ffffff00"}]}, "die_smoke4": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1667, "color": "fffffffe", "curve": "stepped"}, {"time": 3.5, "color": "ffffffff"}, {"time": 3.7333, "color": "ffffff00"}]}, "die_smoke5": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1, "color": "ffffffff", "curve": "stepped"}, {"time": 3.4, "color": "ffffffff"}, {"time": 3.6667, "color": "ffffff00"}]}, "die_smoke6": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.4667, "color": "ffffffff"}, {"time": 3.8333, "color": "ffffff00"}]}, "die_smoke7": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1667, "color": "fffffffe", "curve": "stepped"}, {"time": 3.5, "color": "ffffffff"}, {"time": 3.7333, "color": "ffffff00"}]}, "die_smoke8": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1, "color": "ffffffff", "curve": "stepped"}, {"time": 3.4, "color": "ffffffff"}, {"time": 3.6667, "color": "ffffff00"}]}, "die_smoke9": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.4667, "color": "ffffffff"}, {"time": 3.8333, "color": "ffffff00"}]}, "hero1_body": {"attachment": [{}]}, "hero1_head": {"attachment": [{}]}, "hero1_shield": {"attachment": [{}]}, "hero1_sword": {"attachment": [{}]}, "hero_2_eff": {"rgba": [{"time": 1.4, "color": "ffffff00", "curve": "stepped"}, {"time": 1.9333, "color": "ffffff00"}, {"time": 2, "color": "ffffffb4", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffb4"}, {"time": 2.2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2333, "color": "ffffff00"}, {"time": 2.3, "color": "ffffffb4", "curve": "stepped"}, {"time": 2.3667, "color": "ffffffb4"}, {"time": 2.5, "color": "ffffff00", "curve": "stepped"}, {"time": 2.5333, "color": "ffffff00"}, {"time": 2.6, "color": "ffffffb4", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffb4"}, {"time": 2.8, "color": "ffffff00"}], "attachment": [{"time": 1.4, "name": "hero_2_eff"}, {"time": 1.9333, "name": "hero_2_eff"}, {"time": 2.2333, "name": "hero_2_eff"}, {"time": 2.5333, "name": "hero_2_eff"}]}, "hero_3_arrow": {"attachment": [{}]}, "hero_3_arrow2": {"attachment": [{}]}, "hero_3_body": {"attachment": [{}]}, "hero_3_bow_attack": {"attachment": [{}]}, "hero_3_bow_idle": {"attachment": [{}]}, "hero_3_bow_string1": {"attachment": [{}]}, "hero_3_bow_string2": {"attachment": [{}]}, "hero_3_bow_wingB": {"attachment": [{}]}, "hero_3_bow_wingF": {"attachment": [{}]}, "hobbit_W_body": {"attachment": [{}]}, "hobbit_W_sword": {"attachment": [{}]}, "knight_body": {"attachment": [{}]}, "knight_body2": {"attachment": [{}]}, "knight_sword": {"attachment": [{}]}, "knight_sword2": {"attachment": [{}]}, "shadow": {"attachment": [{}]}, "shadow2": {"rgba": [{"time": 3, "color": "ffffffff", "curve": "stepped"}, {"time": 3.0333, "color": "ffffff00"}]}, "shadow3": {"rgba": [{"time": 3, "color": "ffffffff", "curve": "stepped"}, {"time": 3.0333, "color": "ffffff00"}]}, "shadow4": {"rgba": [{"time": 3, "color": "ffffffff", "curve": "stepped"}, {"time": 3.0333, "color": "ffffff00"}]}, "shadow5": {"attachment": [{}]}, "shadow6": {"attachment": [{"name": "shadow"}]}, "shadow7": {"attachment": [{}]}, "shadow8": {"attachment": [{}]}, "shadow9": {"attachment": [{}]}, "shadow10": {"attachment": [{}]}, "shadow11": {"attachment": [{}]}, "undead_knight_body": {"rgba": [{"time": 3, "color": "ffffffff", "curve": "stepped"}, {"time": 3.0333, "color": "ffffff00"}]}, "undead_knight_body2": {"rgba": [{"time": 3, "color": "ffffffff", "curve": "stepped"}, {"time": 3.0333, "color": "ffffff00"}]}, "undead_knight_body3": {"rgba": [{"time": 3, "color": "ffffffff", "curve": "stepped"}, {"time": 3.0333, "color": "ffffff00"}]}, "undead_knight_sword": {"rgba": [{"time": 3, "color": "ffffffff", "curve": "stepped"}, {"time": 3.0333, "color": "ffffff00"}]}, "undead_knight_sword2": {"rgba": [{"time": 3, "color": "ffffffff", "curve": "stepped"}, {"time": 3.0333, "color": "ffffff00"}]}, "undead_knight_sword3": {"rgba": [{"time": 3, "color": "ffffffff", "curve": "stepped"}, {"time": 3.0333, "color": "ffffff00"}]}, "unit_h4_body": {"attachment": [{}]}, "unit_h4_hairB": {"attachment": [{}]}, "unit_h4_head1": {"attachment": [{}]}, "unit_h4_spellbook": {"attachment": [{}]}}, "bones": {"hero_2_body": {"rotate": [{"time": 1.4}, {"time": 1.7333, "value": 4.98}, {"time": 1.9, "value": 5.98}, {"time": 1.9333, "value": -3.9, "curve": "stepped"}, {"time": 2.2333, "value": -3.9, "curve": "stepped"}, {"time": 2.5333, "value": -3.9, "curve": "stepped"}, {"time": 3, "value": -3.9}, {"time": 3.1667}], "translate": [{"time": 1.4}, {"time": 1.7333, "x": -5, "curve": "stepped"}, {"time": 1.9, "x": -5}, {"time": 1.9333, "x": 5}, {"time": 2.0667}, {"time": 2.2, "x": 5, "curve": "stepped"}, {"time": 2.2333, "x": 5}, {"time": 2.3667}, {"time": 2.5, "x": 5, "curve": "stepped"}, {"time": 2.5333, "x": 5}, {"time": 2.6667}, {"time": 2.8, "x": 5, "curve": "stepped"}, {"time": 3, "x": 5}, {"time": 3.1667}], "scale": [{"curve": [0.222, 1, 0.444, 0.97, 0.222, 1, 0.444, 1]}, {"time": 0.6667, "x": 0.97, "curve": [0.889, 0.97, 1.111, 1, 0.889, 1, 1.111, 1]}, {"time": 1.3333, "curve": "stepped"}, {"time": 3.1667, "curve": [3.389, 1, 3.611, 0.97, 3.389, 1, 3.611, 1]}, {"time": 3.8333, "x": 0.97, "curve": [4.056, 0.97, 4.278, 1, 4.056, 1, 4.278, 1]}, {"time": 4.5, "curve": [4.722, 1, 4.944, 0.97, 4.722, 1, 4.944, 1]}, {"time": 5.1667, "x": 0.97, "curve": [5.389, 0.97, 5.611, 1, 5.389, 1, 5.611, 1]}, {"time": 5.8333, "curve": [6.056, 1, 6.278, 0.97, 6.056, 1, 6.278, 1]}, {"time": 6.5, "x": 0.97, "curve": [6.722, 0.97, 6.944, 1, 6.722, 1, 6.944, 1]}, {"time": 7.1667}]}, "hero_2_axe": {"rotate": [{"value": 25.66, "curve": [0.057, 25.42, 0.112, 25.23]}, {"time": 0.1667, "value": 25.23, "curve": [0.389, 25.23, 0.611, 27.93]}, {"time": 0.8333, "value": 27.93, "curve": [1.001, 27.93, 1.168, 26.42]}, {"time": 1.3333, "value": 25.66, "curve": [1.356, 25.56, 1.378, 34.76]}, {"time": 1.4, "value": 35.36}, {"time": 1.7333, "value": 44.3}, {"time": 1.9, "value": 46.89}, {"time": 1.9333, "curve": "stepped"}, {"time": 2.2333, "curve": "stepped"}, {"time": 2.5333, "curve": "stepped"}, {"time": 3}, {"time": 3.1667, "value": 25.66, "curve": [3.224, 25.42, 3.279, 25.23]}, {"time": 3.3333, "value": 25.23, "curve": [3.556, 25.23, 3.778, 27.93]}, {"time": 4, "value": 27.93, "curve": [4.167, 27.93, 4.333, 26.38]}, {"time": 4.5, "value": 25.66, "curve": [4.557, 25.42, 4.612, 25.23]}, {"time": 4.6667, "value": 25.23, "curve": [4.889, 25.23, 5.111, 27.93]}, {"time": 5.3333, "value": 27.93, "curve": [5.501, 27.93, 5.667, 26.38]}, {"time": 5.8333, "value": 25.66, "curve": [5.89, 25.42, 5.945, 25.23]}, {"time": 6, "value": 25.23, "curve": [6.222, 25.23, 6.444, 27.93]}, {"time": 6.6667, "value": 27.93, "curve": [6.834, 27.93, 7.001, 26.42]}, {"time": 7.1667, "value": 25.66}], "translate": [{"x": -28.74, "y": -17.94, "curve": [0.057, -29.02, 0.112, -29.22, 0.057, -17.45, 0.112, -17.08]}, {"time": 0.1667, "x": -29.22, "y": -17.08, "curve": [0.389, -29.22, 0.611, -26.22, 0.389, -17.08, 0.611, -22.45]}, {"time": 0.8333, "x": -26.22, "y": -22.45, "curve": [1.001, -26.22, 1.168, -27.9, 1.001, -22.45, 1.168, -19.45]}, {"time": 1.3333, "x": -28.74, "y": -17.94, "curve": [1.356, -28.86, 1.378, -30.51, 1.356, -17.74, 1.378, 1.44]}, {"time": 1.4, "x": -30.51, "y": 1.44, "curve": "stepped"}, {"time": 1.7333, "x": -30.51, "y": 1.44, "curve": "stepped"}, {"time": 1.9, "x": -30.51, "y": 1.44}, {"time": 1.9333}, {"time": 2, "y": 9.09}, {"time": 2.0667}, {"time": 2.1333, "y": 9.09}, {"time": 2.2, "curve": "stepped"}, {"time": 2.2333}, {"time": 2.3, "y": 9.09}, {"time": 2.3667}, {"time": 2.4333, "y": 9.09}, {"time": 2.5, "curve": "stepped"}, {"time": 2.5333}, {"time": 2.6, "y": 9.09}, {"time": 2.6667}, {"time": 2.7333, "y": 9.09}, {"time": 2.8, "curve": "stepped"}, {"time": 3}, {"time": 3.1667, "x": -28.74, "y": -17.94, "curve": [3.224, -29.02, 3.279, -29.22, 3.224, -17.45, 3.279, -17.08]}, {"time": 3.3333, "x": -29.22, "y": -17.08, "curve": [3.556, -29.22, 3.778, -26.22, 3.556, -17.08, 3.778, -22.45]}, {"time": 4, "x": -26.22, "y": -22.45, "curve": [4.167, -26.22, 4.333, -27.94, 4.167, -22.45, 4.333, -19.38]}, {"time": 4.5, "x": -28.74, "y": -17.94, "curve": [4.557, -29.02, 4.612, -29.22, 4.557, -17.45, 4.612, -17.08]}, {"time": 4.6667, "x": -29.22, "y": -17.08, "curve": [4.889, -29.22, 5.111, -26.22, 4.889, -17.08, 5.111, -22.45]}, {"time": 5.3333, "x": -26.22, "y": -22.45, "curve": [5.501, -26.22, 5.667, -27.94, 5.501, -22.45, 5.667, -19.38]}, {"time": 5.8333, "x": -28.74, "y": -17.94, "curve": [5.89, -29.02, 5.945, -29.22, 5.89, -17.45, 5.945, -17.08]}, {"time": 6, "x": -29.22, "y": -17.08, "curve": [6.222, -29.22, 6.444, -26.22, 6.222, -17.08, 6.444, -22.45]}, {"time": 6.6667, "x": -26.22, "y": -22.45, "curve": [6.834, -26.22, 7.001, -27.9, 6.834, -22.45, 7.001, -19.45]}, {"time": 7.1667, "x": -28.74, "y": -17.94}]}, "hero_2_hair1": {"rotate": [{"value": -4.74, "curve": [0.222, -4.74, 0.444, -0.54]}, {"time": 0.6667, "value": -0.54, "curve": [0.889, -0.54, 1.111, -4.74]}, {"time": 1.3333, "value": -4.74, "curve": [1.533, -4.74, 1.733, -4.5]}, {"time": 1.9333}, {"time": 2.0667, "value": 3}, {"time": 2.2, "curve": "stepped"}, {"time": 2.2333}, {"time": 2.3667, "value": 3}, {"time": 2.5, "curve": "stepped"}, {"time": 2.5333}, {"time": 2.6667, "value": 3}, {"time": 2.8, "curve": "stepped"}, {"time": 3.1}, {"time": 3.1667, "value": -4.74, "curve": [3.389, -4.74, 3.611, -0.54]}, {"time": 3.8333, "value": -0.54, "curve": [4.056, -0.54, 4.278, -4.74]}, {"time": 4.5, "value": -4.74, "curve": [4.722, -4.74, 4.944, -0.54]}, {"time": 5.1667, "value": -0.54, "curve": [5.389, -0.54, 5.611, -4.74]}, {"time": 5.8333, "value": -4.74, "curve": [6.056, -4.74, 6.278, -0.54]}, {"time": 6.5, "value": -0.54, "curve": [6.722, -0.54, 6.944, -4.74]}, {"time": 7.1667, "value": -4.74}], "translate": [{"curve": [0.222, 0, 0.444, -2.45, 0.222, 0, 0.444, -0.79]}, {"time": 0.6667, "x": -2.45, "y": -0.79, "curve": [0.889, -2.45, 1.111, 0, 0.889, -0.79, 1.111, 0]}, {"time": 1.3333, "curve": "stepped"}, {"time": 3.1667, "curve": [3.389, 0, 3.611, -2.45, 3.389, 0, 3.611, -0.79]}, {"time": 3.8333, "x": -2.45, "y": -0.79, "curve": [4.056, -2.45, 4.278, 0, 4.056, -0.79, 4.278, 0]}, {"time": 4.5, "curve": [4.722, 0, 4.944, -2.45, 4.722, 0, 4.944, -0.79]}, {"time": 5.1667, "x": -2.45, "y": -0.79, "curve": [5.389, -2.45, 5.611, 0, 5.389, -0.79, 5.611, 0]}, {"time": 5.8333, "curve": [6.056, 0, 6.278, -2.45, 6.056, 0, 6.278, -0.79]}, {"time": 6.5, "x": -2.45, "y": -0.79, "curve": [6.722, -2.45, 6.944, 0, 6.722, -0.79, 6.944, 0]}, {"time": 7.1667}]}, "hero_2_axe_center": {"rotate": [{"time": 1.4, "curve": "stepped"}, {"time": 1.9, "curve": "stepped"}, {"time": 1.9333}, {"time": 2.2, "value": -360, "curve": "stepped"}, {"time": 2.2333}, {"time": 2.5, "value": -360, "curve": "stepped"}, {"time": 2.5333}, {"time": 2.8, "value": -360, "curve": "stepped"}, {"time": 3, "value": -360, "curve": "stepped"}, {"time": 3.1667}], "translate": [{"x": -110, "curve": "stepped"}, {"time": 1.4, "x": -110, "curve": "stepped"}, {"time": 1.9, "x": -110}, {"time": 1.9333, "x": -110, "y": 50}, {"time": 2, "x": -100, "y": 50}, {"time": 2.0667, "x": -110, "y": 50}, {"time": 2.1333, "x": -120, "y": 50}, {"time": 2.2, "x": -110, "y": 50, "curve": "stepped"}, {"time": 2.2333, "x": -110, "y": 50}, {"time": 2.3, "x": -100, "y": 50}, {"time": 2.3667, "x": -110, "y": 50}, {"time": 2.4333, "x": -120, "y": 50}, {"time": 2.5, "x": -110, "y": 50, "curve": "stepped"}, {"time": 2.5333, "x": -110, "y": 50}, {"time": 2.6, "x": -100, "y": 50}, {"time": 2.6667, "x": -110, "y": 50}, {"time": 2.7333, "x": -120, "y": 50}, {"time": 2.8, "x": -110, "y": 70, "curve": "stepped"}, {"time": 3, "x": -110, "y": 70}, {"time": 3.1667, "x": -110, "curve": "stepped"}, {"time": 4.1333, "x": -110, "curve": "stepped"}, {"time": 5.4667, "x": -110}], "scale": [{"time": 1.9}, {"time": 1.9333, "y": 0.7}, {"time": 2, "x": 0.8, "y": 1.1}, {"time": 2.0667, "y": 0.7}, {"time": 2.1333, "x": 0.8, "y": 1.1}, {"time": 2.2, "y": 0.7, "curve": "stepped"}, {"time": 2.2333, "y": 0.7}, {"time": 2.3, "x": 0.8, "y": 1.1}, {"time": 2.3667, "y": 0.7}, {"time": 2.4333, "x": 0.8, "y": 1.1}, {"time": 2.5, "y": 0.7, "curve": "stepped"}, {"time": 2.5333, "y": 0.7}, {"time": 2.6, "x": 0.8, "y": 1.1}, {"time": 2.6667, "y": 0.7}, {"time": 2.7333, "x": 0.8, "y": 1.1}, {"time": 2.8, "y": 0.7, "curve": "stepped"}, {"time": 3, "y": 0.7}, {"time": 3.1667}]}, "undead_knight_body": {"scale": [{"y": 0.985, "curve": [0.113, 1, 0.223, 1, 0.113, 0.992, 0.223, 1]}, {"time": 0.3333, "curve": [0.556, 1, 0.778, 1, 0.556, 1, 0.778, 0.97]}, {"time": 1, "y": 0.97, "curve": [1.112, 1, 1.222, 1, 1.112, 0.97, 1.222, 0.978]}, {"time": 1.3333, "y": 0.985, "curve": [1.446, 1, 1.556, 1, 1.446, 0.992, 1.556, 1]}, {"time": 1.6667, "curve": [1.878, 1, 2.089, 1, 1.878, 1, 2.089, 0.97]}, {"time": 2.3, "y": 0.97, "curve": [2.492, 1, 3.91, 1, 2.492, 0.97, 3.91, 0.972]}, {"time": 4.1, "y": 0.985, "curve": [4.213, 1, 4.323, 1, 4.213, 0.992, 4.323, 1]}, {"time": 4.4333, "curve": [4.656, 1, 4.878, 1, 4.656, 1, 4.878, 0.97]}, {"time": 5.1, "y": 0.97, "curve": [5.212, 1, 5.324, 1, 5.212, 0.97, 5.324, 0.977]}, {"time": 5.4333, "y": 0.985}]}, "undead_knight_sword": {"rotate": [{"value": 21.83, "curve": [0.113, 20.92, 0.223, 20]}, {"time": 0.3333, "value": 20, "curve": [0.556, 20, 0.778, 23.65]}, {"time": 1, "value": 23.65, "curve": [1.112, 23.65, 1.222, 22.72]}, {"time": 1.3333, "value": 21.83, "curve": [1.446, 20.92, 1.556, 20]}, {"time": 1.6667, "value": 20, "curve": [1.878, 20, 2.089, 23.65]}, {"time": 2.3, "value": 23.65, "curve": [2.492, 23.65, 3.91, 23.36]}, {"time": 4.1, "value": 21.83, "curve": [4.213, 20.92, 4.323, 20]}, {"time": 4.4333, "value": 20, "curve": [4.656, 20, 4.878, 23.65]}, {"time": 5.1, "value": 23.65, "curve": [5.212, 23.65, 5.324, 22.75]}, {"time": 5.4333, "value": 21.83}], "translate": [{"x": 33.79, "y": -1.93, "curve": [0.113, 33.79, 0.223, 33.79, 0.113, -1.2, 0.223, -0.45]}, {"time": 0.3333, "x": 33.79, "y": -0.45, "curve": [0.556, 33.79, 0.778, 33.79, 0.556, -0.45, 0.778, -3.41]}, {"time": 1, "x": 33.79, "y": -3.41, "curve": [1.112, 33.79, 1.222, 33.79, 1.112, -3.41, 1.222, -2.66]}, {"time": 1.3333, "x": 33.79, "y": -1.93, "curve": [1.446, 33.79, 1.556, 33.79, 1.446, -1.2, 1.556, -0.45]}, {"time": 1.6667, "x": 33.79, "y": -0.45, "curve": [1.878, 33.79, 2.089, 33.79, 1.878, -0.45, 2.089, -3.41]}, {"time": 2.3, "x": 33.79, "y": -3.41, "curve": [2.492, 33.79, 3.91, 33.79, 2.492, -3.41, 3.91, -3.17]}, {"time": 4.1, "x": 33.79, "y": -1.93, "curve": [4.213, 33.79, 4.323, 33.79, 4.213, -1.2, 4.323, -0.45]}, {"time": 4.4333, "x": 33.79, "y": -0.45, "curve": [4.656, 33.79, 4.878, 33.79, 4.656, -0.45, 4.878, -3.41]}, {"time": 5.1, "x": 33.79, "y": -3.41, "curve": [5.212, 33.79, 5.324, 33.79, 5.212, -3.41, 5.324, -2.68]}, {"time": 5.4333, "x": 33.79, "y": -1.93}]}, "undead_knight_body2": {"scale": [{"y": 0.985, "curve": [0.113, 1, 0.223, 1, 0.113, 0.992, 0.223, 1]}, {"time": 0.3333, "curve": [0.556, 1, 0.778, 1, 0.556, 1, 0.778, 0.97]}, {"time": 1, "y": 0.97, "curve": [1.112, 1, 1.222, 1, 1.112, 0.97, 1.222, 0.978]}, {"time": 1.3333, "y": 0.985, "curve": [1.446, 1, 1.556, 1, 1.446, 0.992, 1.556, 1]}, {"time": 1.6667, "curve": [1.878, 1, 2.089, 1, 1.878, 1, 2.089, 0.97]}, {"time": 2.3, "y": 0.97, "curve": [2.492, 1, 3.91, 1, 2.492, 0.97, 3.91, 0.972]}, {"time": 4.1, "y": 0.985, "curve": [4.213, 1, 4.323, 1, 4.213, 0.992, 4.323, 1]}, {"time": 4.4333, "curve": [4.656, 1, 4.878, 1, 4.656, 1, 4.878, 0.97]}, {"time": 5.1, "y": 0.97, "curve": [5.212, 1, 5.324, 1, 5.212, 0.97, 5.324, 0.977]}, {"time": 5.4333, "y": 0.985}]}, "undead_knight_sword2": {"rotate": [{"value": 21.83, "curve": [0.113, 20.92, 0.223, 20]}, {"time": 0.3333, "value": 20, "curve": [0.556, 20, 0.778, 23.65]}, {"time": 1, "value": 23.65, "curve": [1.112, 23.65, 1.222, 22.72]}, {"time": 1.3333, "value": 21.83, "curve": [1.446, 20.92, 1.556, 20]}, {"time": 1.6667, "value": 20, "curve": [1.878, 20, 2.089, 23.65]}, {"time": 2.3, "value": 23.65, "curve": [2.492, 23.65, 3.91, 23.36]}, {"time": 4.1, "value": 21.83, "curve": [4.213, 20.92, 4.323, 20]}, {"time": 4.4333, "value": 20, "curve": [4.656, 20, 4.878, 23.65]}, {"time": 5.1, "value": 23.65, "curve": [5.212, 23.65, 5.324, 22.75]}, {"time": 5.4333, "value": 21.83}], "translate": [{"x": 33.79, "y": -1.93, "curve": [0.113, 33.79, 0.223, 33.79, 0.113, -1.2, 0.223, -0.45]}, {"time": 0.3333, "x": 33.79, "y": -0.45, "curve": [0.556, 33.79, 0.778, 33.79, 0.556, -0.45, 0.778, -3.41]}, {"time": 1, "x": 33.79, "y": -3.41, "curve": [1.112, 33.79, 1.222, 33.79, 1.112, -3.41, 1.222, -2.66]}, {"time": 1.3333, "x": 33.79, "y": -1.93, "curve": [1.446, 33.79, 1.556, 33.79, 1.446, -1.2, 1.556, -0.45]}, {"time": 1.6667, "x": 33.79, "y": -0.45, "curve": [1.878, 33.79, 2.089, 33.79, 1.878, -0.45, 2.089, -3.41]}, {"time": 2.3, "x": 33.79, "y": -3.41, "curve": [2.492, 33.79, 3.91, 33.79, 2.492, -3.41, 3.91, -3.17]}, {"time": 4.1, "x": 33.79, "y": -1.93, "curve": [4.213, 33.79, 4.323, 33.79, 4.213, -1.2, 4.323, -0.45]}, {"time": 4.4333, "x": 33.79, "y": -0.45, "curve": [4.656, 33.79, 4.878, 33.79, 4.656, -0.45, 4.878, -3.41]}, {"time": 5.1, "x": 33.79, "y": -3.41, "curve": [5.212, 33.79, 5.324, 33.79, 5.212, -3.41, 5.324, -2.68]}, {"time": 5.4333, "x": 33.79, "y": -1.93}]}, "undead_knight_body3": {"scale": [{"y": 0.985, "curve": [0.113, 1, 0.223, 1, 0.113, 0.992, 0.223, 1]}, {"time": 0.3333, "curve": [0.556, 1, 0.778, 1, 0.556, 1, 0.778, 0.97]}, {"time": 1, "y": 0.97, "curve": [1.112, 1, 1.222, 1, 1.112, 0.97, 1.222, 0.978]}, {"time": 1.3333, "y": 0.985, "curve": [1.446, 1, 1.556, 1, 1.446, 0.992, 1.556, 1]}, {"time": 1.6667, "curve": [1.878, 1, 2.089, 1, 1.878, 1, 2.089, 0.97]}, {"time": 2.3, "y": 0.97, "curve": [2.492, 1, 3.91, 1, 2.492, 0.97, 3.91, 0.972]}, {"time": 4.1, "y": 0.985, "curve": [4.213, 1, 4.323, 1, 4.213, 0.992, 4.323, 1]}, {"time": 4.4333, "curve": [4.656, 1, 4.878, 1, 4.656, 1, 4.878, 0.97]}, {"time": 5.1, "y": 0.97, "curve": [5.212, 1, 5.324, 1, 5.212, 0.97, 5.324, 0.977]}, {"time": 5.4333, "y": 0.985}]}, "undead_knight_sword3": {"rotate": [{"value": 21.83, "curve": [0.113, 20.92, 0.223, 20]}, {"time": 0.3333, "value": 20, "curve": [0.556, 20, 0.778, 23.65]}, {"time": 1, "value": 23.65, "curve": [1.112, 23.65, 1.222, 22.72]}, {"time": 1.3333, "value": 21.83, "curve": [1.446, 20.92, 1.556, 20]}, {"time": 1.6667, "value": 20, "curve": [1.878, 20, 2.089, 23.65]}, {"time": 2.3, "value": 23.65, "curve": [2.492, 23.65, 3.91, 23.36]}, {"time": 4.1, "value": 21.83, "curve": [4.213, 20.92, 4.323, 20]}, {"time": 4.4333, "value": 20, "curve": [4.656, 20, 4.878, 23.65]}, {"time": 5.1, "value": 23.65, "curve": [5.212, 23.65, 5.324, 22.75]}, {"time": 5.4333, "value": 21.83}], "translate": [{"x": 33.79, "y": -1.93, "curve": [0.113, 33.79, 0.223, 33.79, 0.113, -1.2, 0.223, -0.45]}, {"time": 0.3333, "x": 33.79, "y": -0.45, "curve": [0.556, 33.79, 0.778, 33.79, 0.556, -0.45, 0.778, -3.41]}, {"time": 1, "x": 33.79, "y": -3.41, "curve": [1.112, 33.79, 1.222, 33.79, 1.112, -3.41, 1.222, -2.66]}, {"time": 1.3333, "x": 33.79, "y": -1.93, "curve": [1.446, 33.79, 1.556, 33.79, 1.446, -1.2, 1.556, -0.45]}, {"time": 1.6667, "x": 33.79, "y": -0.45, "curve": [1.878, 33.79, 2.089, 33.79, 1.878, -0.45, 2.089, -3.41]}, {"time": 2.3, "x": 33.79, "y": -3.41, "curve": [2.492, 33.79, 3.91, 33.79, 2.492, -3.41, 3.91, -3.17]}, {"time": 4.1, "x": 33.79, "y": -1.93, "curve": [4.213, 33.79, 4.323, 33.79, 4.213, -1.2, 4.323, -0.45]}, {"time": 4.4333, "x": 33.79, "y": -0.45, "curve": [4.656, 33.79, 4.878, 33.79, 4.656, -0.45, 4.878, -3.41]}, {"time": 5.1, "x": 33.79, "y": -3.41, "curve": [5.212, 33.79, 5.324, 33.79, 5.212, -3.41, 5.324, -2.68]}, {"time": 5.4333, "x": 33.79, "y": -1.93}]}, "dwarf_skill": {"translate": [{"time": 1.4, "y": 52.64, "curve": "stepped"}, {"time": 1.9333, "y": 52.64, "curve": "stepped"}, {"time": 2.2333, "y": 52.64, "curve": "stepped"}, {"time": 2.5333, "y": 52.64}], "scale": [{"time": 1.4, "x": 2, "y": 1.3, "curve": "stepped"}, {"time": 1.9333, "x": 2, "y": 1.3, "curve": "stepped"}, {"time": 2.2333, "x": 2, "y": 1.3, "curve": "stepped"}, {"time": 2.5333, "x": 2, "y": 1.3}]}, "hero_2_eff": {"rotate": [{"time": 1.4, "value": -1.85, "curve": "stepped"}, {"time": 1.9333, "value": -1.85}, {"time": 2, "value": -109.27}, {"time": 2.0667, "value": -204.44}, {"time": 2.1333, "value": -299.59}, {"time": 2.2, "value": -379.45}, {"time": 2.2333, "value": -1.85}, {"time": 2.3, "value": -109.27}, {"time": 2.3667, "value": -204.44}, {"time": 2.4333, "value": -299.59}, {"time": 2.5, "value": -379.45}, {"time": 2.5333, "value": -1.85}, {"time": 2.6, "value": -109.27}, {"time": 2.6667, "value": -204.44}, {"time": 2.7333, "value": -299.59}, {"time": 2.8, "value": -379.45}], "scale": [{"time": 1.4, "x": -1, "curve": "stepped"}, {"time": 1.9333, "x": -1, "curve": "stepped"}, {"time": 2.2333, "x": -1, "curve": "stepped"}, {"time": 2.5333, "x": -1}]}, "hero_package_2": {"translate": [{"x": 171.83}]}, "die_skull01": {"rotate": [{"time": 3}, {"time": 3.8333, "value": -519.03}]}, "die_smoke3": {"translate": [{"x": -125}, {"time": 3}, {"time": 3.8333, "x": 77.81, "y": 20.82}], "scale": [{"time": 3}, {"time": 3.8333, "x": 1.412, "y": 1.412}]}, "die_smoke2": {"translate": [{"x": -125}, {"time": 3, "x": 13.15, "y": -93.16}, {"time": 3.8333, "x": -44.93, "y": 58.09}], "scale": [{"time": 3}, {"time": 3.8333, "x": 0.604, "y": 0.604}]}, "die_smoke": {"translate": [{"x": -125}, {"time": 3, "x": 44.93, "y": -19.73}, {"time": 3.8333, "x": -53.7, "y": 48.22}], "scale": [{"time": 3}, {"time": 3.8333, "x": 1.301, "y": 1.301}]}, "die_skull02": {"rotate": [{"time": 3}, {"time": 3.8333, "value": -454.67}]}, "die_skull03": {"rotate": [{"time": 3}, {"time": 3.8333, "value": 569.99}]}, "undead_night3": {"translate": [{"x": -141.45}]}, "undead_night2": {"translate": [{"x": -141.45}]}, "undead_night": {"translate": [{"x": -141.45}]}, "die": {"translate": [{"x": -141.45}]}, "die2": {"translate": [{"x": -141.45}]}, "die_skull04": {"rotate": [{"time": 3}, {"time": 3.8333, "value": -748.7}]}, "die_skull05": {"rotate": [{"time": 3}, {"time": 3.8333, "value": 551.44}]}, "die_smoke4": {"translate": [{"x": -125}, {"time": 3, "x": 44.93, "y": -19.73}, {"time": 3.8333, "x": -53.7, "y": 48.22}], "scale": [{"time": 3}, {"time": 3.8333, "x": 1.301, "y": 1.301}]}, "die_skull06": {"rotate": [{"time": 3}, {"time": 3.8333, "value": -602.17}]}, "die_smoke5": {"translate": [{"x": -125}, {"time": 3, "x": 13.15, "y": -93.16}, {"time": 3.8333, "x": -44.93, "y": 58.09}], "scale": [{"time": 3}, {"time": 3.8333, "x": 0.604, "y": 0.604}]}, "die_smoke6": {"translate": [{"x": -125}, {"time": 3}, {"time": 3.8333, "x": 77.81, "y": 20.82}], "scale": [{"time": 3}, {"time": 3.8333, "x": 1.412, "y": 1.412}]}, "die3": {"translate": [{"x": -141.45}]}, "die_skull07": {"rotate": [{"time": 3}, {"time": 3.8333, "value": -570.44}]}, "die_skull08": {"rotate": [{"time": 3}, {"time": 3.8333, "value": 443.14}]}, "die_smoke7": {"translate": [{"time": 3, "x": 44.93, "y": -19.73}, {"time": 3.8333, "x": -53.7, "y": 48.22}], "scale": [{"time": 3}, {"time": 3.8333, "x": 1.301, "y": 1.301}]}, "die_skull09": {"rotate": [{"time": 3}, {"time": 3.8333, "value": -515.14}]}, "die_smoke8": {"translate": [{"time": 3, "x": 13.15, "y": -93.16}, {"time": 3.8333, "x": -44.93, "y": 58.09}], "scale": [{"time": 3}, {"time": 3.8333, "x": 0.604, "y": 0.604}]}, "die_smoke9": {"translate": [{"time": 3}, {"time": 3.8333, "x": 77.81, "y": 20.82}], "scale": [{"time": 3}, {"time": 3.8333, "x": 1.412, "y": 1.412}]}}, "path": {"skull01": {"position": [{"time": 3, "curve": [3, 0.4795, 3.667, 0.9527]}, {"time": 4, "value": 0.9527}]}, "skull02": {"position": [{"time": 3, "curve": [3, 0.4795, 3.667, 0.9527]}, {"time": 4, "value": 0.9527}]}, "skull03": {"position": [{"time": 3, "curve": [3, 0.4795, 3.667, 0.9527]}, {"time": 4, "value": 0.9527}]}, "skull04": {"position": [{"time": 3, "curve": [3, 0.469, 3.667, 0.9527]}, {"time": 4, "value": 0.9527}]}, "skull05": {"position": [{"time": 3, "curve": [3, 0.469, 3.667, 0.9527]}, {"time": 4, "value": 0.9527}]}, "skull06": {"position": [{"time": 3, "curve": [3, 0.469, 3.667, 0.9527]}, {"time": 4, "value": 0.9527}]}, "skull07": {"position": [{"time": 3, "curve": [3.006, 0.4795, 3.667, 0.9527]}, {"time": 4, "value": 0.9527}]}, "skull08": {"position": [{"time": 3, "curve": [3.006, 0.4795, 3.667, 0.9527]}, {"time": 4, "value": 0.9527}]}, "skull09": {"position": [{"time": 3, "curve": [3.006, 0.4795, 3.667, 0.9527]}, {"time": 4, "value": 0.9527}]}}, "attachments": {"default": {"hero_2_axe": {"hero_2_axe": {"deform": [{"time": 1.9333}, {"time": 1.9667, "offset": 4, "vertices": [2.22782, -20.47697]}, {"time": 2}, {"time": 2.0333, "vertices": [-3.97553, -1.94442, 3.25628, 10.26339, -0.38679, 3.05309, -3.62449, -8.95572]}, {"time": 2.0667, "vertices": [8.90482, -4.55021, -8.90481, 4.55019, -4.29786, 13.5659, 13.51178, 4.4655]}, {"time": 2.1, "offset": 4, "vertices": [-4.64433, -25.56744, 3.41227, -5.22754]}, {"time": 2.1333}, {"time": 2.1667, "offset": 4, "vertices": [4.91901, 10.63145, -10.71061, -12.50643]}, {"time": 2.2, "curve": "stepped"}, {"time": 2.2333}, {"time": 2.2667, "offset": 4, "vertices": [2.22782, -20.47697]}, {"time": 2.3}, {"time": 2.3333, "vertices": [-3.97553, -1.94442, 3.25628, 10.26339, -0.38679, 3.05309, -3.62449, -8.95572]}, {"time": 2.3667, "vertices": [8.90482, -4.55021, -8.90481, 4.55019, -4.29786, 13.5659, 13.51178, 4.4655]}, {"time": 2.4, "offset": 4, "vertices": [-4.64433, -25.56744, 3.41227, -5.22754]}, {"time": 2.4333}, {"time": 2.4667, "offset": 4, "vertices": [4.91901, 10.63145, -10.71061, -12.50643]}, {"time": 2.5, "curve": "stepped"}, {"time": 2.5333}, {"time": 2.5667, "offset": 4, "vertices": [2.22782, -20.47697]}, {"time": 2.6}, {"time": 2.6333, "vertices": [-3.97553, -1.94442, 3.25628, 10.26339, -0.38679, 3.05309, -3.62449, -8.95572]}, {"time": 2.6667, "vertices": [8.90482, -4.55021, -8.90481, 4.55019, -4.29786, 13.5659, 13.51178, 4.4655]}, {"time": 2.7, "offset": 4, "vertices": [-4.64433, -25.56744, 3.41227, -5.22754]}, {"time": 2.7333}, {"time": 2.7667, "offset": 4, "vertices": [4.91901, 10.63145, -10.71061, -12.50643]}, {"time": 2.8}]}}, "hero_2_body": {"hero_2_body": {"deform": [{"curve": [0.222, 0, 0.444, 1]}, {"time": 0.6667, "vertices": [-3.88113, -0.00541, -2.41693, -0.0271, 0, 0, 0, 0, -2.41693, -0.0271, -1.53019, 0.00381, 0.97815, -2.07909, -1.37285, -2.08381], "curve": [0.889, 0, 1.111, 1]}, {"time": 1.3333, "curve": "stepped"}, {"time": 3.1667, "curve": [3.389, 0, 3.611, 1]}, {"time": 3.8333, "vertices": [-3.88113, -0.00541, -2.41693, -0.0271, 0, 0, 0, 0, -2.41693, -0.0271, -1.53019, 0.00381, 0.97815, -2.07909, -1.37285, -2.08381], "curve": [4.056, 0, 4.278, 1]}, {"time": 4.5, "curve": [4.722, 0, 4.944, 1]}, {"time": 5.1667, "vertices": [-3.88113, -0.00541, -2.41693, -0.0271, 0, 0, 0, 0, -2.41693, -0.0271, -1.53019, 0.00381, 0.97815, -2.07909, -1.37285, -2.08381], "curve": [5.389, 0, 5.611, 1]}, {"time": 5.8333, "curve": [6.056, 0, 6.278, 1]}, {"time": 6.5, "vertices": [-3.88113, -0.00541, -2.41693, -0.0271, 0, 0, 0, 0, -2.41693, -0.0271, -1.53019, 0.00381, 0.97815, -2.07909, -1.37285, -2.08381], "curve": [6.722, 0, 6.944, 1]}, {"time": 7.1667}]}}, "skull01": {"skull01": {"deform": [{"time": 3, "offset": 6, "vertices": [-42.71136, 98.56479, -42.71136, 98.56479, -42.71136, 98.56479, -126.49146, 18.07025, -126.49146, 18.07025, -126.49146, 18.07025]}]}}, "skull02": {"skull02": {"deform": [{"time": 3, "offset": 6, "vertices": [-4.80478, 82.38107, -5.04034, 78.96576, -6.72039, 75.60547]}]}}, "skull04": {"skull01": {"deform": [{"time": 3, "offset": 6, "vertices": [-42.71136, 98.56479, -42.71136, 98.56479, -42.71136, 98.56479, -126.49146, 18.07025, -126.49146, 18.07025, -126.49146, 18.07025]}]}}, "skull05": {"skull02": {"deform": [{"time": 3, "offset": 6, "vertices": [-4.80478, 82.38107, -5.04034, 78.96576, -6.72039, 75.60547]}]}}, "skull07": {"skull01": {"deform": [{"time": 3, "offset": 6, "vertices": [-42.71136, 98.56479, -42.71136, 98.56479, -42.71136, 98.56479, -126.49146, 18.07025, -126.49146, 18.07025, -126.49146, 18.07025]}]}}, "skull08": {"skull02": {"deform": [{"time": 3, "offset": 6, "vertices": [-4.80478, 82.38107, -5.04034, 78.96576, -6.72039, 75.60547]}]}}}}, "drawOrder": [{"time": 1.9333, "offsets": [{"slot": "hero_2_body", "offset": 2}, {"slot": "hero_2_hair", "offset": 2}]}, {"time": 2.0333}, {"time": 2.1333, "offsets": [{"slot": "hero_2_body", "offset": 2}, {"slot": "hero_2_hair", "offset": 2}]}, {"time": 2.2333, "offsets": [{"slot": "hero_2_body", "offset": 7}, {"slot": "hero_2_hair", "offset": 7}]}, {"time": 2.3333}, {"time": 2.4333, "offsets": [{"slot": "hero_2_body", "offset": 7}, {"slot": "hero_2_hair", "offset": 7}]}, {"time": 4.1667}], "events": [{"time": 1.9333, "name": "attack"}, {"time": 2.2333, "name": "attack"}]}, "hero_package_3": {"slots": {"archer_arrow": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6, "color": "ffffffff", "curve": "stepped"}, {"time": 4.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 6, "color": "ffffffff"}]}, "die_skull01": {"attachment": [{}]}, "die_skull02": {"attachment": [{}]}, "die_skull03": {"attachment": [{}]}, "die_skull04": {"attachment": [{}]}, "die_skull05": {"attachment": [{}]}, "die_skull06": {"attachment": [{}]}, "die_skull07": {"attachment": [{}]}, "die_skull08": {"attachment": [{}]}, "die_skull09": {"attachment": [{}]}, "die_smoke": {"attachment": [{}]}, "die_smoke2": {"attachment": [{}]}, "die_smoke3": {"attachment": [{}]}, "die_smoke4": {"attachment": [{}]}, "die_smoke5": {"attachment": [{}]}, "die_smoke6": {"attachment": [{}]}, "die_smoke7": {"attachment": [{}]}, "die_smoke8": {"attachment": [{}]}, "die_smoke9": {"attachment": [{}]}, "hero1_body": {"attachment": [{}]}, "hero1_head": {"attachment": [{}]}, "hero1_shield": {"attachment": [{}]}, "hero1_sword": {"attachment": [{}]}, "hero_2_axe": {"attachment": [{}]}, "hero_2_body": {"attachment": [{}]}, "hero_2_hair": {"attachment": [{}]}, "hero_3_arrow": {"attachment": [{}, {"time": 2}]}, "hero_3_arrow2": {"attachment": [{}, {"time": 2}]}, "hero_3_arrow3": {"attachment": [{"time": 2}]}, "hero_3_bow_attack": {"attachment": [{}, {"time": 2}]}, "hero_3_bow_idle": {"attachment": [{"name": "hero_3_bow_idle"}]}, "hero_3_bow_string1": {"attachment": [{}, {"time": 2}]}, "hero_3_bow_string2": {"attachment": [{}, {"time": 2}]}, "hero_3_bow_wingB": {"attachment": [{}, {"time": 2}]}, "hero_3_bow_wingF": {"attachment": [{}, {"time": 2}]}, "hero_3_circle_eff1": {"rgba": [{"time": 2.1667, "color": "ffffff00"}, {"time": 2.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4, "color": "ffffffff"}, {"time": 2.9333, "color": "ffffff00"}], "attachment": [{"time": 2.1667, "name": "hero_3_circle_eff"}]}, "hero_3_circle_eff2": {"rgba": [{"time": 2.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4, "color": "ffffff00"}, {"time": 2.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7, "color": "ffffffff"}, {"time": 3.1667, "color": "ffffff00"}], "attachment": [{"time": 2.1667, "name": "hero_3_circle_eff"}]}, "hero_3_circle_eff3": {"rgba": [{"time": 2.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.7333, "color": "ffffff00"}, {"time": 2.8, "color": "ffffffff", "curve": "stepped"}, {"time": 3.0667, "color": "ffffffff"}, {"time": 3.5, "color": "ffffff00"}], "attachment": [{"time": 2.1667, "name": "hero_3_circle_eff"}, {"time": 2.7333, "name": "hero_3_circle_eff"}]}, "hero_3_circle_eff4": {"rgba": [{"time": 2.1667, "color": "ffffff00"}, {"time": 2.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4, "color": "ffffffff"}, {"time": 2.9333, "color": "ffffff00"}], "attachment": [{"time": 2.1667, "name": "hero_3_circle_eff"}]}, "hero_3_circle_eff5": {"rgba": [{"time": 2.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4, "color": "ffffff00"}, {"time": 2.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7, "color": "ffffffff"}, {"time": 3.1667, "color": "ffffff00"}], "attachment": [{"time": 2.1667, "name": "hero_3_circle_eff"}]}, "hero_3_circle_eff6": {"rgba": [{"time": 2.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.7333, "color": "ffffff00"}, {"time": 2.8, "color": "ffffffff", "curve": "stepped"}, {"time": 3.0667, "color": "ffffffff"}, {"time": 3.5, "color": "ffffff00"}], "attachment": [{"time": 2.1667, "name": "hero_3_circle_eff"}, {"time": 2.7333, "name": "hero_3_circle_eff"}]}, "hero_3_leaf1": {"rgba": [{"time": 2.8333, "color": "ffffff00", "curve": [2.959, 1, 3.074, 1, 2.959, 1, 3.074, 1, 2.959, 1, 3.074, 1, 2.959, 0, 3.074, 1]}, {"time": 3.2, "color": "ffffffff", "curve": "stepped"}, {"time": 3.5667, "color": "ffffffff", "curve": [3.693, 1, 3.807, 1, 3.693, 1, 3.807, 1, 3.693, 1, 3.807, 1, 3.693, 1, 3.807, 0]}, {"time": 3.9333, "color": "ffffff00"}], "attachment": [{"time": 2.8333, "name": "hero_3_leaf1"}]}, "hero_3_leaf2": {"rgba": [{"time": 3.0667, "color": "ffffff00", "curve": [3.193, 1, 3.307, 1, 3.193, 1, 3.307, 1, 3.193, 1, 3.307, 1, 3.193, 0, 3.307, 1]}, {"time": 3.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.8, "color": "ffffffff", "curve": [3.926, 1, 4.041, 1, 3.926, 1, 4.041, 1, 3.926, 1, 4.041, 1, 3.926, 1, 4.041, 0]}, {"time": 4.1667, "color": "ffffff00"}], "attachment": [{"time": 3.0667, "name": "hero_3_leaf2"}]}, "hero_3_leaf3": {"rgba": [{"time": 3.5, "color": "ffffff00", "curve": [3.626, 1, 3.741, 0.63, 3.626, 1, 3.741, 0.89, 3.626, 1, 3.741, 0.68, 3.626, 0, 3.741, 1]}, {"time": 3.8667, "color": "a1e4aeff", "curve": "stepped"}, {"time": 4.2667, "color": "a1e4aeff", "curve": [4.385, 0.63, 4.481, 1, 4.385, 0.89, 4.481, 1, 4.385, 0.68, 4.481, 1, 4.385, 1, 4.481, 0]}, {"time": 4.6, "color": "ffffff00"}], "attachment": [{"time": 3.5, "name": "hero_3_leaf3"}]}, "hero_3_leaf4": {"rgba": [{"time": 3.7333, "color": "ffffff00", "curve": [3.859, 1, 3.974, 1, 3.859, 1, 3.974, 1, 3.859, 1, 3.974, 1, 3.859, 0, 3.974, 1]}, {"time": 4.1, "color": "ffffffff", "curve": "stepped"}, {"time": 4.4667, "color": "ffffffff", "curve": [4.593, 1, 4.707, 1, 4.593, 1, 4.707, 1, 4.593, 1, 4.707, 1, 4.593, 1, 4.707, 0]}, {"time": 4.8333, "color": "ffffff00"}], "attachment": [{"time": 3.7333, "name": "hero_3_leaf3"}]}, "hero_3_leaf5": {"rgba": [{"time": 3.2667, "color": "e6c2c200", "curve": [3.415, 0.9, 3.585, 0.9, 3.415, 0.76, 3.585, 0.76, 3.415, 0.76, 3.585, 0.76, 3.415, 0, 3.585, 1]}, {"time": 3.7333, "color": "e6c2c2ff", "curve": "stepped"}, {"time": 4.1667, "color": "e6c2c2ff", "curve": [4.241, 0.9, 4.326, 0.9, 4.241, 0.76, 4.326, 0.76, 4.241, 0.76, 4.326, 0.76, 4.241, 1, 4.326, 0]}, {"time": 4.4, "color": "e6c2c200"}], "attachment": [{"time": 3.2667, "name": "hero_3_leaf2"}]}, "hobbit_W_body": {"attachment": [{"time": 1.0667, "name": "hobbit_W_body2"}]}, "knight_body": {"attachment": [{}]}, "knight_body2": {"attachment": [{"name": "knight_body2"}]}, "knight_sword": {"attachment": [{}]}, "shadow": {"attachment": [{}]}, "shadow2": {"attachment": [{}]}, "shadow3": {"attachment": [{}]}, "shadow4": {"attachment": [{}]}, "shadow5": {"attachment": [{}, {"time": 2.1667}]}, "shadow6": {"attachment": [{}]}, "shadow7": {"attachment": [{"name": "shadow"}]}, "shadow8": {"attachment": [{}]}, "shadow9": {"attachment": [{"time": 1.0667, "name": "shadow"}]}, "shadow10": {"attachment": [{"time": 0.9333, "name": "shadow"}]}, "shadow11": {"attachment": [{"time": 4.2333, "name": "shadow"}]}, "undead_knight_body": {"attachment": [{}]}, "undead_knight_body2": {"attachment": [{}]}, "undead_knight_body3": {"attachment": [{}]}, "undead_knight_sword": {"attachment": [{}]}, "undead_knight_sword2": {"attachment": [{}]}, "undead_knight_sword3": {"attachment": [{}]}, "unit_h4_body": {"attachment": [{}]}, "unit_h4_hairB": {"attachment": [{}]}, "unit_h4_head1": {"attachment": [{}]}, "unit_h4_spellbook": {"attachment": [{}]}, "wind_eff": {"attachment": [{"time": 2.9333, "name": "wind_eff"}]}, "wind_effect": {"rgba": [{"time": 2.6667, "color": "ffffff00", "curve": [2.719, 1, 2.781, 0.72, 2.719, 1, 2.781, 0.89, 2.719, 1, 2.781, 1, 2.719, 0, 2.781, 0.47]}, {"time": 2.8333, "color": "b7e3ff78", "curve": "stepped"}, {"time": 2.9667, "color": "b7e3ff78", "curve": [3.088, 0.72, 3.246, 1, 3.088, 0.89, 3.246, 1, 3.088, 1, 3.246, 1, 3.088, 0.47, 3.246, 0]}, {"time": 3.3667, "color": "ffffff00"}], "attachment": [{"time": 2.6667, "name": "hero_2_wind_eff"}]}, "wind_effect2": {"rgba": [{"time": 2.6667, "color": "ffffff00", "curve": [2.719, 1, 2.781, 0.72, 2.719, 1, 2.781, 0.89, 2.719, 1, 2.781, 1, 2.719, 0, 2.781, 0.47]}, {"time": 2.8333, "color": "b7e3ff78", "curve": "stepped"}, {"time": 2.9667, "color": "b7e3ff78", "curve": [3.088, 0.72, 3.246, 1, 3.088, 0.89, 3.246, 1, 3.088, 1, 3.246, 1, 3.088, 0.47, 3.246, 0]}, {"time": 3.3667, "color": "ffffff00"}], "attachment": [{"time": 2.6667, "name": "hero_2_wind_eff"}]}}, "bones": {"hero_3_body": {"translate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 3.3333, "curve": "stepped"}, {"time": 4.6667, "curve": "stepped"}, {"time": 6}], "scale": [{"curve": [0.444, 1, 0.889, 0.97, 0.444, 1, 0.889, 1]}, {"time": 1.3333, "x": 0.97, "curve": [1.556, 0.97, 1.778, 1, 1.556, 1, 1.778, 1]}, {"time": 2, "curve": [2.222, 1, 2.444, 0.97, 2.222, 1, 2.444, 1]}, {"time": 2.6667, "x": 0.97, "curve": [2.889, 0.97, 3.111, 1, 2.889, 1, 3.111, 1]}, {"time": 3.3333, "curve": [3.556, 1, 3.778, 0.97, 3.556, 1, 3.778, 1]}, {"time": 4, "x": 0.97, "curve": [4.222, 0.97, 4.444, 1, 4.222, 1, 4.444, 1]}, {"time": 4.6667, "curve": [4.889, 1, 5.111, 0.97, 4.889, 1, 5.111, 1]}, {"time": 5.3333, "x": 0.97, "curve": [5.556, 0.97, 5.778, 1, 5.556, 1, 5.778, 1]}, {"time": 6}]}, "hero_3_bow_idle": {"rotate": [{"curve": [0.444, 0, 0.889, 2.15]}, {"time": 1.3333, "value": 2.15, "curve": [1.556, 2.15, 1.778, 0]}, {"time": 2, "curve": [2.222, 0, 2.444, 2.15]}, {"time": 2.6667, "value": 2.15, "curve": [2.889, 2.15, 3.111, 0]}, {"time": 3.3333, "curve": [3.556, 0, 3.778, 2.15]}, {"time": 4, "value": 2.15, "curve": [4.222, 2.15, 4.444, 0]}, {"time": 4.6667, "curve": [4.889, 0, 5.111, 2.15]}, {"time": 5.3333, "value": 2.15, "curve": [5.556, 2.15, 5.778, 0]}, {"time": 6}], "translate": [{"curve": [0.444, 0, 0.889, -0.51, 0.444, 0, 0.889, -4.18]}, {"time": 1.3333, "x": -0.51, "y": -4.18, "curve": [1.556, -0.51, 1.778, 0, 1.556, -4.18, 1.778, 0]}, {"time": 2, "curve": [2.222, 0, 2.444, -0.51, 2.222, 0, 2.444, -4.18]}, {"time": 2.6667, "x": -0.51, "y": -4.18, "curve": [2.889, -0.51, 3.111, 0, 2.889, -4.18, 3.111, 0]}, {"time": 3.3333, "curve": [3.556, 0, 3.778, -0.51, 3.556, 0, 3.778, -4.18]}, {"time": 4, "x": -0.51, "y": -4.18, "curve": [4.222, -0.51, 4.444, 0, 4.222, -4.18, 4.444, 0]}, {"time": 4.6667, "curve": [4.889, 0, 5.111, -0.51, 4.889, 0, 5.111, -4.18]}, {"time": 5.3333, "x": -0.51, "y": -4.18, "curve": [5.556, -0.51, 5.778, 0, 5.556, -4.18, 5.778, 0]}, {"time": 6}]}, "hobbitW_body": {"translate": [{"curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 2.4, "curve": "stepped"}, {"time": 3.7333, "curve": "stepped"}, {"time": 5.0667, "curve": "stepped"}, {"time": 6}], "scale": [{"y": 0.981, "curve": [0.36, 1, 0.713, 1, 0.36, 1.004, 0.713, 1]}, {"time": 1.0667, "curve": [1.289, 1, 1.511, 1, 1.289, 1, 1.511, 0.97]}, {"time": 1.7333, "y": 0.97, "curve": [1.956, 1, 2.178, 1, 1.956, 0.97, 2.178, 1]}, {"time": 2.4, "curve": [2.622, 1, 2.844, 1, 2.622, 1, 2.844, 0.97]}, {"time": 3.0667, "y": 0.97, "curve": [3.289, 1, 3.511, 1, 3.289, 0.97, 3.511, 1]}, {"time": 3.7333, "curve": [3.956, 1, 4.178, 1, 3.956, 1, 4.178, 0.97]}, {"time": 4.4, "y": 0.97, "curve": [4.622, 1, 4.844, 1, 4.622, 0.97, 4.844, 1]}, {"time": 5.0667, "curve": [5.289, 1, 5.511, 1, 5.289, 1, 5.511, 0.97]}, {"time": 5.7333, "y": 0.97, "curve": [5.823, 1, 5.913, 1, 5.823, 0.97, 5.913, 0.975]}, {"time": 6, "y": 0.981}]}, "hobbitW_sword": {"rotate": [{"value": -85.97, "curve": [0.36, -82.56, 0.713, -83.09]}, {"time": 1.0667, "value": -83.09, "curve": [1.289, -83.09, 1.511, -87.54]}, {"time": 1.7333, "value": -87.54, "curve": [1.956, -87.54, 2.178, -83.09]}, {"time": 2.4, "value": -83.09, "curve": [2.622, -83.09, 2.844, -87.54]}, {"time": 3.0667, "value": -87.54, "curve": [3.289, -87.54, 3.511, -83.09]}, {"time": 3.7333, "value": -83.09, "curve": [3.956, -83.09, 4.178, -87.54]}, {"time": 4.4, "value": -87.54, "curve": [4.622, -87.54, 4.844, -83.09]}, {"time": 5.0667, "value": -83.09, "curve": [5.289, -83.09, 5.511, -87.54]}, {"time": 5.7333, "value": -87.54, "curve": [5.823, -87.54, 5.913, -86.84]}, {"time": 6, "value": -85.97}], "translate": [{"x": 46.45, "y": -4.71, "curve": [0.36, 46.45, 0.713, 46.45, 0.36, -3.05, 0.713, -3.3]}, {"time": 1.0667, "x": 46.45, "y": -3.3, "curve": [1.289, 46.45, 1.511, 46.45, 1.289, -3.3, 1.511, -5.47]}, {"time": 1.7333, "x": 46.45, "y": -5.47, "curve": [1.956, 46.45, 2.178, 46.45, 1.956, -5.47, 2.178, -3.3]}, {"time": 2.4, "x": 46.45, "y": -3.3, "curve": [2.622, 46.45, 2.844, 46.45, 2.622, -3.3, 2.844, -5.47]}, {"time": 3.0667, "x": 46.45, "y": -5.47, "curve": [3.289, 46.45, 3.511, 46.45, 3.289, -5.47, 3.511, -3.3]}, {"time": 3.7333, "x": 46.45, "y": -3.3, "curve": [3.956, 46.45, 4.178, 46.45, 3.956, -3.3, 4.178, -5.47]}, {"time": 4.4, "x": 46.45, "y": -5.47, "curve": [4.622, 46.45, 4.844, 46.45, 4.622, -5.47, 4.844, -3.3]}, {"time": 5.0667, "x": 46.45, "y": -3.3, "curve": [5.289, 46.45, 5.511, 46.45, 5.289, -3.3, 5.511, -5.47]}, {"time": 5.7333, "x": 46.45, "y": -5.47, "curve": [5.823, 46.45, 5.913, 46.45, 5.823, -5.47, 5.913, -5.13]}, {"time": 6, "x": 46.45, "y": -4.71}]}, "archer_body": {"translate": [{"curve": "stepped"}, {"time": 0.9333, "curve": "stepped"}, {"time": 2.2667, "curve": "stepped"}, {"time": 3.6, "curve": "stepped"}, {"time": 4.9333, "curve": "stepped"}, {"time": 6}], "scale": [{"y": 0.989, "curve": [0.316, 1, 0.625, 1, 0.316, 1.009, 0.625, 1]}, {"time": 0.9333, "curve": [1.156, 1, 1.378, 1, 1.156, 1, 1.378, 0.97]}, {"time": 1.6, "y": 0.97, "curve": [1.822, 1, 2.044, 1, 1.822, 0.97, 2.044, 1]}, {"time": 2.2667, "curve": [2.489, 1, 2.711, 1, 2.489, 1, 2.711, 0.97]}, {"time": 2.9333, "y": 0.97, "curve": [3.156, 1, 3.378, 1, 3.156, 0.97, 3.378, 1]}, {"time": 3.6, "curve": [3.822, 1, 4.044, 1, 3.822, 1, 4.044, 0.97]}, {"time": 4.2667, "y": 0.97, "curve": [4.489, 1, 4.711, 1, 4.489, 0.97, 4.711, 1]}, {"time": 4.9333, "curve": [5.156, 1, 5.378, 1, 5.156, 1, 5.378, 0.97]}, {"time": 5.6, "y": 0.97, "curve": [5.734, 1, 5.868, 1, 5.734, 0.97, 5.868, 0.981]}, {"time": 6, "y": 0.989}]}, "archer_arrow": {"rotate": [{"curve": "stepped"}, {"time": 0.9333, "curve": "stepped"}, {"time": 2.2667, "curve": "stepped"}, {"time": 3.6, "curve": "stepped"}, {"time": 4.9333, "curve": "stepped"}, {"time": 6}], "translate": [{"x": -7.49, "y": 0.53, "curve": "stepped"}, {"time": 0.9333, "x": -7.49, "y": 0.53, "curve": "stepped"}, {"time": 2.2667, "x": -7.49, "y": 0.53, "curve": "stepped"}, {"time": 3.6, "x": -7.49, "y": 0.53, "curve": "stepped"}, {"time": 4.9333, "x": -7.49, "y": 0.53, "curve": "stepped"}, {"time": 6, "x": -7.49, "y": 0.53}]}, "archer_bow": {"rotate": [{"value": -1.06, "curve": [0.316, 0.94, 0.625, 0]}, {"time": 0.9333, "curve": [1.156, 0, 1.378, -3.02]}, {"time": 1.6, "value": -3.02, "curve": [1.822, -3.02, 2.044, 0]}, {"time": 2.2667, "curve": [2.489, 0, 2.711, -3.02]}, {"time": 2.9333, "value": -3.02, "curve": [3.156, -3.02, 3.378, 0]}, {"time": 3.6, "curve": [3.822, 0, 4.044, -3.02]}, {"time": 4.2667, "value": -3.02, "curve": [4.489, -3.02, 4.711, 0]}, {"time": 4.9333, "curve": [5.156, 0, 5.378, -3.02]}, {"time": 5.6, "value": -3.02, "curve": [5.734, -3.02, 5.868, -1.93]}, {"time": 6, "value": -1.06}], "translate": [{"x": -25.72, "y": -10.04, "curve": [0.316, -25.92, 0.625, -25.83, 0.316, -7.92, 0.625, -8.92]}, {"time": 0.9333, "x": -25.83, "y": -8.92, "curve": [1.156, -25.83, 1.378, -25.52, 1.156, -8.92, 1.378, -12.1]}, {"time": 1.6, "x": -25.52, "y": -12.1, "curve": [1.822, -25.52, 2.044, -25.83, 1.822, -12.1, 2.044, -8.92]}, {"time": 2.2667, "x": -25.83, "y": -8.92, "curve": [2.489, -25.83, 2.711, -25.52, 2.489, -8.92, 2.711, -12.1]}, {"time": 2.9333, "x": -25.52, "y": -12.1, "curve": [3.156, -25.52, 3.378, -25.83, 3.156, -12.1, 3.378, -8.92]}, {"time": 3.6, "x": -25.83, "y": -8.92, "curve": [3.822, -25.83, 4.044, -25.52, 3.822, -8.92, 4.044, -12.1]}, {"time": 4.2667, "x": -25.52, "y": -12.1, "curve": [4.489, -25.52, 4.711, -25.83, 4.489, -12.1, 4.711, -8.92]}, {"time": 4.9333, "x": -25.83, "y": -8.92, "curve": [5.156, -25.83, 5.378, -25.52, 5.156, -8.92, 5.378, -12.1]}, {"time": 5.6, "x": -25.52, "y": -12.1, "curve": [5.734, -25.52, 5.868, -25.63, 5.734, -12.1, 5.868, -10.96]}, {"time": 6, "x": -25.72, "y": -10.04}]}, "archer_bow2": {"rotate": [{"curve": "stepped"}, {"time": 0.9333, "curve": "stepped"}, {"time": 2.2667, "curve": "stepped"}, {"time": 3.6, "curve": "stepped"}, {"time": 4.9333, "curve": "stepped"}, {"time": 6}]}, "archer_bow3": {"rotate": [{"curve": "stepped"}, {"time": 0.9333, "curve": "stepped"}, {"time": 2.2667, "curve": "stepped"}, {"time": 3.6, "curve": "stepped"}, {"time": 4.9333, "curve": "stepped"}, {"time": 6}]}, "archer_bow4": {"rotate": [{"curve": "stepped"}, {"time": 0.9333, "curve": "stepped"}, {"time": 2.2667, "curve": "stepped"}, {"time": 3.6, "curve": "stepped"}, {"time": 4.9333, "curve": "stepped"}, {"time": 6}]}, "archer_bow5": {"rotate": [{"curve": "stepped"}, {"time": 0.9333, "curve": "stepped"}, {"time": 2.2667, "curve": "stepped"}, {"time": 3.6, "curve": "stepped"}, {"time": 4.9333, "curve": "stepped"}, {"time": 6}]}, "knight_body": {"scale": [{"y": 0.973, "curve": [0.403, 1, 0.802, 1, 0.403, 0.991, 0.802, 1]}, {"time": 1.2, "curve": [1.422, 1, 1.644, 1, 1.422, 1, 1.644, 0.97]}, {"time": 1.8667, "y": 0.97, "curve": [2.089, 1, 2.311, 1, 2.089, 0.97, 2.311, 1]}, {"time": 2.5333, "curve": [2.756, 1, 2.978, 1, 2.756, 1, 2.978, 0.97]}, {"time": 3.2, "y": 0.97, "curve": [3.422, 1, 3.644, 1, 3.422, 0.97, 3.644, 1]}, {"time": 3.8667, "curve": [4.089, 1, 4.311, 1, 4.089, 1, 4.311, 0.97]}, {"time": 4.5333, "y": 0.97, "curve": [4.756, 1, 4.978, 1, 4.756, 0.97, 4.978, 1]}, {"time": 5.2, "curve": [5.422, 1, 5.644, 1, 5.422, 1, 5.644, 0.97]}, {"time": 5.8667, "y": 0.97, "curve": [5.912, 1, 5.957, 1, 5.912, 0.97, 5.957, 0.971]}, {"time": 6, "y": 0.973}]}, "knight_sword": {"rotate": [{"value": 23.27, "curve": [0.403, 21.15, 0.802, 20]}, {"time": 1.2, "value": 20, "curve": [1.422, 20, 1.644, 23.65]}, {"time": 1.8667, "value": 23.65, "curve": [2.089, 23.65, 2.311, 20]}, {"time": 2.5333, "value": 20, "curve": [2.756, 20, 2.978, 23.65]}, {"time": 3.2, "value": 23.65, "curve": [3.422, 23.65, 3.644, 20]}, {"time": 3.8667, "value": 20, "curve": [4.089, 20, 4.311, 23.65]}, {"time": 4.5333, "value": 23.65, "curve": [4.756, 23.65, 4.978, 20]}, {"time": 5.2, "value": 20, "curve": [5.422, 20, 5.644, 23.65]}, {"time": 5.8667, "value": 23.65, "curve": [5.912, 23.65, 5.957, 23.51]}, {"time": 6, "value": 23.27}], "translate": [{"x": 33.79, "y": -3.1, "curve": [0.403, 33.79, 0.802, 33.79, 0.403, -1.39, 0.802, -0.45]}, {"time": 1.2, "x": 33.79, "y": -0.45, "curve": [1.422, 33.79, 1.644, 33.79, 1.422, -0.45, 1.644, -3.41]}, {"time": 1.8667, "x": 33.79, "y": -3.41, "curve": [2.089, 33.79, 2.311, 33.79, 2.089, -3.41, 2.311, -0.45]}, {"time": 2.5333, "x": 33.79, "y": -0.45, "curve": [2.756, 33.79, 2.978, 33.79, 2.756, -0.45, 2.978, -3.41]}, {"time": 3.2, "x": 33.79, "y": -3.41, "curve": [3.422, 33.79, 3.644, 33.79, 3.422, -3.41, 3.644, -0.45]}, {"time": 3.8667, "x": 33.79, "y": -0.45, "curve": [4.089, 33.79, 4.311, 33.79, 4.089, -0.45, 4.311, -3.41]}, {"time": 4.5333, "x": 33.79, "y": -3.41, "curve": [4.756, 33.79, 4.978, 33.79, 4.756, -3.41, 4.978, -0.45]}, {"time": 5.2, "x": 33.79, "y": -0.45, "curve": [5.422, 33.79, 5.644, 33.79, 5.422, -0.45, 5.644, -3.41]}, {"time": 5.8667, "x": 33.79, "y": -3.41, "curve": [5.912, 33.79, 5.957, 33.79, 5.912, -3.41, 5.957, -3.3]}, {"time": 6, "x": 33.79, "y": -3.1}]}, "hero_package_3": {"translate": [{"x": -387.43, "curve": [0.667, -387.43, 1.333, 48.51, 0.667, 0, 1.333, 0]}, {"time": 2, "x": 48.51, "curve": "stepped"}, {"time": 4, "x": 48.51}, {"time": 5, "x": 796.92}]}, "wind_leaf1": {"rotate": [{"time": 2.8333, "curve": [3.204, 0, 3.563, 241.55]}, {"time": 3.9333, "value": 241.55}], "translate": [{"time": 2.8333, "x": -361.09, "y": 35.14, "curve": [3.204, -361.09, 3.563, 420.21, 3.204, 35.14, 3.563, 162.52]}, {"time": 3.9333, "x": 420.21, "y": 162.52}], "scale": [{"time": 2.8333, "x": 0.709, "y": 0.709}]}, "wind_effect": {"translate": [{"time": 2.6667, "x": -124.05}], "scale": [{"time": 2.6667, "x": 1.5, "y": 1.5, "curve": [2.77, 1.5, 2.863, 3.392, 2.77, 1.5, 2.863, 3.781]}, {"time": 2.9667, "x": 3.392, "y": 3.781, "curve": [3.07, 3.392, 3.163, 2.616, 3.07, 3.781, 3.163, 2.915]}, {"time": 3.2667, "x": 2.616, "y": 2.915}]}, "wind_skill_effect": {"scale": [{"time": 2.9333, "x": 0.8, "y": 0.8}]}, "wind_skill": {"scale": [{"time": 2.8333, "x": 1.5, "y": 1.191}]}, "wind_leaf2": {"rotate": [{"time": 3.0667, "curve": [3.437, 0, 3.796, 271.94]}, {"time": 4.1667, "value": 271.94}], "translate": [{"time": 3.0667, "x": -412.23, "y": 17.2, "curve": [3.437, -412.23, 3.796, 360.87, 3.437, 17.2, 3.796, 34.02]}, {"time": 4.1667, "x": 360.87, "y": 34.02}], "scale": [{"time": 3.0667, "x": 0.678, "y": 0.678}]}, "wind_leaf5": {"rotate": [{"time": 3.2667, "curve": [3.637, 0, 4.03, 271.94]}, {"time": 4.4, "value": 271.94}], "translate": [{"time": 3.2667, "x": -412.23, "y": 17.2, "curve": [3.637, -412.23, 4.03, 254.33, 3.637, 17.2, 4.03, 152.15]}, {"time": 4.4, "x": 254.33, "y": 152.15}], "scale": [{"time": 3.2667, "x": 0.889, "y": 0.889}]}, "wind_leaf3": {"rotate": [{"time": 3.5, "curve": [3.87, 0, 4.23, 265.98]}, {"time": 4.6, "value": 265.98}], "translate": [{"time": 3.5, "x": -339.27, "y": -11.27, "curve": [3.87, -339.27, 4.23, 331.93, 3.87, -11.27, 4.23, 110.37]}, {"time": 4.6, "x": 331.93, "y": 110.37}], "scale": [{"time": 3.5, "x": 0.677, "y": 0.677}]}, "wind_leaf4": {"rotate": [{"time": 3.7333, "curve": [4.104, 0, 4.463, 265.98]}, {"time": 4.8333, "value": 265.98}], "translate": [{"time": 3.7333, "x": -345.59, "y": 28.43, "curve": [4.104, -345.59, 4.463, 227.55, 4.104, 28.43, 4.463, 76.37]}, {"time": 4.8333, "x": 227.55, "y": 76.37}], "scale": [{"time": 3.7333, "x": 0.688, "y": 0.688}]}, "hero_3_circle_eff1": {"rotate": [{"time": 2.1667}, {"time": 2.9, "value": 360.04}], "scale": [{"time": 2.1667, "x": 0.178, "y": 0.178}, {"time": 2.9, "x": 0.614, "y": 0.614}]}, "hero_3_circle_eff2": {"rotate": [{"time": 2.1667, "value": -106.1, "curve": "stepped"}, {"time": 2.4, "value": -106.1}, {"time": 3.1333, "value": 120.04}], "scale": [{"time": 2.1667, "x": 0.178, "y": 0.178, "curve": "stepped"}, {"time": 2.4, "x": 0.178, "y": 0.178}, {"time": 3.1333, "x": 0.614, "y": 0.614}]}, "hero_3_circle_eff3": {"rotate": [{"time": 2.1667, "value": -50, "curve": "stepped"}, {"time": 2.7333, "value": -50}, {"time": 3.4667, "value": 310.04}], "scale": [{"time": 2.1667, "x": 0.178, "y": 0.178, "curve": "stepped"}, {"time": 2.7333, "x": 0.178, "y": 0.178}, {"time": 3.4667, "x": 0.614, "y": 0.614}]}, "hero_3_circle_effect": {"scale": [{"time": 2.1667, "x": 1.4, "y": 1.111}, {"time": 2.5, "x": 2, "y": 1.667}, {"time": 2.8333, "x": 1.4, "y": 1.111}, {"time": 3.1667, "x": 2, "y": 1.667}, {"time": 3.5, "x": 1.4, "y": 1.111}]}, "knight_body2": {"scale": [{"curve": [0.444, 1, 0.889, 1, 0.444, 1, 0.889, 0.97]}, {"time": 1.3333, "y": 0.97, "curve": [1.556, 1, 1.778, 1, 1.556, 0.97, 1.778, 1]}, {"time": 2, "curve": [2.222, 1, 2.444, 1, 2.222, 1, 2.444, 0.97]}, {"time": 2.6667, "y": 0.97, "curve": [2.889, 1, 3.111, 1, 2.889, 0.97, 3.111, 1]}, {"time": 3.3333, "curve": [3.556, 1, 3.778, 1, 3.556, 1, 3.778, 0.97]}, {"time": 4, "y": 0.97, "curve": [4.222, 1, 4.444, 1, 4.222, 0.97, 4.444, 1]}, {"time": 4.6667, "curve": [4.889, 1, 5.111, 1, 4.889, 1, 5.111, 0.97]}, {"time": 5.3333, "y": 0.97, "curve": [5.556, 1, 5.778, 1, 5.556, 0.97, 5.778, 1]}, {"time": 6}]}, "knight_sword2": {"rotate": [{"value": 20, "curve": [0.444, 20, 0.889, 23.65]}, {"time": 1.3333, "value": 23.65, "curve": [1.556, 23.65, 1.778, 20]}, {"time": 2, "value": 20, "curve": [2.222, 20, 2.444, 23.65]}, {"time": 2.6667, "value": 23.65, "curve": [2.889, 23.65, 3.111, 20]}, {"time": 3.3333, "value": 20, "curve": [3.556, 20, 3.778, 23.65]}, {"time": 4, "value": 23.65, "curve": [4.222, 23.65, 4.444, 20]}, {"time": 4.6667, "value": 20, "curve": [4.889, 20, 5.111, 23.65]}, {"time": 5.3333, "value": 23.65, "curve": [5.556, 23.65, 5.778, 20]}, {"time": 6, "value": 20}], "translate": [{"x": 33.79, "y": -0.45, "curve": [0.444, 33.79, 0.889, 33.79, 0.444, -0.45, 0.889, -3.41]}, {"time": 1.3333, "x": 33.79, "y": -3.41, "curve": [1.556, 33.79, 1.778, 33.79, 1.556, -3.41, 1.778, -0.45]}, {"time": 2, "x": 33.79, "y": -0.45, "curve": [2.222, 33.79, 2.444, 33.79, 2.222, -0.45, 2.444, -3.41]}, {"time": 2.6667, "x": 33.79, "y": -3.41, "curve": [2.889, 33.79, 3.111, 33.79, 2.889, -3.41, 3.111, -0.45]}, {"time": 3.3333, "x": 33.79, "y": -0.45, "curve": [3.556, 33.79, 3.778, 33.79, 3.556, -0.45, 3.778, -3.41]}, {"time": 4, "x": 33.79, "y": -3.41, "curve": [4.222, 33.79, 4.444, 33.79, 4.222, -3.41, 4.444, -0.45]}, {"time": 4.6667, "x": 33.79, "y": -0.45, "curve": [4.889, 33.79, 5.111, 33.79, 4.889, -0.45, 5.111, -3.41]}, {"time": 5.3333, "x": 33.79, "y": -3.41, "curve": [5.556, 33.79, 5.778, 33.79, 5.556, -3.41, 5.778, -0.45]}, {"time": 6, "x": 33.79, "y": -0.45}]}}, "attachments": {"default": {"hero_3_body": {"hero_3_body": {"deform": [{"curve": [0.444, 0, 0.889, 1]}, {"time": 1.3333, "vertices": [-3.2113, -0.02853, 0, 0, 0, 0, -3.2113, -0.02853], "curve": [1.556, 0, 1.778, 1]}, {"time": 2, "curve": [2.222, 0, 2.444, 1]}, {"time": 2.6667, "vertices": [-3.2113, -0.02853, 0, 0, 0, 0, -3.2113, -0.02853], "curve": [2.889, 0, 3.111, 1]}, {"time": 3.3333, "curve": [3.556, 0, 3.778, 1]}, {"time": 4, "vertices": [-3.2113, -0.02853, 0, 0, 0, 0, -3.2113, -0.02853], "curve": [4.222, 0, 4.444, 1]}, {"time": 4.6667, "curve": [4.889, 0, 5.111, 1]}, {"time": 5.3333, "vertices": [-3.2113, -0.02853, 0, 0, 0, 0, -3.2113, -0.02853], "curve": [5.556, 0, 5.778, 1]}, {"time": 6}]}}, "wind_eff": {"wind_eff": {"deform": [{"time": 2.9333, "vertices": [-71.77478, 53.35057, -74.54602, 43.63486, -82.72699, 39.54449, -126.35863, -2.72372, -80, -23.17603, -67.22321, -57.85221]}, {"time": 3.5667, "vertices": [-9.27478, 53.35057, -12.04602, 43.63486, -20.22699, 39.54449, -63.85863, -2.72372, -17.5, -23.17603, -4.72321, -57.85221]}]}}}}}, "hero_package_4": {"slots": {"archer_arrow": {"attachment": [{}]}, "archer_body": {"attachment": [{}]}, "archer_bow": {"attachment": [{}]}, "die_skull01": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3.6667, "color": "ffffff00"}, {"time": 3.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.3, "color": "ffffffff"}, {"time": 4.5, "color": "ffffff00"}]}, "die_skull02": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3.6667, "color": "ffffff00"}, {"time": 3.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.3, "color": "ffffffff"}, {"time": 4.5, "color": "ffffff00"}]}, "die_skull03": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3.6667, "color": "ffffff00"}, {"time": 3.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.3, "color": "ffffffff"}, {"time": 4.5, "color": "ffffff00"}]}, "die_skull04": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3.6667, "color": "ffffff00"}, {"time": 3.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.3, "color": "ffffffff"}, {"time": 4.5, "color": "ffffff00"}]}, "die_skull05": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3.6667, "color": "ffffff00"}, {"time": 3.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.3, "color": "ffffffff"}, {"time": 4.5, "color": "ffffff00"}]}, "die_skull06": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3.6667, "color": "ffffff00"}, {"time": 3.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.3, "color": "ffffffff"}, {"time": 4.5, "color": "ffffff00"}]}, "die_skull07": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3.6667, "color": "ffffff00"}, {"time": 3.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.3, "color": "ffffffff"}, {"time": 4.4667, "color": "ffffff00"}]}, "die_skull08": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3.6667, "color": "ffffff00"}, {"time": 3.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.3, "color": "ffffffff"}, {"time": 4.4667, "color": "ffffff00"}]}, "die_skull09": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3.6667, "color": "ffffff00"}, {"time": 3.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.3, "color": "ffffffff"}, {"time": 4.4667, "color": "ffffff00"}]}, "die_smoke": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3.6667, "color": "ffffff00"}, {"time": 3.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.8333, "color": "fffffffe", "curve": "stepped"}, {"time": 4.1667, "color": "ffffffff"}, {"time": 4.4, "color": "ffffff00"}]}, "die_smoke2": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3.6667, "color": "ffffff00"}, {"time": 3.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 4.0667, "color": "ffffffff"}, {"time": 4.3333, "color": "ffffff00"}]}, "die_smoke3": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3.6667, "color": "ffffff00"}, {"time": 3.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.1333, "color": "ffffffff"}, {"time": 4.5, "color": "ffffff00"}]}, "die_smoke4": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3.6667, "color": "ffffff00"}, {"time": 3.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.8333, "color": "fffffffe", "curve": "stepped"}, {"time": 4.1667, "color": "ffffffff"}, {"time": 4.4, "color": "ffffff00"}]}, "die_smoke5": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3.6667, "color": "ffffff00"}, {"time": 3.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 4.0667, "color": "ffffffff"}, {"time": 4.3333, "color": "ffffff00"}]}, "die_smoke6": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3.6667, "color": "ffffff00"}, {"time": 3.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.1333, "color": "ffffffff"}, {"time": 4.5, "color": "ffffff00"}]}, "die_smoke7": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3.6667, "color": "ffffff00"}, {"time": 3.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.8333, "color": "fffffffe", "curve": "stepped"}, {"time": 4.1667, "color": "ffffffff"}, {"time": 4.4, "color": "ffffff00"}]}, "die_smoke8": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3.6667, "color": "ffffff00"}, {"time": 3.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 4.0667, "color": "ffffffff"}, {"time": 4.3333, "color": "ffffff00"}]}, "die_smoke9": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3.6667, "color": "ffffff00"}, {"time": 3.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.1333, "color": "ffffffff"}, {"time": 4.5, "color": "ffffff00"}]}, "hero1_body": {"attachment": [{}]}, "hero1_head": {"attachment": [{}]}, "hero1_shield": {"attachment": [{}]}, "hero1_sword": {"attachment": [{}]}, "hero_2_axe": {"attachment": [{}]}, "hero_2_body": {"attachment": [{}]}, "hero_2_hair": {"attachment": [{}]}, "hero_3_arrow": {"attachment": [{}]}, "hero_3_arrow2": {"attachment": [{}]}, "hero_3_body": {"attachment": [{}]}, "hero_3_bow_attack": {"attachment": [{}]}, "hero_3_bow_idle": {"attachment": [{}]}, "hero_3_bow_string1": {"attachment": [{}]}, "hero_3_bow_string2": {"attachment": [{}]}, "hero_3_bow_wingB": {"attachment": [{}]}, "hero_3_bow_wingF": {"attachment": [{}]}, "hobbit_W_body": {"attachment": [{}]}, "hobbit_W_sword": {"attachment": [{}]}, "knight_body": {"attachment": [{}]}, "knight_body2": {"attachment": [{}]}, "knight_sword": {"attachment": [{}]}, "knight_sword2": {"attachment": [{}]}, "shadow": {"attachment": [{}]}, "shadow2": {"rgba": [{"time": 3.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.7, "color": "ffffff00"}]}, "shadow3": {"rgba": [{"time": 3.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.7, "color": "ffffff00"}]}, "shadow4": {"rgba": [{"time": 3.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.7, "color": "ffffff00"}]}, "shadow5": {"attachment": [{}]}, "shadow6": {"attachment": [{}]}, "shadow7": {"attachment": [{}]}, "shadow8": {"attachment": [{"name": "shadow"}]}, "shadow9": {"attachment": [{}]}, "shadow10": {"attachment": [{}]}, "shadow11": {"attachment": [{}]}, "undead_knight_body": {"rgba": [{"time": 3.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.7, "color": "ffffff00"}]}, "undead_knight_body2": {"rgba": [{"time": 3.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.7, "color": "ffffff00"}]}, "undead_knight_body3": {"rgba": [{"time": 3.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.7, "color": "ffffff00"}]}, "undead_knight_sword": {"rgba": [{"time": 3.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.7, "color": "ffffff00"}]}, "undead_knight_sword2": {"rgba": [{"time": 3.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.7, "color": "ffffff00"}]}, "undead_knight_sword3": {"rgba": [{"time": 3.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.7, "color": "ffffff00"}]}, "unit_h4_attack": {"rgba": [{"time": 3.8, "color": "ffffffff"}, {"time": 3.9667, "color": "ffffff00"}], "attachment": [{"time": 3.0333, "name": "unit_h4_attack"}, {"time": 3.2667, "name": "unit_h4_attack"}, {"time": 3.3333}, {"time": 3.4, "name": "unit_h4_attack1"}, {"time": 3.4667}, {"time": 3.5333, "name": "unit_h4_attack2"}, {"time": 3.6}, {"time": 3.6667, "name": "unit_h4_attack4"}, {"time": 3.7333}, {"time": 3.8, "name": "unit_h4_attack"}, {"time": 3.8667}, {"time": 3.9333, "name": "unit_h4_attack1"}]}, "unit_h4_attack_c": {"rgba": [{"time": 2.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1667, "color": "ffffffff"}, {"time": 3.3, "color": "ffffff00"}], "attachment": [{"time": 0.5333, "name": "unit_h4_attack_c0"}, {"time": 2.7333, "name": "unit_h4_attack_c0"}]}, "unit_h4_attack_c2": {"rgba": [{"time": 2.9667, "color": "ffffffff"}, {"time": 3.0333, "color": "ffffff00"}], "attachment": [{"time": 1.3667, "name": "unit_h4_attack_c0"}, {"time": 2.7333, "name": "unit_h4_attack_c0"}]}, "unit_h4_attack_c3": {"rgba": [{"time": 2.9667, "color": "ffffffff"}, {"time": 3.0333, "color": "ffffff00"}], "attachment": [{"time": 2.1667, "name": "unit_h4_attack_c0"}, {"time": 2.7333, "name": "unit_h4_attack_c0"}]}, "unit_h4_attack_ch1": {"rgba": [{"time": 0.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9667, "color": "ffffff00"}, {"time": 3.1667, "color": "ffffffff"}, {"time": 3.3, "color": "ffffff00"}], "attachment": [{"time": 0.5333, "name": "unit_h4_attack_c2"}, {"time": 2.7333}, {"time": 2.9, "name": "unit_h4_attack_c2"}]}, "unit_h4_attack_ch2": {"rgba": [{"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00", "curve": "stepped"}, {"time": 2.7333, "color": "ffffffff"}], "attachment": [{"time": 0.5333, "name": "unit_h4_attack_c2"}, {"time": 2.7333}]}, "unit_h4_attack_ch3": {"rgba": [{"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.7333, "color": "ffffffff"}], "attachment": [{"time": 0.5333, "name": "unit_h4_attack_c2"}, {"time": 2.7333}]}, "unit_h4_body_shine": {"rgba": [{"time": 3.9333, "color": "ffffffff"}, {"time": 4.1333, "color": "ffffff00"}], "attachment": [{"time": 3.2667, "name": "unit_h4_body_shine"}, {"time": 3.3333}, {"time": 3.4, "name": "unit_h4_body_shine"}, {"time": 3.4667}, {"time": 3.5333, "name": "unit_h4_body_shine"}, {"time": 3.6}, {"time": 3.6667, "name": "unit_h4_body_shine"}, {"time": 3.7333}, {"time": 3.8, "name": "unit_h4_body_shine"}, {"time": 3.8667}, {"time": 3.9333, "name": "unit_h4_body_shine"}, {"time": 4.0333}, {"time": 4.1333, "name": "unit_h4_body_shine"}]}, "unit_h4_eye": {"attachment": [{"time": 3.2667, "name": "unit_h4_eye"}, {"time": 4.3667}]}, "unit_h4_hairB": {"attachment": [{"time": 3.2667}, {"time": 4.3667, "name": "unit_h4_hairB"}]}, "unit_h4_head1": {"attachment": [{"time": 3.2667, "name": "unit_h4_head2"}, {"time": 4.3667, "name": "unit_h4_head1"}]}, "unit_h4_lightning": {"attachment": [{"time": 3.6667, "name": "unit_h4_lightning01"}, {"time": 3.7, "name": "unit_h4_lightning02"}, {"time": 3.7333, "name": "unit_h4_lightning03"}, {"time": 3.8}, {"time": 3.8667, "name": "unit_h4_lightning04"}, {"time": 3.9333}, {"time": 3.9667, "name": "unit_h4_lightning05"}, {"time": 4.0333, "name": "unit_h4_lightning06"}, {"time": 4.0667, "name": "unit_h4_lightning07"}, {"time": 4.1333, "name": "unit_h4_lightning08"}, {"time": 4.2}, {"time": 4.6667}]}, "unit_h4_skill_ready1": {"rgba": [{"time": 0.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6333, "color": "ffffffff"}, {"time": 1.2, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0333, "color": "ffffff00"}], "attachment": [{"time": 0.5333, "name": "unit_h4_skill_ready_1"}, {"time": 0.6333, "name": "unit_h4_skill_ready_11"}, {"time": 0.6667}, {"time": 0.7333, "name": "unit_h4_skill_ready_11"}]}, "unit_h4_skill_ready2": {"rgba": [{"time": 1.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4667, "color": "ffffffff"}, {"time": 2.0333, "color": "ffffff00"}], "attachment": [{"time": 1.3667, "name": "unit_h4_skill_ready_2"}, {"time": 1.4667, "name": "unit_h4_skill_ready_21"}, {"time": 1.5}, {"time": 1.5667, "name": "unit_h4_skill_ready_21"}]}, "unit_h4_skill_ready3": {"rgba": [{"time": 2.2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3, "color": "ffffffff"}, {"time": 2.8667, "color": "ffffff00"}], "attachment": [{"time": 2.2, "name": "unit_h4_skill_ready_3"}, {"time": 2.3, "name": "unit_h4_skill_ready_31"}, {"time": 2.3333}, {"time": 2.4, "name": "unit_h4_skill_ready_31"}]}, "unit_h4_skill_ready_eff1": {"rgba": [{"time": 0.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6333, "color": "ffffffff", "curve": [0.718, 1, 0.815, 1, 0.718, 1, 0.815, 1, 0.718, 1, 0.815, 1, 0.718, 1, 0.815, 0]}, {"time": 0.9, "color": "ffffff00", "curve": [0.955, 1, 1.011, 1, 0.955, 1, 1.011, 1, 0.955, 1, 1.011, 1, 0.955, 0, 1.011, 1]}, {"time": 1.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2, "color": "ffffffff", "curve": [1.285, 1, 1.348, 1, 1.285, 1, 1.348, 1, 1.285, 1, 1.348, 1, 1.285, 1, 1.348, 0]}, {"time": 1.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7333, "color": "ffffffff", "curve": [1.818, 1, 1.915, 1, 1.818, 1, 1.915, 1, 1.818, 1, 1.915, 1, 1.818, 1, 1.915, 0]}, {"time": 2, "color": "ffffff00"}, {"time": 2.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2667, "color": "ffffffff", "curve": [2.352, 1, 2.448, 1, 2.352, 1, 2.448, 1, 2.352, 1, 2.448, 1, 2.352, 1, 2.448, 0]}, {"time": 2.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.7, "color": "ffffff00", "curve": "stepped"}, {"time": 2.7333, "color": "ffffffff"}], "attachment": [{"time": 0.5333, "name": "unit_h4_attack_c1"}, {"time": 2.7333, "name": "unit_h4_attack_c1"}, {"time": 2.9667}]}, "unit_h4_skill_ready_eff2": {"rgba": [{"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4667, "color": "ffffffff", "curve": [1.552, 1, 1.648, 1, 1.552, 1, 1.648, 1, 1.552, 1, 1.648, 1, 1.552, 1, 1.648, 0]}, {"time": 1.7333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.9, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": [2.085, 1, 2.182, 1, 2.085, 1, 2.182, 1, 2.085, 1, 2.182, 1, 2.085, 1, 2.182, 0]}, {"time": 2.2667, "color": "ffffff00"}, {"time": 2.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7333, "color": "ffffffff"}], "attachment": [{"time": 0.5333, "name": "unit_h4_attack_c1"}, {"time": 2.7333, "name": "unit_h4_attack_c1"}, {"time": 2.9667}]}, "unit_h4_skill_ready_eff3": {"rgba": [{"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2667, "color": "ffffffff", "curve": [2.352, 1, 2.448, 1, 2.352, 1, 2.448, 1, 2.352, 1, 2.448, 1, 2.352, 1, 2.448, 0]}, {"time": 2.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.7, "color": "ffffff00", "curve": [2.761, 1, 2.722, 1, 2.761, 1, 2.722, 1, 2.761, 1, 2.722, 1, 2.761, 0, 2.722, 1]}, {"time": 2.7333, "color": "ffffffff"}], "attachment": [{"time": 0.5333, "name": "unit_h4_attack_c1"}, {"time": 2.7333, "name": "unit_h4_attack_c1"}, {"time": 2.9667}]}, "unit_h4_spellbook": {"attachment": [{"time": 2.7, "name": "unit_h4_spellbook"}, {"time": 3.2}, {"time": 4.2667, "name": "unit_h4_spellbook"}]}, "unit_h4_spellbookB": {"attachment": [{"time": 2.7}, {"time": 3.2, "name": "unit_h4_spellbookB"}, {"time": 4.2667}]}, "unit_h4_spellbookF": {"attachment": [{"time": 2.7}, {"time": 3.2, "name": "unit_h4_spellbookF"}, {"time": 4.2667}]}, "unit_h4_spellbook_paper1": {"rgba": [{"time": 3.4333, "color": "ffffffff"}, {"time": 3.5, "color": "ffffff00", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.7667, "color": "ffffffff"}, {"time": 3.8333, "color": "ffffff00"}], "attachment": [{"time": 2.7}, {"time": 3.2, "name": "unit_h4_spellbook_paper"}, {"time": 4.2667}]}, "unit_h4_spellbook_paper2": {"rgba": [{"time": 3.6, "color": "ffffffff"}, {"time": 3.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.9333, "color": "ffffffff"}, {"time": 4, "color": "ffffff00"}], "attachment": [{"time": 2.7}, {"time": 3.2, "name": "unit_h4_spellbook_paper"}, {"time": 4.2667}]}, "unit_h4_spellbook_shine": {"rgba": [{"time": 2.7, "color": "ffffff00", "curve": "stepped"}, {"time": 3.2, "color": "ffffff00"}, {"time": 3.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}, {"time": 3.4, "color": "ffffff00"}, {"time": 3.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.5333, "color": "ffffffff"}, {"time": 3.6, "color": "ffffff00"}, {"time": 3.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.8, "color": "ffffffff"}, {"time": 3.8667, "color": "ffffff00"}, {"time": 3.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 4, "color": "ffffffff"}, {"time": 4.0667, "color": "ffffff00"}], "attachment": [{"time": 2.7, "name": "unit_h4_spellbook_shine"}, {"time": 3.2, "name": "unit_h4_spellbook_shine"}]}}, "bones": {"undead_knight_body": {"scale": [{"y": 0.985, "curve": [0.113, 1, 0.223, 1, 0.113, 0.992, 0.223, 1]}, {"time": 0.3333, "curve": [0.556, 1, 0.778, 1, 0.556, 1, 0.778, 0.97]}, {"time": 1, "y": 0.97, "curve": [1.112, 1, 1.222, 1, 1.112, 0.97, 1.222, 0.978]}, {"time": 1.3333, "y": 0.985, "curve": [1.446, 1, 1.556, 1, 1.446, 0.992, 1.556, 1]}, {"time": 1.6667, "curve": [1.889, 1, 2.111, 1, 1.889, 1, 2.111, 0.97]}, {"time": 2.3333, "y": 0.97, "curve": [2.445, 1, 2.556, 1, 2.445, 0.97, 2.556, 0.978]}, {"time": 2.6667, "y": 0.985, "curve": [2.779, 1, 2.89, 1, 2.779, 0.992, 2.89, 1]}, {"time": 3, "curve": [3.222, 1, 3.444, 1, 3.222, 1, 3.444, 0.97]}, {"time": 3.6667, "y": 0.97, "curve": [3.779, 1, 3.889, 1, 3.779, 0.97, 3.889, 0.978]}, {"time": 4, "y": 0.985, "curve": [4.113, 1, 4.223, 1, 4.113, 0.992, 4.223, 1]}, {"time": 4.3333, "curve": [4.556, 1, 4.778, 1, 4.556, 1, 4.778, 0.97]}, {"time": 5, "y": 0.97, "curve": [5.112, 1, 5.224, 1, 5.112, 0.97, 5.224, 0.977]}, {"time": 5.3333, "y": 0.985}]}, "undead_knight_sword": {"rotate": [{"value": 21.83, "curve": [0.113, 20.92, 0.223, 20]}, {"time": 0.3333, "value": 20, "curve": [0.556, 20, 0.778, 23.65]}, {"time": 1, "value": 23.65, "curve": [1.112, 23.65, 1.222, 22.72]}, {"time": 1.3333, "value": 21.83, "curve": [1.446, 20.92, 1.556, 20]}, {"time": 1.6667, "value": 20, "curve": [1.889, 20, 2.111, 23.65]}, {"time": 2.3333, "value": 23.65, "curve": [2.445, 23.65, 2.556, 22.72]}, {"time": 2.6667, "value": 21.83, "curve": [2.779, 20.92, 2.89, 20]}, {"time": 3, "value": 20, "curve": [3.222, 20, 3.444, 23.65]}, {"time": 3.6667, "value": 23.65, "curve": [3.779, 23.65, 3.889, 22.72]}, {"time": 4, "value": 21.83, "curve": [4.113, 20.92, 4.223, 20]}, {"time": 4.3333, "value": 20, "curve": [4.556, 20, 4.778, 23.65]}, {"time": 5, "value": 23.65, "curve": [5.112, 23.65, 5.224, 22.75]}, {"time": 5.3333, "value": 21.83}], "translate": [{"x": 33.79, "y": -1.93, "curve": [0.113, 33.79, 0.223, 33.79, 0.113, -1.2, 0.223, -0.45]}, {"time": 0.3333, "x": 33.79, "y": -0.45, "curve": [0.556, 33.79, 0.778, 33.79, 0.556, -0.45, 0.778, -3.41]}, {"time": 1, "x": 33.79, "y": -3.41, "curve": [1.112, 33.79, 1.222, 33.79, 1.112, -3.41, 1.222, -2.66]}, {"time": 1.3333, "x": 33.79, "y": -1.93, "curve": [1.446, 33.79, 1.556, 33.79, 1.446, -1.2, 1.556, -0.45]}, {"time": 1.6667, "x": 33.79, "y": -0.45, "curve": [1.889, 33.79, 2.111, 33.79, 1.889, -0.45, 2.111, -3.41]}, {"time": 2.3333, "x": 33.79, "y": -3.41, "curve": [2.445, 33.79, 2.556, 33.79, 2.445, -3.41, 2.556, -2.66]}, {"time": 2.6667, "x": 33.79, "y": -1.93, "curve": [2.779, 33.79, 2.89, 33.79, 2.779, -1.2, 2.89, -0.45]}, {"time": 3, "x": 33.79, "y": -0.45, "curve": [3.222, 33.79, 3.444, 33.79, 3.222, -0.45, 3.444, -3.41]}, {"time": 3.6667, "x": 33.79, "y": -3.41, "curve": [3.779, 33.79, 3.889, 33.79, 3.779, -3.41, 3.889, -2.66]}, {"time": 4, "x": 33.79, "y": -1.93, "curve": [4.113, 33.79, 4.223, 33.79, 4.113, -1.2, 4.223, -0.45]}, {"time": 4.3333, "x": 33.79, "y": -0.45, "curve": [4.556, 33.79, 4.778, 33.79, 4.556, -0.45, 4.778, -3.41]}, {"time": 5, "x": 33.79, "y": -3.41, "curve": [5.112, 33.79, 5.224, 33.79, 5.112, -3.41, 5.224, -2.68]}, {"time": 5.3333, "x": 33.79, "y": -1.93}]}, "undead_knight_body2": {"scale": [{"y": 0.985, "curve": [0.113, 1, 0.223, 1, 0.113, 0.992, 0.223, 1]}, {"time": 0.3333, "curve": [0.556, 1, 0.778, 1, 0.556, 1, 0.778, 0.97]}, {"time": 1, "y": 0.97, "curve": [1.112, 1, 1.222, 1, 1.112, 0.97, 1.222, 0.978]}, {"time": 1.3333, "y": 0.985, "curve": [1.446, 1, 1.556, 1, 1.446, 0.992, 1.556, 1]}, {"time": 1.6667, "curve": [1.889, 1, 2.111, 1, 1.889, 1, 2.111, 0.97]}, {"time": 2.3333, "y": 0.97, "curve": [2.445, 1, 2.556, 1, 2.445, 0.97, 2.556, 0.978]}, {"time": 2.6667, "y": 0.985, "curve": [2.779, 1, 2.89, 1, 2.779, 0.992, 2.89, 1]}, {"time": 3, "curve": [3.222, 1, 3.444, 1, 3.222, 1, 3.444, 0.97]}, {"time": 3.6667, "y": 0.97, "curve": [3.779, 1, 3.889, 1, 3.779, 0.97, 3.889, 0.978]}, {"time": 4, "y": 0.985, "curve": [4.113, 1, 4.223, 1, 4.113, 0.992, 4.223, 1]}, {"time": 4.3333, "curve": [4.556, 1, 4.778, 1, 4.556, 1, 4.778, 0.97]}, {"time": 5, "y": 0.97, "curve": [5.112, 1, 5.224, 1, 5.112, 0.97, 5.224, 0.977]}, {"time": 5.3333, "y": 0.985}]}, "undead_knight_sword2": {"rotate": [{"value": 21.83, "curve": [0.113, 20.92, 0.223, 20]}, {"time": 0.3333, "value": 20, "curve": [0.556, 20, 0.778, 23.65]}, {"time": 1, "value": 23.65, "curve": [1.112, 23.65, 1.222, 22.72]}, {"time": 1.3333, "value": 21.83, "curve": [1.446, 20.92, 1.556, 20]}, {"time": 1.6667, "value": 20, "curve": [1.889, 20, 2.111, 23.65]}, {"time": 2.3333, "value": 23.65, "curve": [2.445, 23.65, 2.556, 22.72]}, {"time": 2.6667, "value": 21.83, "curve": [2.779, 20.92, 2.89, 20]}, {"time": 3, "value": 20, "curve": [3.222, 20, 3.444, 23.65]}, {"time": 3.6667, "value": 23.65, "curve": [3.779, 23.65, 3.889, 22.72]}, {"time": 4, "value": 21.83, "curve": [4.113, 20.92, 4.223, 20]}, {"time": 4.3333, "value": 20, "curve": [4.556, 20, 4.778, 23.65]}, {"time": 5, "value": 23.65, "curve": [5.112, 23.65, 5.224, 22.75]}, {"time": 5.3333, "value": 21.83}], "translate": [{"x": 33.79, "y": -1.93, "curve": [0.113, 33.79, 0.223, 33.79, 0.113, -1.2, 0.223, -0.45]}, {"time": 0.3333, "x": 33.79, "y": -0.45, "curve": [0.556, 33.79, 0.778, 33.79, 0.556, -0.45, 0.778, -3.41]}, {"time": 1, "x": 33.79, "y": -3.41, "curve": [1.112, 33.79, 1.222, 33.79, 1.112, -3.41, 1.222, -2.66]}, {"time": 1.3333, "x": 33.79, "y": -1.93, "curve": [1.446, 33.79, 1.556, 33.79, 1.446, -1.2, 1.556, -0.45]}, {"time": 1.6667, "x": 33.79, "y": -0.45, "curve": [1.889, 33.79, 2.111, 33.79, 1.889, -0.45, 2.111, -3.41]}, {"time": 2.3333, "x": 33.79, "y": -3.41, "curve": [2.445, 33.79, 2.556, 33.79, 2.445, -3.41, 2.556, -2.66]}, {"time": 2.6667, "x": 33.79, "y": -1.93, "curve": [2.779, 33.79, 2.89, 33.79, 2.779, -1.2, 2.89, -0.45]}, {"time": 3, "x": 33.79, "y": -0.45, "curve": [3.222, 33.79, 3.444, 33.79, 3.222, -0.45, 3.444, -3.41]}, {"time": 3.6667, "x": 33.79, "y": -3.41, "curve": [3.779, 33.79, 3.889, 33.79, 3.779, -3.41, 3.889, -2.66]}, {"time": 4, "x": 33.79, "y": -1.93, "curve": [4.113, 33.79, 4.223, 33.79, 4.113, -1.2, 4.223, -0.45]}, {"time": 4.3333, "x": 33.79, "y": -0.45, "curve": [4.556, 33.79, 4.778, 33.79, 4.556, -0.45, 4.778, -3.41]}, {"time": 5, "x": 33.79, "y": -3.41, "curve": [5.112, 33.79, 5.224, 33.79, 5.112, -3.41, 5.224, -2.68]}, {"time": 5.3333, "x": 33.79, "y": -1.93}]}, "undead_knight_body3": {"scale": [{"y": 0.985, "curve": [0.113, 1, 0.223, 1, 0.113, 0.992, 0.223, 1]}, {"time": 0.3333, "curve": [0.556, 1, 0.778, 1, 0.556, 1, 0.778, 0.97]}, {"time": 1, "y": 0.97, "curve": [1.112, 1, 1.222, 1, 1.112, 0.97, 1.222, 0.978]}, {"time": 1.3333, "y": 0.985, "curve": [1.446, 1, 1.556, 1, 1.446, 0.992, 1.556, 1]}, {"time": 1.6667, "curve": [1.889, 1, 2.111, 1, 1.889, 1, 2.111, 0.97]}, {"time": 2.3333, "y": 0.97, "curve": [2.445, 1, 2.556, 1, 2.445, 0.97, 2.556, 0.978]}, {"time": 2.6667, "y": 0.985, "curve": [2.779, 1, 2.89, 1, 2.779, 0.992, 2.89, 1]}, {"time": 3, "curve": [3.222, 1, 3.444, 1, 3.222, 1, 3.444, 0.97]}, {"time": 3.6667, "y": 0.97, "curve": [3.779, 1, 3.889, 1, 3.779, 0.97, 3.889, 0.978]}, {"time": 4, "y": 0.985, "curve": [4.113, 1, 4.223, 1, 4.113, 0.992, 4.223, 1]}, {"time": 4.3333, "curve": [4.556, 1, 4.778, 1, 4.556, 1, 4.778, 0.97]}, {"time": 5, "y": 0.97, "curve": [5.112, 1, 5.224, 1, 5.112, 0.97, 5.224, 0.977]}, {"time": 5.3333, "y": 0.985}]}, "undead_knight_sword3": {"rotate": [{"value": 21.83, "curve": [0.113, 20.92, 0.223, 20]}, {"time": 0.3333, "value": 20, "curve": [0.556, 20, 0.778, 23.65]}, {"time": 1, "value": 23.65, "curve": [1.112, 23.65, 1.222, 22.72]}, {"time": 1.3333, "value": 21.83, "curve": [1.446, 20.92, 1.556, 20]}, {"time": 1.6667, "value": 20, "curve": [1.889, 20, 2.111, 23.65]}, {"time": 2.3333, "value": 23.65, "curve": [2.445, 23.65, 2.556, 22.72]}, {"time": 2.6667, "value": 21.83, "curve": [2.779, 20.92, 2.89, 20]}, {"time": 3, "value": 20, "curve": [3.222, 20, 3.444, 23.65]}, {"time": 3.6667, "value": 23.65, "curve": [3.779, 23.65, 3.889, 22.72]}, {"time": 4, "value": 21.83, "curve": [4.113, 20.92, 4.223, 20]}, {"time": 4.3333, "value": 20, "curve": [4.556, 20, 4.778, 23.65]}, {"time": 5, "value": 23.65, "curve": [5.112, 23.65, 5.224, 22.75]}, {"time": 5.3333, "value": 21.83}], "translate": [{"x": 33.79, "y": -1.93, "curve": [0.113, 33.79, 0.223, 33.79, 0.113, -1.2, 0.223, -0.45]}, {"time": 0.3333, "x": 33.79, "y": -0.45, "curve": [0.556, 33.79, 0.778, 33.79, 0.556, -0.45, 0.778, -3.41]}, {"time": 1, "x": 33.79, "y": -3.41, "curve": [1.112, 33.79, 1.222, 33.79, 1.112, -3.41, 1.222, -2.66]}, {"time": 1.3333, "x": 33.79, "y": -1.93, "curve": [1.446, 33.79, 1.556, 33.79, 1.446, -1.2, 1.556, -0.45]}, {"time": 1.6667, "x": 33.79, "y": -0.45, "curve": [1.889, 33.79, 2.111, 33.79, 1.889, -0.45, 2.111, -3.41]}, {"time": 2.3333, "x": 33.79, "y": -3.41, "curve": [2.445, 33.79, 2.556, 33.79, 2.445, -3.41, 2.556, -2.66]}, {"time": 2.6667, "x": 33.79, "y": -1.93, "curve": [2.779, 33.79, 2.89, 33.79, 2.779, -1.2, 2.89, -0.45]}, {"time": 3, "x": 33.79, "y": -0.45, "curve": [3.222, 33.79, 3.444, 33.79, 3.222, -0.45, 3.444, -3.41]}, {"time": 3.6667, "x": 33.79, "y": -3.41, "curve": [3.779, 33.79, 3.889, 33.79, 3.779, -3.41, 3.889, -2.66]}, {"time": 4, "x": 33.79, "y": -1.93, "curve": [4.113, 33.79, 4.223, 33.79, 4.113, -1.2, 4.223, -0.45]}, {"time": 4.3333, "x": 33.79, "y": -0.45, "curve": [4.556, 33.79, 4.778, 33.79, 4.556, -0.45, 4.778, -3.41]}, {"time": 5, "x": 33.79, "y": -3.41, "curve": [5.112, 33.79, 5.224, 33.79, 5.112, -3.41, 5.224, -2.68]}, {"time": 5.3333, "x": 33.79, "y": -1.93}]}, "unit_h4": {"translate": [{"x": 118.12}]}, "unit_h4_body": {"rotate": [{"time": 2.7}, {"time": 3.0333, "value": 0.89, "curve": "stepped"}, {"time": 3.1333, "value": 0.89}, {"time": 3.2667, "value": -0.97}, {"time": 3.4}], "scale": [{"curve": [0.222, 1, 0.444, 0.913, 0.222, 1, 0.444, 1]}, {"time": 0.6667, "x": 0.913, "curve": [0.889, 0.913, 1.111, 1, 0.889, 1, 1.111, 1]}, {"time": 1.3333, "curve": [1.556, 1, 1.778, 0.913, 1.556, 1, 1.778, 1]}, {"time": 2, "x": 0.913, "curve": [2.222, 0.913, 2.444, 1, 2.222, 1, 2.444, 1]}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.7}, {"time": 3.0333, "x": 1.029, "curve": "stepped"}, {"time": 3.1333, "x": 1.029}, {"time": 3.2667, "x": 0.957}, {"time": 3.4, "curve": "stepped"}, {"time": 4.4, "curve": [4.622, 1, 4.844, 0.913, 4.622, 1, 4.844, 1]}, {"time": 5.0667, "x": 0.913, "curve": [5.289, 0.913, 5.511, 1, 5.289, 1, 5.511, 1]}, {"time": 5.7333, "curve": [5.956, 1, 6.178, 0.913, 5.956, 1, 6.178, 1]}, {"time": 6.4, "x": 0.913, "curve": [6.622, 0.913, 6.844, 1, 6.622, 1, 6.844, 1]}, {"time": 7.0667}]}, "unit_h4_head": {"rotate": [{"curve": [0.222, 0, 0.444, -1.02]}, {"time": 0.6667, "value": -1.02, "curve": [0.889, -1.02, 1.111, 0]}, {"time": 1.3333, "curve": [1.556, 0, 1.778, -1.02]}, {"time": 2, "value": -1.02, "curve": [2.222, -1.02, 2.444, 0]}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.7, "curve": [2.811, 0, 2.922, 2.89]}, {"time": 3.0333, "value": 2.89, "curve": "stepped"}, {"time": 3.1333, "value": 2.89, "curve": [3.178, 2.89, 3.222, -1.45]}, {"time": 3.2667, "value": -1.45, "curve": [3.289, -1.45, 3.311, 0]}, {"time": 3.3333, "curve": "stepped"}, {"time": 4.4, "curve": [4.622, 0, 4.844, -1.02]}, {"time": 5.0667, "value": -1.02, "curve": [5.289, -1.02, 5.511, 0]}, {"time": 5.7333, "curve": [5.956, 0, 6.178, -1.02]}, {"time": 6.4, "value": -1.02, "curve": [6.622, -1.02, 6.844, 0]}, {"time": 7.0667}], "translate": [{"curve": [0.222, 0, 0.444, -0.77, 0.222, 0, 0.444, -0.01]}, {"time": 0.6667, "x": -0.77, "y": -0.01, "curve": [0.889, -0.77, 1.111, 0, 0.889, -0.01, 1.111, 0]}, {"time": 1.3333, "curve": [1.556, 0, 1.778, -0.77, 1.556, 0, 1.778, -0.01]}, {"time": 2, "x": -0.77, "y": -0.01, "curve": [2.222, -0.77, 2.444, 0, 2.222, -0.01, 2.444, 0]}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.7, "curve": "stepped"}, {"time": 3.1333, "curve": [3.156, 0, 3.178, -5.45, 3.156, 0, 3.178, 2.25]}, {"time": 3.2, "x": -5.45, "y": 2.25, "curve": [3.222, -5.45, 3.244, -2.93, 3.222, 2.25, 3.244, 2.35]}, {"time": 3.2667, "x": -2.93, "y": 2.35, "curve": [3.289, -2.93, 3.311, -3.04, 3.289, 2.35, 3.311, 1.95]}, {"time": 3.3333, "x": -3.04, "y": 1.95, "curve": [3.356, -3.04, 3.378, -5.03, 3.356, 1.95, 3.378, 1.5]}, {"time": 3.4, "x": -5.03, "y": 1.5, "curve": [3.422, -5.03, 3.444, -3.04, 3.422, 1.5, 3.444, 1.95]}, {"time": 3.4667, "x": -3.04, "y": 1.95, "curve": [3.489, -3.04, 3.511, -5.03, 3.489, 1.95, 3.511, 1.5]}, {"time": 3.5333, "x": -5.03, "y": 1.5, "curve": [3.556, -5.03, 3.578, -3.04, 3.556, 1.5, 3.578, 1.95]}, {"time": 3.6, "x": -3.04, "y": 1.95, "curve": [3.622, -3.04, 3.644, -5.03, 3.622, 1.95, 3.644, 1.5]}, {"time": 3.6667, "x": -5.03, "y": 1.5, "curve": [3.689, -5.03, 3.711, -3.04, 3.689, 1.5, 3.711, 1.95]}, {"time": 3.7333, "x": -3.04, "y": 1.95, "curve": [3.756, -3.04, 3.778, -5.03, 3.756, 1.95, 3.778, 1.5]}, {"time": 3.8, "x": -5.03, "y": 1.5, "curve": [3.822, -5.03, 3.844, -3.04, 3.822, 1.5, 3.844, 1.95]}, {"time": 3.8667, "x": -3.04, "y": 1.95, "curve": [3.889, -3.04, 3.911, -5.03, 3.889, 1.95, 3.911, 1.5]}, {"time": 3.9333, "x": -5.03, "y": 1.5, "curve": [3.956, -5.03, 3.978, -3.04, 3.956, 1.5, 3.978, 1.95]}, {"time": 4, "x": -3.04, "y": 1.95, "curve": [4.022, -3.04, 4.044, -5.03, 4.022, 1.95, 4.044, 1.5]}, {"time": 4.0667, "x": -5.03, "y": 1.5, "curve": [4.089, -5.03, 4.111, -3.04, 4.089, 1.5, 4.111, 1.53]}, {"time": 4.1333, "x": -3.04, "y": 1.53, "curve": "stepped"}, {"time": 4.3667, "x": -3.04, "y": 1.53, "curve": [4.378, -3.04, 4.389, 0, 4.378, 1.53, 4.389, 0]}, {"time": 4.4, "curve": [4.622, 0, 4.844, -0.77, 4.622, 0, 4.844, -0.01]}, {"time": 5.0667, "x": -0.77, "y": -0.01, "curve": [5.289, -0.77, 5.511, 0, 5.289, -0.01, 5.511, 0]}, {"time": 5.7333, "curve": [5.956, 0, 6.178, -0.77, 5.956, 0, 6.178, -0.01]}, {"time": 6.4, "x": -0.77, "y": -0.01, "curve": [6.622, -0.77, 6.844, 0, 6.622, -0.01, 6.844, 0]}, {"time": 7.0667}], "scale": [{"curve": [0.222, 1, 0.444, 1.078, 0.222, 1, 0.444, 1]}, {"time": 0.6667, "x": 1.078, "curve": [0.889, 1.078, 1.111, 1, 0.889, 1, 1.111, 1]}, {"time": 1.3333, "curve": [1.556, 1, 1.778, 1.078, 1.556, 1, 1.778, 1]}, {"time": 2, "x": 1.078, "curve": [2.222, 1.078, 2.444, 1, 2.222, 1, 2.444, 1]}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.7, "curve": [2.811, 1, 2.922, 0.97, 2.811, 1, 2.922, 1]}, {"time": 3.0333, "x": 0.97, "curve": "stepped"}, {"time": 3.1333, "x": 0.97, "curve": [3.178, 0.97, 3.222, 1.046, 3.178, 1, 3.222, 1]}, {"time": 3.2667, "x": 1.046, "curve": [3.289, 1.046, 3.311, 1, 3.289, 1, 3.311, 1]}, {"time": 3.3333, "curve": "stepped"}, {"time": 4.4, "curve": [4.622, 1, 4.844, 1.078, 4.622, 1, 4.844, 1]}, {"time": 5.0667, "x": 1.078, "curve": [5.289, 1.078, 5.511, 1, 5.289, 1, 5.511, 1]}, {"time": 5.7333, "curve": [5.956, 1, 6.178, 1.078, 5.956, 1, 6.178, 1]}, {"time": 6.4, "x": 1.078, "curve": [6.622, 1.078, 6.844, 1, 6.622, 1, 6.844, 1]}, {"time": 7.0667}]}, "unit_h4_spellbook1": {"rotate": [{"value": 0.92, "curve": [0.022, 0.97, 0.044, 1]}, {"time": 0.0667, "value": 1, "curve": [0.289, 1, 0.511, -2]}, {"time": 0.7333, "value": -2, "curve": [0.933, -2, 1.133, 0.43]}, {"time": 1.3333, "value": 0.92, "curve": [1.356, 0.97, 1.378, 1]}, {"time": 1.4, "value": 1, "curve": [1.622, 1, 1.844, -2]}, {"time": 2.0667, "value": -2, "curve": [2.267, -2, 2.467, 0.43]}, {"time": 2.6667, "value": 0.92, "curve": "stepped"}, {"time": 2.7, "value": 0.92, "curve": "stepped"}, {"time": 3.1333, "value": 0.92}, {"time": 3.2667, "value": -0.47}, {"time": 4.4, "value": 0.92, "curve": [4.422, 0.97, 4.444, 1]}, {"time": 4.4667, "value": 1, "curve": [4.689, 1, 4.911, -2]}, {"time": 5.1333, "value": -2, "curve": [5.333, -2, 5.533, 0.43]}, {"time": 5.7333, "value": 0.92, "curve": [5.756, 0.97, 5.778, 1]}, {"time": 5.8, "value": 1, "curve": [6.022, 1, 6.244, -2]}, {"time": 6.4667, "value": -2, "curve": [6.667, -2, 6.867, 0.43]}, {"time": 7.0667, "value": 0.92}], "translate": [{"x": -0.03, "y": -0.12, "curve": [0.022, -0.01, 0.044, 0, 0.022, -0.04, 0.044, 0]}, {"time": 0.0667, "curve": [0.289, 0, 0.511, -1.19, 0.289, 0, 0.511, -5.4]}, {"time": 0.7333, "x": -1.19, "y": -5.4, "curve": [0.933, -1.19, 1.133, -0.23, 0.933, -5.4, 1.133, -0.84]}, {"time": 1.3333, "x": -0.03, "y": -0.12, "curve": [1.356, -0.01, 1.378, 0, 1.356, -0.04, 1.378, 0]}, {"time": 1.4, "curve": [1.622, 0, 1.844, -1.19, 1.622, 0, 1.844, -5.4]}, {"time": 2.0667, "x": -1.19, "y": -5.4, "curve": [2.267, -1.19, 2.467, -0.23, 2.267, -5.4, 2.467, -0.84]}, {"time": 2.6667, "x": -0.03, "y": -0.12, "curve": "stepped"}, {"time": 2.7, "x": -0.03, "y": -0.12, "curve": "stepped"}, {"time": 3.1333, "x": -0.03, "y": -0.12}, {"time": 3.2, "x": 51.21, "y": 20.72, "curve": "stepped"}, {"time": 4.2, "x": 51.21, "y": 20.72}, {"time": 4.3667, "x": -0.03, "y": -0.12, "curve": "stepped"}, {"time": 4.4, "x": -0.03, "y": -0.12, "curve": [4.422, -0.01, 4.444, 0, 4.422, -0.04, 4.444, 0]}, {"time": 4.4667, "curve": [4.689, 0, 4.911, -1.19, 4.689, 0, 4.911, -5.4]}, {"time": 5.1333, "x": -1.19, "y": -5.4, "curve": [5.333, -1.19, 5.533, -0.23, 5.333, -5.4, 5.533, -0.84]}, {"time": 5.7333, "x": -0.03, "y": -0.12, "curve": [5.756, -0.01, 5.778, 0, 5.756, -0.04, 5.778, 0]}, {"time": 5.8, "curve": [6.022, 0, 6.244, -1.19, 6.022, 0, 6.244, -5.4]}, {"time": 6.4667, "x": -1.19, "y": -5.4, "curve": [6.667, -1.19, 6.867, -0.23, 6.667, -5.4, 6.867, -0.84]}, {"time": 7.0667, "x": -0.03, "y": -0.12}]}, "unit_h4_spellbook2": {"rotate": [{"time": 2.7, "value": -9.05}, {"time": 3.2, "value": -0.26}], "translate": [{"time": 2.7, "x": 36.08, "y": 10.55}]}, "unit_h4_spellbookB": {"translate": [{"time": 2.7, "x": 9.26, "y": 9.29, "curve": "stepped"}, {"time": 3.2, "x": 9.26, "y": 9.29}, {"time": 3.2667, "x": -6.37, "y": -4.06}, {"time": 3.3667, "x": 2.19, "y": 0.39, "curve": "stepped"}, {"time": 4.2, "x": 2.19, "y": 0.39}, {"time": 4.2667, "x": 9.26, "y": 9.29}]}, "unit_h4_spellbookF": {"translate": [{"time": 2.7, "x": 9.02, "y": -7.41, "curve": "stepped"}, {"time": 3.2, "x": 9.02, "y": -7.41}, {"time": 3.2667, "x": -6.78, "y": 1.22}, {"time": 3.3667, "x": 2.19, "y": 0.39, "curve": "stepped"}, {"time": 4.2, "x": 2.19, "y": 0.39}, {"time": 4.2667, "x": 9.02, "y": -7.41}]}, "unit_h4_spellbook_paper1": {"translate": [{"time": 2.7, "x": -23.89, "y": 26.45}, {"time": 3.2, "x": -23.6, "y": 12.53}, {"time": 3.3, "x": -23.89, "y": 26.45}, {"time": 3.3667, "x": 2.38, "y": 9.83}, {"time": 3.4667, "x": -14.18, "y": -22.85, "curve": "stepped"}, {"time": 3.6333, "x": -23.89, "y": 26.45}, {"time": 3.7, "x": 2.38, "y": 9.83}, {"time": 3.8, "x": -14.18, "y": -22.85}]}, "unit_h4_spellbook_paper2": {"translate": [{"time": 2.7, "x": -23.89, "y": 26.45}, {"time": 3.2, "x": -23.6, "y": 12.53}, {"time": 3.4667, "x": -23.89, "y": 26.45}, {"time": 3.5333, "x": 2.38, "y": 9.83}, {"time": 3.6333, "x": -14.18, "y": -22.85, "curve": "stepped"}, {"time": 3.8, "x": -23.89, "y": 26.45}, {"time": 3.8667, "x": 2.38, "y": 9.83}, {"time": 3.9667, "x": -14.18, "y": -22.85}]}, "unit_h4_eye": {"scale": [{"time": 3.2667}, {"time": 3.3333, "x": 1.1, "y": 1.1}, {"time": 3.4}, {"time": 3.4667, "x": 1.1, "y": 1.1}, {"time": 3.5333}, {"time": 3.6, "x": 1.1, "y": 1.1}, {"time": 3.6667}, {"time": 3.7333, "x": 1.1, "y": 1.1}, {"time": 3.8}, {"time": 3.8667, "x": 1.1, "y": 1.1}, {"time": 3.9333}, {"time": 4, "x": 1.1, "y": 1.1}, {"time": 4.1}, {"time": 4.2, "x": 1.1, "y": 1.1}]}, "unit_h4_attack_eff": {"rotate": [{"time": 3.0333, "value": 90, "curve": "stepped"}, {"time": 3.2667, "value": 90}], "translate": [{"time": 3.0333, "x": 84.74, "y": 49.17, "curve": "stepped"}, {"time": 3.2667, "x": 84.74, "y": 49.17}], "scale": [{"time": 3.0333, "x": 0.175, "y": 1.1, "curve": "stepped"}, {"time": 3.2667, "x": 0.175, "y": 1.1}, {"time": 3.3333, "x": 1.278, "y": 1.5}]}, "die_smoke": {"translate": [{"time": 3.6667, "x": 44.93, "y": -19.73}, {"time": 4.5, "x": -53.7, "y": 48.22}], "scale": [{"time": 3.6667}, {"time": 4.5, "x": 1.301, "y": 1.301}]}, "die_smoke2": {"translate": [{"time": 3.6667, "x": 13.15, "y": -93.16}, {"time": 4.5, "x": -44.93, "y": 58.09}], "scale": [{"time": 3.6667}, {"time": 4.5, "x": 0.604, "y": 0.604}]}, "die_smoke3": {"translate": [{"time": 3.6667}, {"time": 4.5, "x": 77.81, "y": 20.82}], "scale": [{"time": 3.6667}, {"time": 4.5, "x": 1.412, "y": 1.412}]}, "die_skull01": {"rotate": [{"time": 3.6667}, {"time": 4.5, "value": -519.03}]}, "die_skull02": {"rotate": [{"time": 3.6667}, {"time": 4.5, "value": -454.67}]}, "die_skull03": {"rotate": [{"time": 3.6667}, {"time": 4.5, "value": 569.99}]}, "die_smoke4": {"translate": [{"time": 3.6667, "x": 44.93, "y": -19.73}, {"time": 4.5, "x": -53.7, "y": 48.22}], "scale": [{"time": 3.6667}, {"time": 4.5, "x": 1.301, "y": 1.301}]}, "die_smoke5": {"translate": [{"time": 3.6667, "x": 13.15, "y": -93.16}, {"time": 4.5, "x": -44.93, "y": 58.09}], "scale": [{"time": 3.6667}, {"time": 4.5, "x": 0.604, "y": 0.604}]}, "die_smoke6": {"translate": [{"time": 3.6667}, {"time": 4.5, "x": 77.81, "y": 20.82}], "scale": [{"time": 3.6667}, {"time": 4.5, "x": 1.412, "y": 1.412}]}, "die_skull04": {"rotate": [{"time": 3.6667}, {"time": 4.5, "value": -748.7}]}, "die_skull05": {"rotate": [{"time": 3.6667}, {"time": 4.5, "value": 551.44}]}, "die_skull06": {"rotate": [{"time": 3.6667}, {"time": 4.5, "value": -602.17}]}, "die_smoke7": {"translate": [{"time": 3.6667, "x": 44.93, "y": -19.73}, {"time": 4.5, "x": -53.7, "y": 48.22}], "scale": [{"time": 3.6667}, {"time": 4.5, "x": 1.301, "y": 1.301}]}, "die_smoke8": {"translate": [{"time": 3.6667, "x": 13.15, "y": -93.16}, {"time": 4.5, "x": -44.93, "y": 58.09}], "scale": [{"time": 3.6667}, {"time": 4.5, "x": 0.604, "y": 0.604}]}, "die_smoke9": {"translate": [{"time": 3.6667}, {"time": 4.5, "x": 77.81, "y": 20.82}], "scale": [{"time": 3.6667}, {"time": 4.5, "x": 1.412, "y": 1.412}]}, "die_skull07": {"rotate": [{"time": 3.6667}, {"time": 4.5, "value": -570.44}]}, "die_skull08": {"rotate": [{"time": 3.6667}, {"time": 4.5, "value": 443.14}]}, "die_skull09": {"rotate": [{"time": 3.6667}, {"time": 4.5, "value": -515.14}]}, "undead_night": {"translate": [{"x": -95.91}]}, "undead_night2": {"translate": [{"x": -95.91}]}, "undead_night3": {"translate": [{"x": -95.91}]}, "unit_h4_skill_ready_eff": {"translate": [{"time": 0.5333, "x": 7, "y": 70}], "scale": [{"time": 0.5333, "x": 1.05, "y": 1.2}]}, "unit_h4_lightning_eff": {"translate": [{"time": 3.6667, "x": 354.39}], "scale": [{"time": 3.6667, "x": 1.3, "y": 1.3}]}, "unit_h4_attack_hit_eff1": {"rotate": [{"time": 0.5333, "curve": [1.236, 0, 1.936, -3119.14]}, {"time": 2.6333, "value": -4041.88, "curve": [2.664, -4081.53, 2.621, 0]}, {"time": 2.7333}], "translate": [{"time": 0.5333, "x": -95, "y": 115, "curve": [0.739, -95, 0.961, -95, 0.739, 115, 0.961, 100]}, {"time": 1.1667, "x": -95, "y": 100, "curve": [1.372, -95, 1.561, -95, 1.372, 100, 1.561, 115]}, {"time": 1.7667, "x": -95, "y": 115, "curve": [1.972, -95, 2.194, -95, 1.972, 115, 2.194, 100]}, {"time": 2.4, "x": -95, "y": 100, "curve": [2.481, -95, 2.556, -95, 2.481, 100, 2.556, 102.31]}, {"time": 2.6333, "x": -95, "y": 105.13, "curve": [2.664, -95, 2.69, -95, 2.664, 106.23, 2.69, 115]}, {"time": 2.7333, "x": -95, "y": 115, "curve": [2.782, -95, 2.9, -35.3, 2.782, 115, 2.9, 76.2]}, {"time": 2.9, "x": 5, "y": 50}], "scale": [{"time": 0.5333, "x": 0.46, "y": 0.449, "curve": "stepped"}, {"time": 2.7333, "x": 0.46, "y": 0.449}, {"time": 2.9667, "x": 0.875, "y": 0.875}]}, "unit_h4_attack_hit_eff2": {"rotate": [{"time": 0.5333, "curve": "stepped"}, {"time": 2.7333}], "translate": [{"time": 0.5333, "x": 91.54, "y": 120.45, "curve": [0.739, 91.54, 0.961, 91.54, 0.739, 120.45, 0.961, 136.45]}, {"time": 1.1667, "x": 91.54, "y": 136.45, "curve": [1.372, 91.54, 1.561, 91.54, 1.372, 136.45, 1.561, 120.45]}, {"time": 1.7667, "x": 91.54, "y": 120.45, "curve": [1.972, 91.54, 2.194, 91.54, 1.972, 120.45, 2.194, 136.45]}, {"time": 2.4, "x": 91.54, "y": 136.45, "curve": [2.512, 91.54, 2.622, 91.54, 2.512, 136.45, 2.622, 120.45]}, {"time": 2.7333, "x": 91.54, "y": 120.45, "curve": [2.782, 91.54, 2.9, 40.47, 2.782, 120.45, 2.9, 78.39]}, {"time": 2.9, "x": 6, "y": 50}], "scale": [{"time": 1.3667, "x": 0.333, "y": 0.325, "curve": "stepped"}, {"time": 2.7333, "x": 0.333, "y": 0.325}, {"time": 2.9667, "x": 0.875, "y": 0.875}]}, "unit_h4_attack_hit_eff3": {"rotate": [{"time": 0.5333, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 2.7333}], "translate": [{"time": 0.5333, "x": -49.29, "y": 23.05, "curve": [0.643, -49.29, 0.757, -49.29, 0.643, 23.05, 0.757, 15.05]}, {"time": 0.8667, "x": -49.29, "y": 15.05, "curve": [1.072, -49.29, 1.261, -49.29, 1.072, 15.05, 1.261, 30.05]}, {"time": 1.4667, "x": -49.29, "y": 30.05, "curve": [1.672, -49.29, 1.894, -49.29, 1.672, 30.05, 1.894, 15.05]}, {"time": 2.1, "x": -49.29, "y": 15.05, "curve": [2.276, -49.29, 2.458, -49.29, 2.276, 15.05, 2.458, 25.88]}, {"time": 2.6333, "x": -49.29, "y": 29.08, "curve": [2.664, -49.29, 2.726, -49.29, 2.664, 29.64, 2.726, 23.05]}, {"time": 2.7333, "x": -49.29, "y": 23.05, "curve": [2.782, -49.29, 2.9, -16.28, 2.782, 23.05, 2.9, 39.14]}, {"time": 2.9, "x": 6, "y": 50}], "scale": [{"time": 2.1667, "x": 0.56, "y": 0.546, "curve": "stepped"}, {"time": 2.7333, "x": 0.56, "y": 0.546}, {"time": 2.9667, "x": 0.875, "y": 0.875}]}, "unit_h4_skill_ready_eff1": {"scale": [{"time": 0.5333, "curve": [0.649, 1, 0.751, 2.031, 0.649, 1, 0.751, 2.1]}, {"time": 0.8667, "x": 2.031, "y": 2.1, "curve": [0.927, 2.031, 1, 1, 0.927, 2.1, 1, 1]}, {"time": 1.0667, "curve": [1.182, 1, 1.318, 2.031, 1.182, 1, 1.318, 2.1]}, {"time": 1.4333, "x": 2.031, "y": 2.1, "curve": "stepped"}, {"time": 1.6, "x": 2.031, "y": 2.1, "curve": "stepped"}, {"time": 1.6333, "curve": [1.749, 1, 1.851, 2.031, 1.749, 1, 1.851, 2.1]}, {"time": 1.9667, "x": 2.031, "y": 2.1, "curve": [2.027, 2.031, 2.1, 1, 2.027, 2.1, 2.1, 1]}, {"time": 2.1667, "curve": [2.282, 1, 2.418, 2.031, 2.282, 1, 2.418, 2.1]}, {"time": 2.5333, "x": 2.031, "y": 2.1, "curve": "stepped"}, {"time": 2.7, "x": 2.031, "y": 2.1, "curve": "stepped"}, {"time": 2.7333}]}, "unit_h4_skill_ready_eff2": {"scale": [{"time": 1.3667, "curve": [1.482, 1, 1.585, 2.031, 1.482, 1, 1.585, 2.1]}, {"time": 1.7, "x": 2.031, "y": 2.1, "curve": "stepped"}, {"time": 1.8667, "x": 2.031, "y": 2.1, "curve": "stepped"}, {"time": 1.9, "curve": [2.015, 1, 2.118, 2.031, 2.015, 1, 2.118, 2.1]}, {"time": 2.2333, "x": 2.031, "y": 2.1, "curve": [2.294, 2.031, 2.367, 1, 2.294, 2.1, 2.367, 1]}, {"time": 2.4333, "curve": "stepped"}, {"time": 2.7333}]}, "unit_h4_skill_ready_eff3": {"scale": [{"time": 0.5333, "x": 2.031, "y": 2.1, "curve": "stepped"}, {"time": 2.1667, "curve": [2.282, 1, 2.418, 2.031, 2.282, 1, 2.418, 2.1]}, {"time": 2.5333, "x": 2.031, "y": 2.1, "curve": "stepped"}, {"time": 2.6333, "x": 2.031, "y": 2.1, "curve": "stepped"}, {"time": 2.7, "x": 2.031, "y": 2.1, "curve": [2.767, 2.031, 2.722, 1, 2.767, 2.1, 2.722, 1]}, {"time": 2.7333}]}, "die": {"translate": [{"x": -95.91}]}, "die2": {"translate": [{"x": -95.91}]}, "die3": {"translate": [{"x": -95.91}]}, "uniit_h4_effects": {"translate": [{"x": 117.11}]}}, "path": {"skull01": {"position": [{"time": 3.6667, "curve": [3.667, 0.4795, 4.333, 0.9527]}, {"time": 4.6667, "value": 0.9527}]}, "skull02": {"position": [{"time": 3.6667, "curve": [3.667, 0.4795, 4.333, 0.9527]}, {"time": 4.6667, "value": 0.9527}]}, "skull03": {"position": [{"time": 3.6667, "curve": [3.667, 0.4795, 4.333, 0.9527]}, {"time": 4.6667, "value": 0.9527}]}, "skull04": {"position": [{"time": 3.6667, "curve": [3.667, 0.469, 4.333, 0.9527]}, {"time": 4.6667, "value": 0.9527}]}, "skull05": {"position": [{"time": 3.6667, "curve": [3.667, 0.469, 4.333, 0.9527]}, {"time": 4.6667, "value": 0.9527}]}, "skull06": {"position": [{"time": 3.6667, "curve": [3.667, 0.469, 4.333, 0.9527]}, {"time": 4.6667, "value": 0.9527}]}, "skull07": {"position": [{"time": 3.6667, "curve": [3.672, 0.4795, 4.333, 0.9527]}, {"time": 4.6667, "value": 0.9527}]}, "skull08": {"position": [{"time": 3.6667, "curve": [3.672, 0.4795, 4.333, 0.9527]}, {"time": 4.6667, "value": 0.9527}]}, "skull09": {"position": [{"time": 3.6667, "curve": [3.672, 0.4795, 4.333, 0.9527]}, {"time": 4.6667, "value": 0.9527}]}}, "attachments": {"default": {"skull01": {"skull01": {"deform": [{"time": 3.6667, "offset": 6, "vertices": [-42.71136, 98.56479, -42.71136, 98.56479, -42.71136, 98.56479, -126.49146, 18.07025, -126.49146, 18.07025, -126.49146, 18.07025]}]}}, "skull02": {"skull02": {"deform": [{"time": 3.6667, "offset": 6, "vertices": [-4.80478, 82.38107, -5.04034, 78.96576, -6.72039, 75.60547]}]}}, "skull04": {"skull01": {"deform": [{"time": 3.6667, "offset": 6, "vertices": [-42.71136, 98.56479, -42.71136, 98.56479, -42.71136, 98.56479, -126.49146, 18.07025, -126.49146, 18.07025, -126.49146, 18.07025]}]}}, "skull05": {"skull02": {"deform": [{"time": 3.6667, "offset": 6, "vertices": [-4.80478, 82.38107, -5.04034, 78.96576, -6.72039, 75.60547]}]}}, "skull07": {"skull01": {"deform": [{"time": 3.6667, "offset": 6, "vertices": [-42.71136, 98.56479, -42.71136, 98.56479, -42.71136, 98.56479, -126.49146, 18.07025, -126.49146, 18.07025, -126.49146, 18.07025]}]}}, "skull08": {"skull02": {"deform": [{"time": 3.6667, "offset": 6, "vertices": [-4.80478, 82.38107, -5.04034, 78.96576, -6.72039, 75.60547]}]}}, "unit_h4_attack_ch1": {"unit_h4_attack_c2": {"deform": [{"time": 0.5333, "vertices": [-9.6828, 9.58694, 9.6828, 9.58694, 9.6828, -9.58694, -9.6828, -9.58694]}, {"time": 0.6, "vertices": [82.81931, -81.99924, -82.81909, -81.99908, -82.81914, 81.99933, 82.81929, 81.99927]}, {"time": 2.7333, "vertices": [-9.6828, 9.58694, 9.6828, 9.58694, 9.6828, -9.58694, -9.6828, -9.58694]}, {"time": 2.8, "vertices": [82.81931, -81.99924, -82.81909, -81.99908, -82.81914, 81.99933, 82.81929, 81.99927]}, {"time": 2.9, "vertices": [53.52702, -52.99698, -53.52681, -52.99684, -53.52686, 52.99707, 53.52699, 52.99699]}, {"time": 2.9667, "vertices": [100.59169, -99.59578, -100.59156, -99.59557, -100.59163, 99.59578, 100.59166, 99.59567]}]}}, "unit_h4_attack_ch2": {"unit_h4_attack_c2": {"deform": [{"time": 1.3667, "vertices": [-3.36407, 3.33081, 3.36401, 3.33081, 3.36404, -3.33075, -3.36404, -3.33081]}, {"time": 1.4, "vertices": [103.79721, -102.76965, -103.79776, -102.76956, -103.79779, 102.76984, 103.7977, 102.76944]}, {"time": 2.7333, "vertices": [-3.36407, 3.33081, 3.36401, 3.33081, 3.36404, -3.33075, -3.36404, -3.33081]}, {"time": 2.8, "vertices": [103.79721, -102.76965, -103.79776, -102.76956, -103.79779, 102.76984, 103.7977, 102.76944]}]}}, "unit_h4_attack_ch3": {"unit_h4_attack_c2": {"deform": [{"time": 2.1667}, {"time": 2.2333, "vertices": [97.34763, -96.38382, -97.34769, -96.38382, -97.34766, 96.38385, 97.34767, 96.38384]}, {"time": 2.7333}, {"time": 2.8, "vertices": [97.34763, -96.38382, -97.34769, -96.38382, -97.34766, 96.38385, 97.34767, 96.38384]}]}}, "unit_h4_head1": {"unit_h4_head2": {"deform": [{"time": 3.2667, "curve": [3.289, 0, 3.311, 1]}, {"time": 3.3333, "vertices": [6.92415, 0.07916, 0, 0, 0, 0, 0, 0, 0, 0, 6.92415, 0.07916, 6.92415, 0.07916, 6.92415, 0.07916, 6.92415, 0.07916, 6.92415, 0.07916, 6.92415, 0.07916], "curve": [3.356, 0, 3.378, 1]}, {"time": 3.4, "curve": [3.422, 0, 3.444, 1]}, {"time": 3.4667, "vertices": [6.92415, 0.07916, 0, 0, 0, 0, 0, 0, 0, 0, 6.92415, 0.07916, 6.92415, 0.07916, 6.92415, 0.07916, 6.92415, 0.07916, 6.92415, 0.07916, 6.92415, 0.07916], "curve": [3.489, 0, 3.511, 1]}, {"time": 3.5333, "curve": [3.556, 0, 3.578, 1]}, {"time": 3.6, "vertices": [6.92415, 0.07916, 0, 0, 0, 0, 0, 0, 0, 0, 6.92415, 0.07916, 6.92415, 0.07916, 6.92415, 0.07916, 6.92415, 0.07916, 6.92415, 0.07916, 6.92415, 0.07916], "curve": [3.622, 0, 3.644, 1]}, {"time": 3.6667, "curve": [3.689, 0, 3.711, 1]}, {"time": 3.7333, "vertices": [6.92415, 0.07916, 0, 0, 0, 0, 0, 0, 0, 0, 6.92415, 0.07916, 6.92415, 0.07916, 6.92415, 0.07916, 6.92415, 0.07916, 6.92415, 0.07916, 6.92415, 0.07916], "curve": [3.756, 0, 3.778, 1]}, {"time": 3.8, "curve": [3.822, 0, 3.844, 1]}, {"time": 3.8667, "vertices": [6.92415, 0.07916, 0, 0, 0, 0, 0, 0, 0, 0, 6.92415, 0.07916, 6.92415, 0.07916, 6.92415, 0.07916, 6.92415, 0.07916, 6.92415, 0.07916, 6.92415, 0.07916], "curve": [3.889, 0, 3.911, 1]}, {"time": 3.9333, "curve": [3.956, 0, 3.978, 1]}, {"time": 4, "vertices": [3.48736, 0.03984, 0, 0, 0, 0, 0, 0, 0, 0, 3.48736, 0.03984, 3.48736, 0.03984, 3.48736, 0.03984, 3.48736, 0.03984, 3.48736, 0.03984, 3.48736, 0.03984], "curve": [4.022, 0, 4.044, 1]}, {"time": 4.0667, "curve": [4.089, 0, 4.111, 1]}, {"time": 4.1333, "vertices": [3.48736, 0.03984, 0, 0, 0, 0, 0, 0, 0, 0, 3.48736, 0.03984, 3.48736, 0.03984, 3.48736, 0.03984, 3.48736, 0.03984, 3.48736, 0.03984, 3.48736, 0.03984], "curve": "stepped"}, {"time": 4.2667, "vertices": [3.48736, 0.03984, 0, 0, 0, 0, 0, 0, 0, 0, 3.48736, 0.03984, 3.48736, 0.03984, 3.48736, 0.03984, 3.48736, 0.03984, 3.48736, 0.03984, 3.48736, 0.03984], "curve": [4.3, 0, 4.333, 1]}, {"time": 4.3667, "vertices": [-6.99043, -0.07989, 0, 0, 0, 0, 0, 0, 0, 0, -6.99043, -0.07989, -18.5327, -0.21183, -18.5327, -0.21183, -6.99043, -0.07989, -6.99043, -0.07989, -6.99043, -0.07989]}]}}, "unit_h4_lightning": {"unit_h4_lightning04": {"deform": [{"time": 3.9, "vertices": [-39.63327, 0, -39.63327, 0, -39.63327, 0, -39.63327, 0, -39.63327, 0, -39.63327, 0, -39.63327]}]}}, "unit_h4_spellbook_shine": {"unit_h4_spellbook_shine": {"deform": [{"time": 3.2}, {"time": 3.2667, "vertices": [-4.12197, -9.29437, -4.42687, 9.15311, 4.12196, 9.2944, 4.42688, -9.15309]}, {"time": 3.3667, "vertices": [2.17228, 3.58789, 2.31142, -4.82999, -1.58956, -4.89447, -1.7287, 3.52342]}]}}}}, "drawOrder": [{"time": 2.4667, "offsets": [{"slot": "hero_2_body", "offset": 7}, {"slot": "hero_2_hair", "offset": 7}]}, {"time": 2.5667}, {"time": 2.6667, "offsets": [{"slot": "hero_2_body", "offset": 7}, {"slot": "hero_2_hair", "offset": 7}]}, {"time": 2.7333}], "events": [{"time": 2.4667, "name": "attack"}, {"time": 3.2667, "name": "attack"}]}}}