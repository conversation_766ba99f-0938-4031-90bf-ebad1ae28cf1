{"skeleton": {"hash": "yDQzV472yyEAOoI3rE8MJTSyDx4", "spine": "3.5.49", "width": 393.5, "height": 366.5, "images": "./image/images/"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 55.05, "rotation": -178.48, "x": -7.28, "y": -35.74}, {"name": "bone2", "parent": "bone", "length": 71.25, "rotation": 73.02, "x": 15.79, "y": 7.38}, {"name": "bone3", "parent": "bone2", "length": 89.19, "rotation": 13.27, "x": 71.25}, {"name": "bone4", "parent": "bone3", "length": 45.67, "rotation": -65.89, "x": 89.19}, {"name": "bone5", "parent": "bone", "length": 75.33, "rotation": 105.39, "x": -9.49, "y": 9.51}, {"name": "bone6", "parent": "bone5", "length": 83.47, "rotation": 5.01, "x": 75.33}, {"name": "bone7", "parent": "bone6", "length": 30.93, "rotation": -29.16, "x": 83.47}, {"name": "bone8", "parent": "bone", "length": 28.95, "rotation": -104.15, "x": 2.67, "y": -9.19}, {"name": "bone9", "parent": "bone8", "length": 41.2, "rotation": 5.84, "x": 28.95}, {"name": "bone10", "parent": "bone9", "length": 16.79, "rotation": -10.07, "x": 41.2}, {"name": "bone11", "parent": "bone10", "length": 68.74, "rotation": 30.8, "x": 16.79}, {"name": "bone12", "parent": "bone9", "length": 40.28, "rotation": 172.08, "x": 25.18, "y": 19.18}, {"name": "bone13", "parent": "bone12", "length": 40.63, "rotation": 0.84, "x": 40.28}, {"name": "bone14", "parent": "bone13", "length": 29.13, "rotation": -6.69, "x": 40.63}, {"name": "bone15", "parent": "bone9", "length": 45.79, "rotation": -143.92, "x": 36.63, "y": -27.52}, {"name": "bone16", "parent": "bone15", "length": 50.91, "rotation": -14.32, "x": 45.6, "y": 0.66}, {"name": "bone17", "parent": "bone16", "length": 19.15, "rotation": -7.66, "x": 50.91}, {"name": "bone18", "parent": "bone17", "length": 13.29, "rotation": -15.73, "x": 18.6, "y": -0.42}, {"name": "bone19", "parent": "bone11", "length": 22.15, "rotation": -137.28, "x": 3.08, "y": -7.79}, {"name": "bone20", "parent": "bone19", "length": 22.42, "rotation": 30.85, "x": 22.15}, {"name": "bone21", "parent": "bone20", "length": 25.52, "rotation": 15.73, "x": 22.42}, {"name": "bone22", "parent": "bone21", "length": 28.83, "rotation": 39.31, "x": 25.52}, {"name": "bone23", "parent": "bone", "length": 23.8, "rotation": 119.24, "x": -21.98, "y": -1.71}, {"name": "bone24", "parent": "bone23", "length": 23.07, "rotation": 13.38, "x": 23.8}, {"name": "bone25", "parent": "bone24", "length": 25.59, "rotation": 24.66, "x": 23.07}, {"name": "bone26", "parent": "bone25", "length": 28.24, "rotation": -11.13, "x": 25.59}, {"name": "bone27", "parent": "bone26", "length": 29.33, "rotation": -22.14, "x": 28.24}, {"name": "bone28", "parent": "bone", "length": 24.32, "rotation": 121.21, "x": -20.59, "y": 13.84}, {"name": "bone29", "parent": "bone28", "length": 23.67, "rotation": 3.9, "x": 24.32}, {"name": "bone30", "parent": "bone29", "length": 28.93, "rotation": 9.73, "x": 23.67}, {"name": "bone31", "parent": "bone30", "length": 26.86, "rotation": -1.36, "x": 28.93}, {"name": "bone32", "parent": "bone31", "length": 29.73, "rotation": -13.39, "x": 26.86}, {"name": "bone33", "parent": "bone32", "length": 31.71, "rotation": -20.99, "x": 29.32, "y": 0.26}, {"name": "bone34", "parent": "bone9", "length": 12.82, "rotation": -45.56, "x": 41.09, "y": -22.26}, {"name": "bone35", "parent": "bone34", "length": 9.41, "rotation": 18.66, "x": 12.82}, {"name": "bone36", "parent": "bone35", "length": 10.29, "rotation": 12.19, "x": 9.41}, {"name": "bone37", "parent": "bone9", "length": 18.62, "rotation": 44.19, "x": 50.68, "y": 24.73}, {"name": "bone38", "parent": "bone9", "length": 18.91, "rotation": 64.32, "x": 33.6, "y": 30.87}, {"name": "bone39", "parent": "bone11", "length": 16.53, "rotation": 76.06, "x": 11.26, "y": 5.45}, {"name": "bone40", "parent": "bone14", "length": 223.71, "rotation": -41.56, "x": 14.36, "y": 0.32}, {"name": "bone41", "parent": "bone", "length": 25.27, "rotation": 114.84, "x": -5.13, "y": 3.45}, {"name": "bone42", "parent": "bone41", "length": 30.9, "rotation": 8.7, "x": 25.27}, {"name": "bone43", "parent": "bone42", "length": 33.07, "rotation": 52.82, "x": 30.9}, {"name": "bone44", "parent": "bone43", "length": 33.18, "rotation": 47.12, "x": 33.07}, {"name": "bone45", "parent": "bone44", "length": 34.75, "rotation": 41.3, "x": 33.18}, {"name": "bone46", "parent": "bone45", "length": 44.53, "rotation": 15.06, "x": 34.96, "y": 0.01}, {"name": "bone47", "parent": "bone6", "length": 20.15, "rotation": 118.27, "x": 38.45, "y": 16.66}, {"name": "bone48", "parent": "bone3", "length": 17.74, "rotation": 141.32, "x": 44.71, "y": 19.08}], "slots": [{"name": "12", "bone": "bone40", "attachment": "11"}, {"name": "11", "bone": "root"}, {"name": "10", "bone": "root", "attachment": "10"}, {"name": "9", "bone": "root", "attachment": "9"}, {"name": "8", "bone": "root", "attachment": "8"}, {"name": "7", "bone": "root", "attachment": "7"}, {"name": "6", "bone": "root", "attachment": "6"}, {"name": "5", "bone": "root", "attachment": "5"}, {"name": "4", "bone": "root", "attachment": "4"}, {"name": "13", "bone": "bone39", "attachment": "3"}, {"name": "3", "bone": "root"}, {"name": "2", "bone": "root", "attachment": "2"}, {"name": "1", "bone": "root", "attachment": "1"}], "skins": {"default": {"1": {"1": {"type": "mesh", "uvs": [0.02778, 0.60055, 0.02778, 0.72429, 0.17658, 0.89384, 0.32424, 0.98276, 0.46452, 0.95342, 0.53097, 0.77469, 0.61957, 0.61888, 0.75247, 0.48599, 0.90013, 0.376, 0.97222, 0.22477, 0.97222, 0.02772, 0.76724, 0.01724, 0.50144, 0.06896, 0.33901, 0.1652, 0.25779, 0.32559, 0.11013, 0.45849, 0.30209, 0.83885, 0.26518, 0.70137, 0.35378, 0.55014, 0.41284, 0.41266, 0.65649, 0.29351, 0.81154, 0.15603], "triangles": [20, 11, 21, 20, 12, 11, 21, 10, 9, 21, 11, 10, 20, 21, 8, 8, 21, 9, 7, 20, 8, 14, 13, 19, 20, 13, 12, 20, 19, 13, 6, 20, 7, 6, 19, 20, 18, 14, 19, 18, 19, 6, 15, 14, 18, 2, 16, 3, 3, 16, 4, 4, 16, 5, 2, 17, 16, 2, 1, 17, 16, 17, 5, 17, 18, 5, 5, 18, 6, 1, 0, 17, 0, 15, 17, 17, 15, 18], "vertices": [1, 9, 30.03, -9.9, 1, 1, 9, 22.91, -10.75, 1, 1, 9, 13.78, -17.24, 1, 1, 9, 9.28, -23.12, 1, 1, 9, 11.57, -27.94, 1, 1, 9, 22.15, -29.09, 1, 1, 9, 31.5, -31.19, 1, 3, 9, 39.72, -35.03, 0.41, 34, 8.15, -9.92, 0.54006, 35, -7.6, -7.9, 0.04994, 2, 34, 16.25, -8.11, 0.36873, 35, 0.66, -8.79, 0.63127, 2, 35, 9.4, -6.08, 0.72166, 36, -1.3, -5.94, 0.27834, 2, 34, 30.65, 6.3, 0, 36, 9.34, -1.75, 1, 3, 34, 25.18, 11.29, 0.00063, 35, 15.32, 6.74, 0.07836, 36, 7.2, 5.34, 0.92101, 3, 34, 15.77, 14.75, 0.21786, 35, 7.51, 13.04, 0.45382, 36, 0.9, 13.14, 0.32833, 3, 34, 7.73, 13.91, 0.60715, 35, -0.37, 14.8, 0.31605, 36, -6.44, 16.54, 0.07679, 4, 9, 46.85, -16.24, 0.41, 34, -0.27, 8.33, 0.57182, 35, -9.74, 12.08, 0.01784, 36, -16.16, 15.85, 0.00034, 1, 9, 38.57, -11.87, 1, 1, 9, 17.48, -21.34, 1, 1, 9, 25.24, -19.08, 1, 1, 9, 34.33, -21.21, 1, 3, 9, 42.49, -22.38, 0.41, 34, 1.07, 0.92, 0.58861, 35, -10.84, 4.63, 0.00139, 3, 34, 12.23, 1.03, 0.52695, 35, -0.23, 1.17, 0.46883, 36, -9.17, 3.18, 0.00422, 2, 35, 9.51, 0.95, 0.5, 36, 0.29, 0.9, 0.5], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 36, "height": 58}}, "2": {"2": {"type": "mesh", "uvs": [0.06041, 0.04572, 0.02778, 0.09509, 0.02778, 0.20068, 0.09983, 0.27061, 0.17606, 0.35015, 0.15766, 0.40637, 0.18394, 0.49688, 0.17869, 0.60933, 0.36004, 0.6107, 0.40209, 0.66418, 0.42312, 0.70806, 0.38895, 0.75606, 0.36004, 0.87536, 0.45729, 0.90827, 0.49409, 0.826, 0.53614, 0.82051, 0.56505, 0.86988, 0.5414, 0.92884, 0.62025, 0.97464, 0.72538, 0.97464, 0.75166, 0.90827, 0.77795, 0.85479, 0.76743, 0.79857, 0.71487, 0.73823, 0.83577, 0.71492, 0.92514, 0.67926, 0.86468, 0.65321, 0.72538, 0.66281, 0.70961, 0.61207, 0.73327, 0.56133, 0.89885, 0.49414, 0.97222, 0.41872, 0.97222, 0.36935, 0.87783, 0.39403, 0.77795, 0.35152, 0.63865, 0.3049, 0.51511, 0.27199, 0.4494, 0.18834, 0.35216, 0.09783, 0.24177, 0.02536, 0.13663, 0.02536, 0.13137, 0.09234, 0.23388, 0.18011, 0.31799, 0.2857, 0.3837, 0.34878, 0.36793, 0.4818, 0.6439, 0.453, 0.8016, 0.45437, 0.57031, 0.57093, 0.56768, 0.70121, 0.46518, 0.77114, 0.59134, 0.77663, 0.65967, 0.85342, 0.64653, 0.91787], "triangles": [20, 52, 21, 53, 16, 52, 53, 52, 20, 17, 16, 53, 18, 17, 53, 19, 53, 20, 18, 53, 19, 50, 10, 49, 11, 10, 50, 51, 49, 23, 15, 50, 49, 51, 15, 49, 14, 50, 15, 52, 51, 23, 52, 23, 22, 52, 22, 21, 16, 15, 51, 16, 51, 52, 12, 50, 14, 50, 12, 11, 13, 12, 14, 33, 32, 31, 46, 35, 34, 47, 34, 33, 46, 34, 47, 45, 4, 44, 5, 4, 45, 30, 33, 31, 47, 33, 30, 6, 5, 45, 29, 46, 47, 29, 47, 30, 36, 46, 44, 46, 36, 35, 48, 46, 29, 46, 45, 44, 48, 45, 46, 8, 6, 45, 8, 45, 48, 7, 6, 8, 28, 48, 29, 9, 8, 48, 49, 9, 48, 49, 48, 28, 49, 28, 27, 10, 9, 49, 24, 27, 26, 24, 26, 25, 23, 49, 27, 23, 27, 24, 41, 0, 40, 39, 41, 40, 42, 39, 38, 42, 41, 39, 41, 2, 1, 41, 1, 0, 3, 2, 41, 3, 41, 42, 43, 42, 38, 43, 38, 37, 44, 43, 37, 44, 37, 36, 4, 3, 42, 4, 42, 43, 44, 4, 43], "vertices": [1, 15, -3.55, 0.94, 1, 1, 15, 1.24, -4.44, 1, 1, 15, 13.95, -11.57, 1, 3, 15, 24.9, -11.77, 0.99721, 16, -16.98, -17.17, 0.00279, 17, -65, -26.06, 0, 3, 15, 37.16, -12.35, 0.77115, 16, -4.96, -14.7, 0.22885, 17, -53.42, -22.02, 0, 3, 15, 43.28, -17.3, 0.38792, 16, 2.19, -17.98, 0.61208, 17, -45.89, -24.32, 0, 3, 15, 55.1, -21.76, 0.05092, 16, 14.75, -19.38, 0.94907, 17, -33.26, -24.03, 1e-05, 2, 16, 29.64, -23.75, 0.99996, 17, -17.92, -26.38, 4e-05, 1, 16, 33.2, -11.19, 1, 2, 16, 41.11, -10.17, 0.90834, 17, -8.36, -11.39, 0.09166, 2, 16, 47.35, -10.27, 0.52289, 17, -2.16, -10.65, 0.47711, 2, 16, 53.11, -14.36, 0.12861, 17, 4.09, -13.94, 0.87139, 1, 17, 20.16, -18.09, 1, 1, 17, 25.55, -11.73, 1, 2, 16, 64.39, -9.54, 0.00173, 17, 14.63, -7.66, 0.99827, 2, 17, 14.26, -4.56, 0.97441, 18, -3.06, -5.16, 0.02559, 2, 17, 21.29, -3.36, 0.2132, 18, 3.38, -2.1, 0.7868, 1, 18, 11.68, -2.59, 1, 2, 16, 86.56, -6.06, 0, 18, 17.1, 3.95, 1, 2, 16, 88.51, 1.25, 1e-05, 18, 15.99, 11.44, 0.99999, 3, 16, 80.15, 5.45, 1e-05, 17, 28.25, 9.3, 0.13716, 18, 6.65, 11.97, 0.86283, 2, 17, 21.17, 12.11, 0.48396, 18, -0.93, 12.76, 0.51604, 3, 16, 65.82, 10.45, 0.01644, 17, 13.38, 12.35, 0.80095, 18, -8.49, 10.87, 0.18261, 3, 16, 56.8, 8.95, 0.31291, 17, 4.64, 9.65, 0.68273, 18, -16.17, 5.91, 0.00436, 2, 16, 55.94, 18.19, 0.61676, 17, 2.55, 18.7, 0.38324, 2, 16, 52.85, 25.67, 0.65381, 17, -1.51, 25.7, 0.34619, 2, 16, 48.25, 22.4, 0.66102, 17, -5.63, 21.84, 0.33898, 2, 16, 46.94, 12.37, 0.74825, 17, -5.59, 11.73, 0.25175, 2, 16, 39.88, 13.08, 0.98037, 17, -12.68, 11.49, 0.01963, 1, 16, 33.56, 16.53, 1, 1, 16, 27.68, 30.44, 1, 2, 15, 73.46, 33.01, 0.00055, 16, 18.99, 38.24, 0.99945, 1, 16, 12.4, 40, 1, 2, 15, 67.16, 28.75, 0.00747, 16, 13.94, 32.55, 0.99253, 2, 15, 58.53, 25.35, 0.06579, 16, 6.41, 27.12, 0.93421, 2, 15, 48.01, 19.75, 0.28906, 16, -2.39, 19.09, 0.71094, 2, 15, 39.69, 14.22, 0.71925, 16, -9.08, 11.67, 0.28075, 2, 15, 27.31, 15.74, 0.99613, 16, -21.45, 10.08, 0.00387, 2, 15, 12.99, 15.74, 0.99996, 16, -35.32, 6.54, 4e-05, 2, 15, 0.38, 13.7, 0.99998, 16, -47.04, 1.45, 2e-05, 2, 15, -3.32, 7.1, 0.99999, 16, -48.99, -5.87, 1e-05, 2, 15, 4.56, 2.25, 1, 16, -40.16, -8.62, 0, 1, 15, 18.73, 2.76, 1, 1, 15, 34.4, 0.91, 1, 2, 15, 44.31, 0.78, 0.5, 16, -1.28, -0.21, 0.5, 3, 15, 59.76, -9.19, 0.00404, 16, 16.16, -6.05, 0.99596, 17, -33.64, -10.62, 0, 2, 15, 66.02, 10.08, 0.02142, 16, 17.45, 14.18, 0.97858, 2, 15, 71.74, 19.89, 0.00313, 16, 20.57, 25.1, 0.99687, 1, 16, 31.8, 4.86, 1, 2, 16, 49.12, 0.03, 0.5, 17, -1.78, -0.21, 0.5, 2, 16, 56.54, -9.59, 0.05907, 17, 6.86, -8.76, 0.94093, 1, 17, 8.76, 0.16, 1, 2, 17, 19.9, 3.69, 0.54576, 18, 0.13, 4.31, 0.45424, 3, 16, 79.48, -2.21, 1e-05, 17, 28.6, 1.62, 0.05652, 18, 9.07, 4.67, 0.94348], "hull": 41, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 0, 80], "width": 72, "height": 138}}, "4": {"4": {"type": "mesh", "uvs": [0.30448, 0, 0.20052, 0.0662, 0.21274, 0.2484, 0.23532, 0.31545, 0.15139, 0.33249, 0.02685, 0.36155, 0, 0.39832, 0, 0.545, 0.02151, 0.62214, 0.13834, 0.68074, 0.17728, 0.75771, 0.26398, 0.72599, 0.27794, 0.75402, 0.22523, 0.77014, 0.18546, 0.83883, 0.14433, 0.90268, 0.06668, 1, 0.26798, 0.99184, 0.4979, 1, 0.72839, 1, 0.63924, 0.90244, 0.63676, 0.84209, 0.64916, 0.78337, 0.76188, 0.76848, 0.8403, 0.73099, 0.74835, 0.64474, 0.71115, 0.53546, 0.72603, 0.4588, 0.78132, 0.40438, 0.7781, 0.26471, 0.82917, 0.07877, 0.56245, 0, 0.31836, 0.37681, 0.39256, 0.42436, 0.46295, 0.46565, 0.53144, 0.4694, 0.61704, 0.42811, 0.67602, 0.39182, 0.72739, 0.38431, 0.36022, 0.46065, 0.33168, 0.49693, 0.34309, 0.54448, 0.31456, 0.60205, 0.28602, 0.67838, 0.4268, 0.76472, 0.39446, 0.84981, 0.27461, 0.8861, 0.55236, 0.90737, 0.39065, 0.90737, 0.54285, 0.791, 0.39636, 0.68088, 0.63797, 0.7009, 0.46485, 0.57452, 0.63797, 0.56951, 0.63607, 0.49318, 0.22134, 0.51946, 0.13192, 0.53823, 0.13383, 0.44438, 0.26129, 0.44188, 0.27461, 0.39808, 0.16046, 0.39808, 0.20041, 0.61706, 0.47056, 0.26919, 0.35641, 0.16783, 0.64178, 0.16408, 0.63797, 0.2817], "triangles": [6, 5, 57, 55, 57, 58, 57, 7, 6, 56, 57, 55, 56, 7, 57, 61, 56, 55, 61, 55, 42, 8, 7, 56, 9, 56, 61, 8, 56, 9, 9, 61, 11, 10, 9, 11, 60, 4, 3, 5, 4, 60, 59, 3, 32, 60, 3, 59, 58, 60, 59, 57, 5, 60, 39, 59, 32, 39, 58, 59, 40, 58, 39, 58, 57, 60, 55, 58, 40, 39, 33, 34, 64, 31, 30, 63, 0, 31, 62, 63, 31, 1, 0, 63, 2, 1, 63, 29, 64, 30, 64, 62, 31, 65, 62, 64, 65, 64, 29, 3, 2, 63, 32, 3, 63, 62, 32, 63, 38, 65, 29, 37, 65, 38, 38, 29, 28, 33, 32, 62, 36, 62, 65, 36, 65, 37, 34, 33, 62, 27, 37, 38, 27, 38, 28, 39, 32, 33, 54, 36, 37, 62, 35, 34, 36, 35, 62, 35, 36, 54, 54, 37, 27, 26, 54, 27, 41, 40, 39, 53, 54, 26, 52, 34, 35, 41, 42, 55, 41, 55, 40, 53, 26, 25, 43, 61, 42, 41, 39, 34, 41, 34, 52, 50, 41, 52, 42, 41, 50, 43, 42, 50, 54, 53, 35, 51, 53, 25, 11, 61, 43, 12, 11, 43, 53, 52, 35, 51, 52, 53, 23, 25, 24, 51, 25, 23, 22, 51, 23, 49, 52, 51, 49, 51, 22, 49, 50, 52, 50, 12, 43, 49, 44, 50, 21, 49, 22, 12, 50, 44, 45, 12, 44, 46, 13, 12, 46, 12, 45, 14, 13, 46, 47, 49, 21, 45, 49, 47, 49, 45, 44, 48, 46, 45, 47, 21, 20, 46, 15, 14, 17, 46, 48, 17, 15, 46, 16, 15, 17, 47, 48, 45, 18, 48, 47, 17, 48, 18, 18, 47, 20, 18, 20, 19], "vertices": [1, 11, 81.22, 7.56, 1, 1, 11, 72.28, 22.95, 1, 1, 11, 38.85, 29.7, 1, 3, 10, 23.77, 39.11, 0.00052, 11, 26.02, 30.03, 0.22, 37, 21.59, -7.56, 0.77948, 3, 10, 17.72, 48.07, 0.00019, 37, 25.33, 2.57, 0.94574, 38, 26.54, -24.36, 0.05407, 5, 9, 59.85, 58.8, 0.18, 10, 8.08, 61.15, 0.00014, 11, 23.83, 56.99, 0.01353, 37, 30.32, 18.04, 0.42553, 38, 36.54, -11.55, 0.38079, 5, 9, 52.63, 61.26, 0.18, 10, 0.54, 62.32, 3e-05, 11, 17.95, 61.85, 0.00391, 37, 26.86, 24.84, 0.09486, 38, 35.64, -3.98, 0.7212, 4, 8, 48.3, 60.31, 0.00967, 9, 25.39, 58.02, 0.18, 37, 5.07, 41.5, 0.0002, 38, 20.91, 19.16, 0.81013, 5, 8, 34.8, 54.57, 0.0374, 9, 11.38, 53.69, 0.18, 10, -38.75, 47.65, 0, 37, -7.99, 48.16, 1e-05, 38, 10.93, 29.91, 0.78259, 4, 8, 27.25, 38.15, 0.14764, 9, 2.19, 38.12, 0.17736, 10, -45.07, 30.72, 0, 38, -7.07, 31.44, 0.675, 4, 8, 14.25, 30.33, 0.26275, 9, -11.53, 31.67, 0.22219, 10, -57.45, 21.96, 0, 38, -18.84, 41.01, 0.51506, 4, 1, 17.77, -36.07, 0.00082, 8, 22.37, 21.22, 0.61118, 9, -4.38, 21.78, 0.388, 10, -48.68, 13.47, 0, 4, 1, 16.19, -30.79, 0.02833, 8, 17.63, 18.4, 0.85494, 9, -9.38, 19.45, 0.11672, 10, -53.2, 10.31, 0, 4, 8, 13.27, 24.07, 0.91196, 9, -13.14, 25.54, 0.0259, 10, -57.97, 15.64, 0, 38, -25.07, 39.81, 0.06214, 4, 1, 27.98, -15.23, 0.14, 8, -0.33, 26.03, 0.85044, 10, -71.68, 16.6, 0, 38, -27.83, 53.27, 0.00956, 5, 1, 33.36, -3.43, 0.98328, 8, -13.09, 28.36, 0.01668, 9, -38.93, 32.49, 0, 10, -84.57, 17.98, 0, 38, -29.98, 66.06, 4e-05, 5, 1, 43.39, 14.51, 0.99339, 8, -32.94, 33.69, 0.00656, 9, -58.13, 39.82, 0, 10, -104.76, 21.84, 0, 38, -31.69, 86.54, 4e-05, 3, 1, 18.6, 13.64, 1, 9, -53.69, 15.41, 0, 10, -96.12, -1.41, 0, 1, 1, -9.63, 15.92, 1, 1, 1, -37.97, 16.67, 1, 4, 1, -27.49, -1.86, 0.69371, 8, 0.27, -31.04, 0.27427, 9, -31.69, -27.95, 0.03202, 38, -81.31, 33.34, 0, 4, 8, 11.21, -28.27, 0.82993, 9, -20.52, -26.32, 0.17, 10, -56.16, -36.7, 7e-05, 38, -74.99, 23.99, 0, 4, 8, 22.26, -27.36, 0.45889, 9, -9.44, -26.53, 0.53548, 10, -45.21, -34.98, 0.00563, 38, -70.39, 13.9, 0, 5, 1, -43.24, -26.5, 0.01339, 8, 28.01, -40.28, 0.2521, 9, -5.03, -39.97, 0.71617, 10, -38.53, -47.44, 0.01833, 38, -80.59, 4.11, 1e-05, 5, 1, -53.07, -33.25, 0.00406, 8, 36.96, -48.16, 0.19941, 9, 3.07, -48.72, 0.76651, 10, -29.02, -54.64, 0.03001, 38, -84.96, -6.98, 1e-05, 1, 9, 17.75, -35.58, 1, 1, 9, 37.5, -28.63, 1, 1, 9, 51.95, -28.75, 1, 5, 9, 62.86, -34.3, 0.07265, 10, 27.33, -29.98, 0.03, 11, -6.3, -31.15, 0.89734, 37, -32.42, -50.81, 0, 38, -46.05, -54.62, 0, 1, 11, 19.14, -37.05, 1, 1, 11, 51.38, -51.53, 1, 3, 11, 73.58, -23.23, 1, 37, 44.01, -75.36, 0, 38, 17.26, -103.96, 0, 3, 10, 15.75, 26.01, 0.00302, 11, 12.42, 22.88, 0.79, 37, 6.27, -8.71, 0.20698, 5, 9, 53.5, 12.74, 0.01942, 10, 9.89, 14.7, 0.1631, 11, 1.59, 16.16, 0.51, 37, -6.34, -10.56, 0.30168, 38, -7.71, -25.79, 0.0058, 5, 9, 46.86, 3.23, 0.21995, 10, 5.01, 4.17, 0.56786, 11, -7.99, 9.62, 0.06, 37, -17.73, -12.74, 0.13056, 38, -19.16, -23.93, 0.02162, 3, 9, 47.15, -5.21, 0.15338, 10, 6.78, -4.09, 0.78662, 11, -10.7, 1.61, 0.06, 2, 10, 17.22, -11.93, 0.65179, 11, -5.74, -10.47, 0.34821, 4, 9, 63.66, -21.16, 0.11, 10, 25.82, -16.91, 0.19068, 11, -0.9, -19.14, 0.69931, 37, -22.68, -41.95, 0, 5, 9, 65.8, -27.27, 0.01782, 10, 28.99, -22.54, 0.20217, 11, -1.06, -25.61, 0.78, 37, -25.41, -47.82, 0, 38, -38.44, -54.22, 0, 3, 9, 46.29, 15.89, 0.12512, 10, 2.24, 16.54, 0.10895, 37, -9.31, -3.27, 0.76593, 3, 9, 39.14, 18.57, 0.86616, 10, -5.28, 17.93, 0.02496, 37, -12.57, 3.64, 0.10888, 4, 8, 57.62, 19.15, 0.00469, 9, 30.47, 16.13, 0.92165, 10, -13.38, 14.01, 0.02343, 37, -20.49, 7.92, 0.05022, 2, 8, 46.35, 20.22, 0.0455, 9, 19.37, 18.34, 0.9545, 1, 9, 4.78, 20.14, 1, 2, 8, 19.68, 0.09, 0.73, 9, -9.2, 1.04, 0.27, 1, 8, 3.29, 0.5, 1, 4, 1, 17.26, -6.11, 0.65144, 8, -6.56, 13.4, 0.34759, 10, -76.96, 3.54, 0, 38, -41.83, 54.84, 0.00097, 4, 1, -16.79, -1.22, 0.78698, 8, -2.97, -20.81, 0.20222, 9, -33.87, -17.45, 0.01079, 38, -72.79, 39.86, 0, 1, 1, 3.1, -1.75, 1, 3, 8, 18.01, -14.91, 0.7557, 9, -12.4, -13.72, 0.2443, 38, -60.12, 22.13, 0, 2, 8, 34.16, 7.18, 0.30309, 9, 5.92, 6.61, 0.69691, 5, 1, -28.34, -39.54, 0.00708, 8, 37.01, -22.64, 0.18772, 9, 5.72, -23.34, 0.77575, 10, -30.85, -29.19, 0.02944, 38, -60.94, 1.63, 0, 1, 9, 26.67, 0.59, 1, 1, 9, 30.11, -20.44, 1, 2, 9, 44.26, -18.52, 0.83648, 11, -18.11, -9.81, 0.16352, 1, 38, 0.51, 0.52, 1, 3, 8, 53.08, 44.75, 0.00913, 9, 28.57, 42.06, 0.00902, 38, 7.9, 9.38, 0.98185, 3, 11, 5.62, 47.95, 0.00012, 37, 10.02, 17, 0.3619, 38, 17.12, -5.55, 0.63798, 1, 37, 0.87, 4.26, 1, 1, 37, 6.38, -2.02, 1, 2, 37, 14.91, 9.14, 0.77615, 38, 19.01, -14.61, 0.22385, 3, 8, 40.54, 33.3, 0.0789, 9, 14.92, 31.95, 0.14419, 38, -7.12, 17.3, 0.77692, 1, 11, 27.44, -0.14, 1, 1, 11, 49.22, 8.92, 1, 4, 10, 65.36, -0.52, 0.00087, 11, 41.45, -25.31, 0.99911, 37, 13.71, -64.47, 1e-05, 38, -7.44, -83.31, 0, 1, 11, 20.21, -19.56, 1], "hull": 32, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 0, 62], "width": 123, "height": 187}}, "5": {"5": {"type": "mesh", "uvs": [0.34302, 0.02551, 0.28312, 0.11169, 0.23532, 0.23367, 0.23359, 0.34503, 0.21242, 0.4095, 0.21391, 0.47925, 0.19175, 0.57518, 0.19755, 0.67382, 0.20142, 0.7639, 0.17918, 0.82609, 0.16338, 0.85781, 0.0474, 0.8869, 0, 0.91795, 0, 0.97954, 0.14538, 0.98821, 0.3527, 0.98182, 0.34565, 0.88399, 0.34234, 0.83891, 0.35228, 0.77202, 0.43347, 0.6935, 0.48815, 0.57863, 0.40862, 0.48411, 0.3821, 0.41432, 0.39867, 0.36343, 0.44507, 0.25728, 0.46948, 0.17023, 0.48733, 0.1742, 0.51385, 0.2289, 0.55383, 0.31916, 0.61327, 0.41219, 0.64059, 0.46178, 0.66602, 0.52955, 0.64153, 0.5684, 0.68674, 0.64527, 0.76155, 0.72654, 0.80622, 0.8038, 0.81542, 0.84531, 0.80754, 0.88451, 0.75543, 1, 0.93083, 1, 0.98609, 0.96576, 0.97751, 0.88576, 0.95201, 0.84185, 0.93728, 0.78766, 0.92436, 0.69323, 1, 0.60677, 0.99999, 0.54003, 1, 0.44461, 0.84632, 0.48223, 0.79573, 0.4302, 0.78079, 0.38349, 0.7563, 0.35251, 0.73199, 0.27007, 0.71573, 0.16889, 0.68402, 0.09121, 0.64986, 0.02551, 0.50139, 0.02551, 0.40804, 0.08845, 0.56031, 0.07606, 0.47475, 0.08019, 0.36644, 0.17662, 0.34523, 0.24895, 0.3107, 0.3631, 0.29688, 0.40957, 0.30494, 0.47725, 0.32411, 0.60572, 0.29917, 0.7082, 0.28103, 0.77585, 0.27876, 0.84351, 0.20847, 0.90718, 0.12571, 0.92608, 0.85243, 0.92509, 0.87964, 0.8634, 0.87283, 0.81664, 0.85243, 0.74899, 0.82295, 0.69129, 0.781, 0.58583, 0.89778, 0.54703, 0.71638, 0.4943, 0.69597, 0.42167, 0.67557, 0.35235, 0.65516, 0.28669, 0.62228, 0.19913, 0.398, 0.59843, 0.392, 0.6682, 0.8555, 0.57342, 0.881, 0.64319], "triangles": [47, 77, 48, 46, 77, 47, 77, 46, 45, 86, 77, 45, 58, 55, 54, 26, 59, 58, 25, 59, 26, 82, 58, 54, 82, 54, 53, 26, 58, 82, 27, 26, 82, 82, 53, 52, 81, 82, 52, 28, 27, 82, 28, 82, 81, 80, 81, 52, 28, 81, 80, 80, 52, 51, 29, 28, 80, 79, 80, 51, 79, 51, 50, 29, 80, 79, 30, 29, 79, 79, 50, 49, 78, 79, 49, 30, 79, 78, 31, 30, 78, 85, 48, 77, 48, 76, 78, 48, 78, 49, 76, 48, 85, 86, 85, 77, 76, 33, 31, 76, 31, 78, 32, 31, 33, 86, 75, 76, 86, 76, 85, 34, 33, 76, 44, 86, 45, 75, 34, 76, 44, 74, 75, 44, 75, 86, 34, 75, 74, 74, 44, 43, 35, 34, 74, 73, 74, 43, 35, 74, 73, 73, 43, 42, 36, 35, 73, 72, 73, 42, 36, 73, 72, 37, 36, 72, 71, 37, 72, 72, 41, 71, 41, 72, 42, 39, 41, 40, 39, 71, 41, 38, 37, 71, 38, 71, 39, 69, 9, 68, 10, 9, 69, 70, 11, 10, 70, 10, 69, 14, 70, 69, 13, 12, 11, 11, 70, 13, 14, 13, 70, 14, 69, 15, 68, 16, 69, 15, 69, 16, 64, 63, 22, 5, 4, 63, 5, 63, 64, 64, 22, 21, 21, 65, 64, 83, 21, 20, 83, 65, 21, 84, 65, 83, 65, 7, 6, 6, 5, 64, 64, 65, 6, 19, 83, 20, 84, 83, 19, 66, 7, 65, 66, 65, 84, 8, 7, 66, 18, 66, 84, 18, 84, 19, 67, 8, 66, 67, 66, 18, 17, 67, 18, 68, 8, 67, 68, 67, 17, 9, 8, 68, 17, 16, 68, 1, 0, 57, 25, 57, 59, 60, 1, 57, 60, 57, 25, 2, 1, 60, 61, 2, 60, 24, 60, 25, 61, 60, 24, 3, 2, 61, 62, 3, 61, 23, 61, 24, 62, 61, 23, 63, 3, 62, 4, 3, 63, 22, 62, 23, 63, 62, 22, 58, 56, 55, 56, 57, 0, 59, 56, 58, 59, 57, 56], "vertices": [1, 1, 27.29, 3.43, 1, 1, 2, 18.61, -17.58, 1, 1, 2, 43.85, -19.14, 1, 2, 2, 64.97, -13.6, 0.79546, 3, -9.23, -11.8, 0.20454, 2, 2, 78.12, -13.74, 0.31442, 3, 3.53, -14.95, 0.68558, 1, 3, 17.18, -14.18, 1, 1, 3, 36.12, -17.27, 1, 1, 3, 55.4, -15.53, 1, 2, 3, 73.02, -14.19, 0.8677, 4, 6.35, -20.56, 0.1323, 2, 3, 85.34, -17.55, 0.26234, 4, 14.45, -10.67, 0.73766, 2, 3, 91.66, -20.02, 0.01894, 4, 19.29, -5.92, 0.98106, 1, 4, 39.92, -8.08, 1, 1, 4, 49.76, -5.47, 1, 1, 4, 54.26, 5.73, 1, 1, 4, 31.7, 16.63, 1, 2, 3, 114.7, 13.44, 0.19065, 4, -1.85, 28.78, 0.80935, 2, 3, 95.59, 11.5, 0.37759, 4, -7.88, 10.54, 0.62241, 2, 3, 86.78, 10.59, 0.66714, 4, -10.65, 2.13, 0.33286, 2, 3, 73.61, 11.8, 0.98723, 4, -17.13, -9.39, 0.01277, 2, 3, 57.7, 25.17, 0.61, 48, -6.34, -12.87, 0.39, 2, 3, 34.84, 33.7, 0.55, 48, 16.84, -5.25, 0.45, 2, 2, 83.21, 22.68, 0.02305, 3, 16.86, 19.33, 0.97695, 2, 2, 71.24, 14.64, 0.38987, 3, 3.36, 14.25, 0.61013, 2, 2, 60.87, 14.72, 0.81786, 3, -6.72, 16.71, 0.18214, 2, 2, 38.69, 16.87, 0.98206, 5, 32.95, -30.27, 0.01794, 2, 2, 21.13, 16.37, 0.64102, 5, 17.84, -21.29, 0.35898, 2, 2, 21.06, 19.54, 0.40901, 5, 19.48, -18.58, 0.59099, 2, 2, 30.18, 26.79, 0.07999, 5, 31.07, -17.34, 0.92001, 1, 5, 49.99, -15.9, 1, 2, 6, -5.9, -10.95, 0.21863, 5, 70.41, -11.43, 0.78137, 2, 6, 4.87, -10.22, 0.71636, 5, 81.08, -9.76, 0.28364, 1, 6, 18.83, -11.13, 1, 1, 6, 24.32, -17.88, 1, 1, 6, 41.2, -16.29, 1, 2, 7, -14.8, -20.05, 0.00114, 6, 60.78, -10.3, 0.99886, 2, 7, -0.74, -10.52, 0.3291, 6, 77.69, -8.83, 0.6709, 2, 7, 7.13, -7.93, 0.788, 6, 85.83, -10.4, 0.212, 1, 7, 14.92, -8.3, 1, 1, 7, 38.51, -14.34, 1, 1, 7, 34.71, 15.58, 1, 1, 7, 26.85, 24.17, 1, 2, 7, 11.48, 20.73, 0.94337, 6, 103.6, 12.51, 0.05663, 2, 7, 3.5, 15.29, 0.77832, 6, 93.98, 11.65, 0.22168, 2, 7, -6.72, 11.44, 0.36185, 6, 83.18, 13.27, 0.63815, 2, 6, 65.18, 18.12, 0.99986, 5, 138.68, 23.74, 0.00014, 3, 6, 54.31, 36.51, 0.26979, 5, 126.25, 41.12, 0.00021, 47, 9.97, -23.38, 0.73, 1, 47, 20.01, -15, 1, 1, 47, 34.38, -3.03, 1, 2, 6, 21.8, 21.11, 0.8, 47, 11.8, 12.56, 0.2, 2, 6, 9.09, 16.84, 0.86517, 5, 82.92, 17.57, 0.13483, 2, 6, -0.36, 17.88, 0.49008, 5, 73.41, 17.78, 0.50992, 2, 6, -7.57, 16.24, 0.17662, 5, 66.38, 15.51, 0.82338, 1, 5, 49.7, 16.21, 1, 1, 5, 29.91, 19.31, 1, 1, 5, 13.76, 18.52, 1, 1, 1, -25.47, 4.83, 1, 1, 1, 0.06, 4.15, 1, 3, 1, 16.44, 16.06, 0.23, 2, 8.5, 1.91, 0.72634, 5, -0.57, -26.74, 0.04366, 3, 1, -9.8, 14.33, 0.29, 2, -0.83, 26.5, 0.05355, 5, 4.73, -0.98, 0.65645, 3, 1, 4.93, 14.75, 0.53, 2, 3.88, 12.54, 0.16573, 5, 1.22, -15.29, 0.30427, 1, 2, 27.06, -0.38, 1, 1, 2, 41.7, -0.12, 1, 2, 2, 64.84, 0.12, 0.83423, 3, -6.2, 1.59, 0.16577, 2, 2, 74.26, 0.26, 0.5, 3, 2.99, -0.44, 0.5, 1, 3, 16.19, 1.46, 1, 1, 3, 41.23, 5.71, 1, 1, 3, 61.46, 2.19, 1, 1, 3, 74.83, -0.42, 1, 2, 3, 88.1, -0.3, 0.5, 4, -0.17, -1.12, 0.5, 1, 4, 15.7, 5.95, 1, 1, 4, 30.29, 4.07, 1, 1, 7, 21.84, 0.36, 1, 1, 7, 9.26, 3.48, 1, 2, 7, 0.31, 1.16, 0.5, 6, 84.31, 0.86, 0.5, 1, 6, 70.7, 2.56, 1, 1, 6, 58.31, 2.08, 1, 1, 6, 36.44, 3.1, 1, 3, 6, 36.89, 24.58, 0.25983, 5, 109.93, 27.7, 0.00017, 47, 7.71, -2.37, 0.74, 1, 6, 15.65, -0.51, 1, 2, 6, 1.13, 1.55, 0.5, 5, 76.33, 1.64, 0.5, 1, 5, 62.31, 2.24, 1, 1, 5, 48.97, 2.62, 1, 1, 5, 30.91, 2.2, 1, 2, 3, 39.32, 18.36, 0.83, 48, 3.76, 3.94, 0.17, 2, 3, 53.02, 17.85, 0.83, 48, -7.26, -4.23, 0.17, 2, 6, 38.97, 15.9, 0.9996, 5, 112.77, 19.24, 0.0004, 3, 6, 53.29, 14.86, 0.8098, 5, 127.13, 19.46, 0.0002, 47, -8.62, -12.22, 0.19], "hull": 57, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 0, 112], "width": 172, "height": 196}}, "6": {"6": {"type": "mesh", "uvs": [0.7459, 0.02941, 0.6757, 0.1112, 0.6601, 0.18767, 0.57916, 0.26186, 0.352, 0.2412, 0.25058, 0.28385, 0.12724, 0.32183, 0.1375, 0.44959, 0.2779, 0.47061, 0.3403, 0.53561, 0.3286, 0.60826, 0.2584, 0.65414, 0, 0.65821, 0, 0.69715, 0.10336, 0.75955, 0.27228, 0.76457, 0.24426, 0.79371, 0.157, 0.84914, 0.1526, 0.91786, 0.35492, 0.90865, 0.4339, 0.91414, 0.5002, 0.96385, 0.6016, 0.91797, 0.6133, 0.83194, 0.5821, 0.74591, 0.5821, 0.70194, 0.6796, 0.64267, 0.7654, 0.54135, 0.8785, 0.53753, 0.9487, 0.4515, 0.9214, 0.3387, 0.9448, 0.25459, 0.98, 0.18385, 0.98, 0.09209, 0.98, 0.02941, 0.4534, 0.75164, 0.4651, 0.65223, 0.3949, 0.83958, 0.5158, 0.83767, 0.274, 0.8587, 0.5431, 0.55473, 0.6523, 0.3712, 0.3871, 0.39414, 0.6289, 0.4687, 0.8122, 0.39414, 0.7537, 0.24503, 0.8083, 0.11312], "triangles": [38, 35, 24, 38, 24, 23, 37, 15, 35, 37, 35, 38, 39, 15, 37, 16, 15, 39, 17, 16, 39, 19, 39, 37, 20, 37, 38, 19, 37, 20, 18, 17, 39, 18, 39, 19, 22, 38, 23, 21, 20, 38, 21, 38, 22, 42, 4, 3, 42, 3, 41, 5, 4, 42, 7, 6, 5, 8, 7, 5, 44, 30, 29, 43, 42, 41, 42, 8, 5, 9, 8, 42, 28, 44, 29, 44, 43, 41, 27, 44, 28, 27, 43, 44, 40, 42, 43, 9, 42, 40, 26, 43, 27, 40, 43, 26, 36, 9, 40, 10, 9, 36, 25, 40, 26, 36, 40, 25, 35, 36, 25, 35, 10, 36, 24, 35, 25, 14, 12, 11, 13, 12, 14, 15, 11, 10, 15, 10, 35, 14, 11, 15, 46, 0, 34, 33, 46, 34, 1, 0, 46, 46, 33, 32, 45, 1, 46, 2, 1, 45, 31, 46, 32, 45, 46, 31, 30, 45, 31, 45, 41, 3, 45, 3, 2, 44, 45, 30, 41, 45, 44], "vertices": [1, 12, 5.96, -3.86, 1, 1, 12, 14.92, -5.14, 1, 1, 12, 22.67, -3.91, 1, 2, 12, 31.01, -5.9, 0.90643, 13, -9.35, -5.77, 0.09357, 2, 12, 31.86, -17.43, 0.5184, 13, -8.68, -17.3, 0.4816, 2, 12, 37.35, -21.23, 0.38931, 13, -3.24, -21.18, 0.61069, 1, 13, 2, -26.24, 1, 1, 13, 14.53, -22.62, 1, 1, 13, 14.93, -15.29, 1, 2, 12, 61.05, -10.37, 0.00678, 13, 20.62, -10.67, 0.99322, 2, 13, 27.95, -9.46, 0.99855, 14, -11.49, -10.87, 0.00145, 2, 13, 33.34, -11.75, 0.9413, 14, -5.88, -12.52, 0.0587, 2, 13, 36.84, -24.19, 0.97691, 14, -0.95, -24.47, 0.02309, 2, 13, 40.69, -23.24, 0.96855, 14, 2.77, -23.07, 0.03145, 2, 13, 45.63, -16.7, 0.91815, 14, 6.91, -16, 0.08185, 2, 13, 44.11, -8.37, 0.53485, 14, 4.43, -7.91, 0.46515, 2, 13, 47.33, -9.02, 0.12347, 14, 7.7, -8.18, 0.87653, 1, 14, 14.53, -10.28, 1, 1, 14, 21.17, -8.02, 1, 1, 14, 16.74, 1.12, 1, 1, 14, 15.87, 5.01, 1, 1, 14, 19.46, 9.9, 1, 1, 14, 13.29, 13, 1, 2, 13, 46.69, 9.83, 0.04885, 14, 4.87, 10.47, 0.95115, 2, 13, 38.54, 6.21, 0.59504, 14, -2.8, 5.92, 0.40496, 2, 13, 34.19, 5.13, 0.96767, 14, -7, 4.35, 0.03233, 1, 13, 27.15, 8.42, 1, 2, 12, 56.22, 10.34, 0.0079, 13, 16.09, 10.11, 0.9921, 2, 12, 54.41, 15.71, 0.0408, 13, 14.36, 15.5, 0.9592, 2, 12, 45.03, 16.88, 0.20699, 13, 5, 16.81, 0.79301, 2, 12, 34.25, 12.64, 0.77647, 13, -5.85, 12.72, 0.22353, 2, 12, 25.65, 11.59, 0.99281, 13, -14.46, 11.8, 0.00719, 1, 12, 18.23, 11.46, 1, 1, 12, 9.17, 9.08, 1, 1, 12, 2.99, 7.46, 1, 2, 13, 40.66, 0.1, 0.5, 14, 0.01, 0.1, 0.5, 1, 13, 30.67, -1.76, 1, 1, 14, 9.44, 0.52, 1, 2, 13, 48.43, 5.24, 0.00186, 14, 7.13, 6.11, 0.99814, 1, 14, 13.39, -4.46, 1, 1, 13, 20.08, -0.36, 1, 2, 12, 40.87, 0.46, 0.5, 13, 0.6, 0.46, 0.5, 1, 13, 6.05, -11.86, 1, 2, 12, 50.79, 1.86, 0.00028, 13, 10.53, 1.7, 0.99972, 2, 12, 41.1, 8.79, 0.38295, 13, 0.95, 8.78, 0.61705, 1, 12, 27.14, 2.1, 1, 1, 12, 13.43, 1.32, 1], "hull": 35, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 0, 68], "width": 50, "height": 102}}, "7": {"7": {"type": "mesh", "uvs": [0.01923, 0.48021, 0.01923, 0.64469, 0.01923, 0.7448, 0.08844, 0.833, 0.16335, 0.92835, 0.23037, 0.97674, 0.35258, 0.98547, 0.48116, 0.89785, 0.54969, 0.75195, 0.64825, 0.67568, 0.7135, 0.80027, 0.87119, 0.76164, 1, 0.63943, 1, 0.50857, 0.93604, 0.423, 0.81383, 0.38724, 0.95969, 0.27998, 0.95575, 0.16556, 0.92027, 0.00714, 0.78883, 0, 0.73893, 0.14411, 0.61277, 0.26329, 0.51816, 0.35864, 0.34075, 0.38724, 0.08056, 0.44684, 0.12393, 0.56126, 0.15152, 0.68759, 0.30921, 0.8044, 0.392, 0.68044, 0.47085, 0.55649, 0.67585, 0.39201, 0.69162, 0.53504], "triangles": [20, 18, 17, 18, 20, 19, 16, 20, 17, 16, 15, 20, 21, 20, 15, 30, 21, 15, 22, 21, 30, 31, 30, 15, 29, 23, 22, 15, 14, 13, 22, 31, 29, 31, 22, 30, 9, 29, 31, 9, 8, 29, 15, 12, 31, 13, 12, 15, 11, 31, 12, 9, 31, 11, 10, 9, 11, 25, 24, 23, 28, 25, 23, 0, 24, 25, 1, 0, 25, 29, 28, 23, 8, 28, 29, 26, 25, 28, 1, 25, 26, 2, 1, 26, 27, 26, 28, 3, 2, 26, 3, 26, 27, 7, 28, 8, 27, 28, 7, 4, 3, 27, 5, 4, 27, 6, 5, 27, 7, 6, 27], "vertices": [2, 19, 18.56, 4.23, 0.73, 20, -0.92, 5.48, 0.27, 2, 19, 26.33, -7.58, 0.73, 20, -0.3, -8.65, 0.27, 2, 19, 31.06, -14.78, 0.73, 20, 0.07, -17.25, 0.27, 1, 20, 4, -24.68, 1, 1, 20, 8.24, -32.7, 1, 1, 20, 11.91, -36.71, 1, 1, 20, 18.29, -37.18, 1, 1, 20, 24.64, -29.36, 1, 2, 20, 27.66, -16.67, 0.52, 21, 0.52, -17.47, 0.48, 1, 21, 7.01, -12.25, 1, 1, 21, 7.86, -23.46, 1, 1, 21, 16.6, -22.11, 1, 1, 21, 25.53, -13.41, 1, 1, 21, 28.11, -2.45, 1, 1, 21, 26.55, 5.47, 1, 2, 21, 21.07, 9.92, 0.47, 22, 2.85, 10.49, 0.53, 1, 22, 14.78, 10.08, 1, 1, 22, 22.47, 16.22, 1, 1, 22, 32.16, 25.97, 1, 1, 22, 28.49, 31.77, 1, 1, 22, 17.08, 26.3, 1, 1, 22, 4.95, 25.27, 1, 2, 21, 6.67, 15.84, 0.49, 22, -4.55, 24.19, 0.51, 2, 20, 15.44, 14.19, 0.52, 21, -2.87, 15.55, 0.48, 1, 20, 2.14, 8.48, 1, 1, 20, 4.82, -1.25, 1, 1, 20, 6.73, -12.04, 1, 1, 20, 15.36, -21.72, 1, 1, 20, 19.2, -10.88, 1, 2, 20, 22.83, -0.06, 0.52, 21, 0.38, -0.16, 0.48, 2, 21, 13.99, 11.16, 0.49, 22, -1.84, 15.94, 0.51, 1, 21, 11.97, -1, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48], "width": 52, "height": 86}}, "8": {"8": {"type": "mesh", "uvs": [0.02294, 0.06464, 0.04787, 0.16631, 0.08594, 0.25554, 0.14041, 0.36258, 0.2394, 0.45095, 0.34674, 0.52528, 0.47428, 0.5813, 0.58659, 0.63732, 0.68558, 0.72447, 0.7484, 0.84896, 0.82454, 1, 0.9124, 1, 0.99999, 0.90781, 1, 0.77799, 0.92924, 0.68297, 0.82835, 0.60827, 0.70461, 0.5481, 0.58849, 0.48378, 0.46857, 0.4485, 0.35054, 0.39248, 0.25536, 0.32608, 0.18493, 0.22234, 0.14305, 0.13104, 0.08594, 0.03, 0.02294, 0.03], "triangles": [14, 9, 8, 14, 8, 15, 9, 14, 13, 12, 9, 13, 11, 9, 12, 10, 9, 11, 7, 6, 17, 7, 17, 16, 8, 7, 16, 8, 16, 15, 5, 4, 19, 5, 19, 18, 6, 18, 17, 5, 18, 6, 3, 2, 21, 3, 21, 20, 4, 3, 20, 4, 20, 19, 22, 1, 0, 2, 1, 22, 2, 22, 21, 0, 24, 23, 22, 0, 23], "vertices": [1, 1, -19.84, -0.86, 1, 1, 23, 9.83, -5.15, 1, 2, 23, 19.62, -6.15, 0.75, 24, -5.5, -5.02, 0.25, 2, 23, 31.85, -6.53, 0.02118, 24, 6.32, -8.21, 0.97882, 2, 24, 20.18, -6.62, 0.65, 25, -5.4, -4.81, 0.35, 2, 24, 33.66, -3.4, 0.0002, 25, 8.2, -7.51, 0.9998, 2, 25, 23.19, -7.71, 0.69746, 26, -0.88, -8.03, 0.30254, 1, 26, 12.46, -6.22, 1, 2, 26, 26.24, -7.81, 0.5319, 27, 1.1, -7.99, 0.4681, 1, 27, 15.21, -9.65, 1, 1, 27, 32.32, -11.68, 1, 1, 27, 37.89, -3.89, 1, 1, 27, 35.93, 9.24, 1, 1, 27, 25.37, 16.79, 1, 1, 27, 13.16, 16.03, 1, 2, 26, 33.18, 10.33, 0.4, 27, 0.69, 11.43, 0.6, 2, 26, 18.57, 8.2, 0.99078, 27, -12.05, 3.95, 0.00922, 2, 25, 31.27, 5.89, 0.37, 26, 4.43, 6.87, 0.63, 1, 25, 17.8, 4.45, 1, 2, 24, 24.42, 6.14, 0.23914, 25, 3.78, 5.02, 0.76086, 1, 24, 12.43, 3.32, 1, 2, 23, 22.28, 4.82, 0.68091, 24, -0.36, 5.04, 0.31909, 1, 23, 12.1, 5.56, 1, 1, 1, -26.8, -4.14, 1, 1, 1, -19.93, -4.32, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48], "width": 109, "height": 100}}, "9": {"9": {"type": "mesh", "uvs": [0.00183, 0.06717, 0.03696, 0.18276, 0.10371, 0.27477, 0.21018, 0.38451, 0.32786, 0.46536, 0.45676, 0.5597, 0.57164, 0.63863, 0.67531, 0.75029, 0.71174, 0.87158, 0.76894, 1, 1, 1, 1, 0.89852, 0.94312, 0.76778, 0.84903, 0.66173, 0.72575, 0.57125, 0.56043, 0.47114, 0.45676, 0.41531, 0.33067, 0.34793, 0.20458, 0.24397, 0.15694, 0.15349, 0.08689, 0.02381, 0.0198, 0.02381, 0.0753, 0.12674, 0.11326, 0.19919, 0.18918, 0.28323, 0.25034, 0.34263, 0.33891, 0.3919, 0.44014, 0.46725, 0.54348, 0.5339, 0.66158, 0.60345, 0.72062, 0.68459, 0.79865, 0.76863, 0.84501, 0.86838], "triangles": [20, 0, 21, 22, 0, 20, 32, 31, 12, 8, 7, 31, 8, 31, 32, 32, 12, 11, 9, 8, 32, 10, 32, 11, 9, 32, 10, 30, 29, 14, 30, 14, 13, 7, 29, 30, 6, 29, 7, 31, 30, 13, 31, 13, 12, 7, 30, 31, 28, 27, 15, 5, 27, 28, 29, 28, 15, 14, 29, 15, 6, 28, 29, 5, 28, 6, 16, 26, 17, 25, 26, 3, 26, 25, 17, 27, 4, 26, 4, 3, 26, 16, 27, 26, 27, 16, 15, 4, 27, 5, 23, 19, 18, 2, 1, 23, 2, 23, 18, 24, 2, 18, 17, 25, 18, 24, 18, 25, 3, 24, 25, 2, 24, 3, 22, 20, 19, 1, 0, 22, 23, 22, 19, 1, 22, 23], "vertices": [2, 28, 1.31, 1.58, 0.2, 1, -22.63, 14.13, 0.8, 2, 28, 17.52, -4.62, 0.81, 29, -7.1, -4.15, 0.19, 1, 29, 7.78, -6.81, 1, 2, 29, 27.14, -7.8, 0.26319, 30, 2.1, -8.28, 0.73681, 2, 30, 18.91, -8.68, 0.98307, 31, -9.82, -8.91, 0.01693, 2, 30, 37.9, -9.73, 0.03018, 31, 9.19, -9.51, 0.96982, 2, 31, 25.6, -9.51, 0.51233, 32, 0.98, -9.55, 0.48767, 2, 32, 20.45, -9.23, 0.90977, 33, -4.89, -12.04, 0.09023, 2, 32, 37.56, -15.44, 0.02112, 33, 13.32, -11.7, 0.97888, 1, 33, 32.94, -9.51, 1, 1, 33, 37.24, 13.43, 1, 2, 32, 56.19, 7.28, 0.0004, 33, 22.57, 16.18, 0.9996, 2, 32, 36.81, 12.46, 0.20946, 33, 2.63, 14.08, 0.79054, 2, 32, 18.56, 12.53, 0.99409, 33, -14.45, 7.61, 0.00591, 3, 30, 58.73, 7.79, 9e-05, 31, 29.6, 8.5, 0.32411, 32, 0.7, 8.9, 0.6758, 3, 30, 36.49, 6.92, 0.032, 31, 7.39, 7.1, 0.96773, 32, -20.58, 2.39, 0.00027, 3, 30, 23.25, 5.63, 0.91461, 31, -5.81, 5.49, 0.08538, 32, -33.06, -2.22, 0, 1, 30, 7.2, 4.01, 1, 1, 29, 10.22, 4.07, 1, 2, 28, 20.45, 7.9, 0.85778, 29, -3.32, 8.15, 0.14222, 2, 28, 0.59, 12.26, 0.2, 1, -31.38, 7.99, 0.8, 2, 28, -3.08, 6.56, 0.2, 1, -24.61, 7.81, 0.8, 1, 28, 12.68, 3.09, 1, 2, 28, 23.71, 0.56, 0.5, 29, -0.56, 0.6, 0.5, 1, 29, 13.93, -0.62, 1, 2, 29, 24.62, -0.88, 0.5, 30, 0.79, -1.02, 0.5, 1, 30, 12.26, -0.09, 1, 2, 30, 27.31, -1.05, 0.69949, 31, -1.6, -1.09, 0.30051, 1, 31, 12.71, -0.64, 1, 2, 31, 28.37, 0.57, 0.5, 32, 1.34, 0.9, 0.5, 1, 32, 14.62, -0.27, 1, 2, 32, 29.27, -0.03, 0.5, 33, 0.06, -0.29, 0.5, 1, 33, 15.33, 1.61, 1], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42], "width": 101, "height": 147}}, "10": {"10": {"type": "mesh", "uvs": [0.02917, 0.42772, 0.02917, 0.47511, 0.05807, 0.50923, 0.03987, 0.59263, 0, 0.69807, 0, 0.79284, 0.02587, 0.88312, 0.10606, 0.96406, 0.22081, 0.99131, 0.35028, 1, 0.53738, 1, 0.69842, 0.97562, 0.82125, 0.89724, 0.90291, 0.79834, 0.94202, 0.65466, 1, 0.52202, 0.98934, 0.43556, 0.87356, 0.28746, 0.78963, 0.13724, 0.6945, 0, 0.63411, 0, 0.61988, 0.09981, 0.58001, 0.18908, 0.54032, 0.29835, 0.47868, 0.40791, 0.42427, 0.49027, 0.35604, 0.56799, 0.25368, 0.62864, 0.20137, 0.59642, 0.17635, 0.5206, 0.16497, 0.45426, 0.10129, 0.38981, 0.02917, 0.38792, 0.07869, 0.43697, 0.11099, 0.48797, 0.12289, 0.56305, 0.14329, 0.64238, 0.18579, 0.75572, 0.29459, 0.82088, 0.47309, 0.82372, 0.40509, 0.65938, 0.67539, 0.76563, 0.53599, 0.58997, 0.61929, 0.45255, 0.82499, 0.56588, 0.83824, 0.39518, 0.66179, 0.3123, 0.70089, 0.16922], "triangles": [21, 20, 19, 47, 21, 19, 18, 47, 19, 22, 21, 47, 46, 22, 47, 23, 22, 46, 17, 47, 18, 17, 46, 47, 45, 46, 17, 43, 23, 46, 45, 17, 16, 43, 46, 45, 24, 23, 43, 44, 43, 45, 44, 45, 16, 44, 16, 15, 42, 24, 43, 25, 24, 42, 14, 44, 15, 44, 42, 43, 26, 25, 42, 13, 41, 44, 41, 42, 44, 14, 13, 44, 39, 42, 41, 12, 41, 13, 11, 41, 12, 11, 10, 41, 40, 26, 42, 27, 26, 40, 38, 27, 40, 39, 40, 42, 38, 40, 39, 10, 39, 41, 38, 8, 37, 9, 38, 39, 9, 39, 10, 8, 38, 9, 35, 29, 28, 36, 35, 28, 3, 35, 36, 4, 3, 36, 27, 37, 36, 27, 36, 28, 4, 36, 37, 5, 4, 37, 37, 27, 38, 6, 5, 37, 7, 6, 37, 8, 7, 37, 31, 33, 32, 33, 31, 30, 34, 33, 30, 2, 1, 33, 34, 2, 33, 34, 30, 29, 35, 34, 29, 2, 34, 35, 3, 2, 35, 33, 0, 32, 1, 0, 33], "vertices": [5, 1, -5.67, 5.4, 0.83, 41, 1.99, -0.33, 0.17, 43, -30.07, 44.92, 0, 44, -10.04, 76.84, 0, 45, 18.24, 86.25, 0, 2, 1, -5.49, 12.22, 0.83, 41, 8.11, -3.36, 0.17, 1, 41, 14.05, -2.44, 1, 2, 41, 23.84, -9.73, 0.39042, 42, -2.89, -9.4, 0.60958, 3, 41, 35.32, -20.75, 0.02831, 42, 6.8, -22.03, 0.96431, 43, -32.12, 5.89, 0.00738, 2, 42, 17.97, -29.87, 0.87184, 43, -31.62, -7.75, 0.12816, 2, 42, 30.39, -34.8, 0.71255, 43, -28.03, -20.62, 0.28745, 2, 42, 45.46, -33.62, 0.54341, 43, -17.99, -31.91, 0.45659, 2, 42, 56.58, -24.6, 0.32149, 43, -4.08, -35.32, 0.67851, 3, 42, 66.53, -12.6, 0.08774, 43, 11.49, -36, 0.86851, 44, -41.06, -8.68, 0.04376, 3, 42, 79.42, 5.78, 0.00101, 43, 33.93, -35.17, 0.68927, 44, -25.19, -24.56, 0.30972, 2, 43, 53.11, -30.95, 0.36009, 44, -9.04, -35.74, 0.63991, 3, 43, 67.42, -19.12, 0.12295, 44, 9.36, -38.18, 0.86939, 45, -43.09, -12.97, 0.00766, 3, 43, 76.69, -4.53, 0.0204, 44, 26.36, -35.04, 0.88628, 45, -28.25, -21.83, 0.09331, 2, 44, 44.31, -23.73, 0.49881, 45, -7.3, -25.18, 0.50119, 3, 44, 62.74, -15.14, 0.10793, 45, 12.21, -30.89, 0.89106, 46, -29.99, -23.93, 0.00101, 3, 44, 70.63, -5.44, 8e-05, 45, 24.55, -28.81, 0.90959, 46, -17.53, -25.12, 0.09033, 2, 45, 44.94, -13.56, 0.31386, 46, 6.11, -15.7, 0.68614, 1, 46, 29.3, -10.09, 1, 1, 46, 50.93, -2.79, 1, 2, 45, 84.39, 17.78, 0, 46, 52.35, 4.31, 1, 1, 46, 38.6, 8.82, 1, 2, 45, 56.8, 22.5, 0.01035, 46, 26.94, 16.04, 0.98965, 5, 42, -3.08, 64.1, 0.00051, 43, 30.54, 65.81, 0.007, 44, 46.51, 46.64, 0.00617, 45, 40.79, 26.24, 0.33974, 46, 12.45, 23.81, 0.64659, 5, 42, 5.58, 48.99, 0.0153, 43, 23.73, 49.77, 0.07152, 44, 30.12, 40.71, 0.11111, 45, 24.57, 32.6, 0.6144, 46, -1.56, 34.17, 0.18767, 5, 42, 11.54, 36.83, 0.0766, 43, 17.65, 37.68, 0.23817, 44, 17.12, 36.94, 0.23917, 45, 12.31, 38.35, 0.41071, 46, -11.9, 42.91, 0.03535, 5, 42, 16, 23.7, 0.27389, 43, 9.88, 26.19, 0.43748, 44, 3.41, 34.82, 0.15268, 45, 0.62, 45.8, 0.13457, 46, -21.26, 53.14, 0.00138, 5, 41, 39.88, 10.96, 0.00119, 42, 16.1, 8.63, 0.83912, 43, -2.07, 17.01, 0.1403, 44, -11.45, 37.33, 0.00905, 45, -8.89, 57.49, 0.01034, 5, 41, 32.93, 7.4, 0.10086, 42, 8.69, 6.15, 0.88775, 43, -8.52, 21.41, 0.01021, 44, -12.61, 45.05, 0.0004, 45, -4.67, 64.06, 0.00079, 2, 41, 21.82, 9.55, 0.79362, 42, -1.97, 9.97, 0.20638, 4, 41, 12.65, 12.57, 0.99999, 43, -13.64, 41.71, 1e-05, 44, -1.22, 62.61, 0, 45, 15.48, 69.74, 0, 5, 1, -14.46, 0.17, 0.4, 41, 0.94, 9.84, 0.6, 43, -21.62, 50.7, 0, 44, -0.06, 74.58, 0, 45, 24.25, 77.96, 0, 5, 1, -5.82, -0.33, 0.83, 41, -3.14, 2.21, 0.17, 43, -30.28, 50.65, 0, 44, -5.99, 80.89, 0, 45, 23.96, 86.62, 0, 4, 41, 5.82, 4.4, 1, 43, -24.08, 43.81, 0, 44, -6.78, 71.69, 0, 45, 17.3, 80.23, 0, 2, 41, 14.12, 4.61, 1, 44, -9.23, 63.76, 0, 2, 41, 24.45, 1.09, 0.5, 42, -0.65, 1.2, 0.5, 1, 42, 10.11, -3.35, 1, 2, 42, 26.4, -8.55, 0.79427, 43, -9.54, -1.58, 0.20573, 2, 42, 41.58, -3.25, 0.38281, 43, 3.86, -10.47, 0.61719, 3, 42, 54.21, 14.05, 0.00177, 43, 25.28, -10.09, 0.89175, 44, -12.69, -1.15, 0.10648, 4, 42, 30.15, 20.96, 0.11685, 43, 16.25, 13.26, 0.68162, 44, -1.73, 21.35, 0.14836, 45, -12.14, 39.08, 0.05317, 3, 43, 49.23, -0.83, 0.07338, 44, 10.39, -12.41, 0.91896, 45, -25.31, 5.72, 0.00767, 5, 42, 30.99, 39.56, 0.02131, 43, 31.58, 23.83, 0.15837, 44, 16.44, 17.31, 0.57714, 45, -1.15, 24.05, 0.2417, 46, -28.62, 32.59, 0.00148, 5, 42, 20.54, 59.11, 0.00356, 43, 40.83, 43.97, 0.02474, 44, 37.5, 24.23, 0.09897, 45, 19.24, 15.35, 0.82759, 46, -11.19, 18.89, 0.04514, 2, 44, 43.42, -4.76, 0.15337, 45, 4.55, -10.34, 0.84663, 2, 45, 29.19, -10.33, 0.76748, 46, -8.26, -8.49, 0.23252, 3, 43, 45.18, 64.34, 0.00045, 45, 39.73, 11.57, 0.20931, 46, 7.61, 9.92, 0.79024, 1, 46, 26.89, 1.26, 1], "hull": 33, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 0, 64], "width": 120, "height": 144}}, "12": {"11": {"x": 83.18, "y": -0.65, "rotation": 152.12, "width": 312, "height": 160}}, "13": {"3": {"x": -4.65, "y": -1.89, "rotation": 180, "width": 33, "height": 8}}}}, "animations": {"show": {"slots": {"13": {"attachment": [{"time": 0, "name": null}, {"time": 2, "name": "3"}, {"time": 2.0333, "name": "3"}, {"time": 2.1, "name": "3"}, {"time": 2.1333, "name": null}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 10}, {"time": 0.6333, "x": 0, "y": 12.22}, {"time": 1.3333, "x": 0, "y": 10}, {"time": 1.9667, "x": 0, "y": 12.22}, {"time": 2.6667, "x": 0, "y": 10}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 0.6}, {"time": 1.3333, "angle": 0}, {"time": 2, "angle": 0.6}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.6333, "x": 1.006, "y": 1}, {"time": 1.3333, "x": 1, "y": 1}, {"time": 1.9667, "x": 1.006, "y": 1}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone3": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -1.08}, {"time": 1.3333, "angle": 0}, {"time": 2, "angle": -1.08}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone4": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 1.42}, {"time": 1.2821, "angle": 0.13}, {"time": 1.3333, "angle": 0}, {"time": 2, "angle": 1.42}, {"time": 2.6154, "angle": 0.13}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.2821, "x": 0, "y": 0.01}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 2.6154, "x": 0, "y": 0.01}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.6333, "x": 1.008, "y": 1}, {"time": 1.3333, "x": 1, "y": 1}, {"time": 1.9667, "x": 1.008, "y": 1}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone6": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -0.31}, {"time": 1.3333, "angle": 0}, {"time": 2, "angle": -0.31}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone7": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 0.46}, {"time": 1.2821, "angle": 0.05}, {"time": 1.3333, "angle": 0}, {"time": 2, "angle": 0.46}, {"time": 2.6154, "angle": 0.05}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone8": {"rotate": [{"time": 0, "angle": 0.49}, {"time": 0.6667, "angle": -1.95}, {"time": 1.3333, "angle": 0.49}, {"time": 2, "angle": -1.95}, {"time": 2.6667, "angle": 0.49}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone9": {"rotate": [{"time": 0, "angle": -0.55}, {"time": 0.3333, "angle": 2.62, "curve": [0.312, 0.12, 0.641, 0.88]}, {"time": 0.6667, "angle": 2.27}, {"time": 1, "angle": -1.28, "curve": [0.312, 0.12, 0.641, 0.88]}, {"time": 1.3333, "angle": -0.55}, {"time": 1.6667, "angle": 2.62, "curve": [0.312, 0.12, 0.641, 0.88]}, {"time": 2, "angle": 2.27}, {"time": 2.3333, "angle": -1.28, "curve": [0.312, 0.12, 0.641, 0.88]}, {"time": 2.6667, "angle": -0.55}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1.012}, {"time": 1.3333, "x": 1, "y": 1}, {"time": 2, "x": 1, "y": 1.012}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone10": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.2821, "angle": -0.01}, {"time": 1.3333, "angle": 0}, {"time": 2.6154, "angle": -0.01}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone11": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": -4.49}, {"time": 0.6667, "angle": -1.58}, {"time": 1, "angle": 3.05}, {"time": 1.2821, "angle": 0.51}, {"time": 1.3333, "angle": 0}, {"time": 1.6667, "angle": -4.49}, {"time": 2, "angle": -1.58}, {"time": 2.3333, "angle": 3.05}, {"time": 2.6154, "angle": 0.51}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone12": {"rotate": [{"time": 0, "angle": 0, "curve": [0.715, 0.48, 0.352, 0.48]}, {"time": 0.6667, "angle": -0.84, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 0, "curve": [0.715, 0.48, 0.352, 0.48]}, {"time": 2, "angle": -0.84, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone13": {"rotate": [{"time": 0, "angle": -2.91, "curve": [0.217, 0.28, 0.75, 1]}, {"time": 0.3333, "angle": -2.68, "curve": [0.278, 0.08, 0.821, 0.79]}, {"time": 0.6667, "angle": -2.63, "curve": [0.15, 0.19, 0.717, 0.96]}, {"time": 1, "angle": -2.27, "curve": [0.278, 0.08, 0.845, 0.8]}, {"time": 1.3333, "angle": -2.91, "curve": [0.217, 0.28, 0.75, 1]}, {"time": 1.6667, "angle": -2.68, "curve": [0.278, 0.08, 0.821, 0.79]}, {"time": 2, "angle": -2.63, "curve": [0.15, 0.19, 0.717, 0.96]}, {"time": 2.3333, "angle": -2.27, "curve": [0.278, 0.08, 0.845, 0.8]}, {"time": 2.6667, "angle": -2.91}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.2821, "x": 0, "y": 0.01}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 2.6154, "x": 0, "y": 0.01}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone14": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 0.42}, {"time": 1.3333, "angle": 0}, {"time": 2, "angle": 0.42}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone15": {"rotate": [{"time": 0, "angle": -2.17}, {"time": 0.3333, "angle": -5.86}, {"time": 0.6667, "angle": -3.66}, {"time": 1, "angle": 0.97}, {"time": 1.2821, "angle": -1.66}, {"time": 1.3333, "angle": -2.17}, {"time": 1.6667, "angle": -5.86}, {"time": 2, "angle": -3.66}, {"time": 2.3333, "angle": 0.97}, {"time": 2.6154, "angle": -1.66}, {"time": 2.6667, "angle": -2.17}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone16": {"rotate": [{"time": 0, "angle": 1.24}, {"time": 0.3333, "angle": 1.13}, {"time": 0.6667, "angle": -2.14}, {"time": 1, "angle": -3.59}, {"time": 1.2821, "angle": 0.47}, {"time": 1.3333, "angle": 1.24}, {"time": 1.6667, "angle": 1.13}, {"time": 2, "angle": -2.14}, {"time": 2.3333, "angle": -3.59}, {"time": 2.6154, "angle": 0.47}, {"time": 2.6667, "angle": 1.24}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.2821, "x": 0, "y": -0.02}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 2.6154, "x": 0, "y": -0.02}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone17": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": 6.3}, {"time": 0.6667, "angle": 5.18}, {"time": 1, "angle": -0.56}, {"time": 1.3333, "angle": 0}, {"time": 1.6667, "angle": 6.3}, {"time": 2, "angle": 5.18}, {"time": 2.3333, "angle": -0.56}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.2821, "x": 0, "y": 0.01}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 2.6154, "x": 0, "y": 0.01}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone18": {"rotate": [{"time": 0, "angle": -7.49}, {"time": 0.3333, "angle": 1.53}, {"time": 0.6667, "angle": -6.42}, {"time": 1, "angle": -13.8}, {"time": 1.2821, "angle": -8.47}, {"time": 1.3333, "angle": -7.49}, {"time": 1.6667, "angle": 1.53}, {"time": 2, "angle": -6.42}, {"time": 2.3333, "angle": -13.8}, {"time": 2.6154, "angle": -8.47}, {"time": 2.6667, "angle": -7.49}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone19": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -13.85}, {"time": 1.2821, "angle": -1.09}, {"time": 1.3333, "angle": 0}, {"time": 2, "angle": -13.85}, {"time": 2.6154, "angle": -1.09}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone20": {"rotate": [{"time": 0, "angle": -5.37, "curve": [0.25, 0.31, 0.75, 1]}, {"time": 0.3333, "angle": 10.23, "curve": [0.267, 0.16, 0.622, 0.59]}, {"time": 0.6667, "angle": 5.82, "curve": [0.332, 0.48, 0.684, 0.93]}, {"time": 1, "angle": -11.86, "curve": [0.234, 0, 0.752, 0.67]}, {"time": 1.2821, "angle": -6.68, "curve": [0.429, 0.43, 0.772, 0.77]}, {"time": 1.3333, "angle": -5.37, "curve": [0.25, 0.31, 0.75, 1]}, {"time": 1.6667, "angle": 10.23, "curve": [0.267, 0.16, 0.622, 0.59]}, {"time": 2, "angle": 5.82, "curve": [0.332, 0.48, 0.684, 0.93]}, {"time": 2.3333, "angle": -11.86, "curve": [0.234, 0, 0.752, 0.67]}, {"time": 2.6154, "angle": -6.68, "curve": [0.429, 0.43, 0.772, 0.77]}, {"time": 2.6667, "angle": -5.37}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone21": {"rotate": [{"time": 0, "angle": -0.53}, {"time": 0.2667, "angle": 1.24}, {"time": 0.4333, "angle": 8.02, "curve": [0.25, 0, 0.807, 0.76]}, {"time": 0.6667, "angle": 4.28, "curve": [0.226, 0.28, 0.75, 1]}, {"time": 1, "angle": 1.14, "curve": [0.234, 0, 0.752, 0.67]}, {"time": 1.2821, "angle": -0.19, "curve": [0.429, 0.42, 0.772, 0.77]}, {"time": 1.3333, "angle": -0.53}, {"time": 1.6, "angle": 1.24}, {"time": 1.7667, "angle": 8.02, "curve": [0.25, 0, 0.807, 0.76]}, {"time": 2, "angle": 4.28, "curve": [0.226, 0.28, 0.75, 1]}, {"time": 2.3333, "angle": 1.14, "curve": [0.234, 0, 0.752, 0.67]}, {"time": 2.6154, "angle": -0.19, "curve": [0.429, 0.42, 0.772, 0.77]}, {"time": 2.6667, "angle": -0.53}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.2821, "x": 0, "y": 0.01}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 2.6154, "x": 0, "y": 0.01}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone22": {"rotate": [{"time": 0, "angle": 8.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 26.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 8.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 26.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "angle": 8.89}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone23": {"rotate": [{"time": 0, "angle": 3.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -5.94, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 3.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -5.94, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "angle": 3.24}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone24": {"rotate": [{"time": 0, "angle": 0, "curve": [0.117, 0.14, 0.75, 1]}, {"time": 0.3333, "angle": 8.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -8.27, "curve": [0.25, 0, 0.911, 0.89]}, {"time": 1.3333, "angle": 0, "curve": [0.117, 0.14, 0.75, 1]}, {"time": 1.6667, "angle": 8.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "angle": -8.27, "curve": [0.25, 0, 0.911, 0.89]}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone25": {"rotate": [{"time": 0, "angle": -16.48, "curve": [0.122, 0.15, 0.75, 1]}, {"time": 0.3333, "angle": -12.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -14.49, "curve": [0.25, 0, 0.911, 0.88]}, {"time": 1.3333, "angle": -16.48, "curve": [0.122, 0.15, 0.75, 1]}, {"time": 1.6667, "angle": -12.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "angle": -14.49, "curve": [0.25, 0, 0.911, 0.88]}, {"time": 2.6667, "angle": -16.48}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone26": {"rotate": [{"time": 0, "angle": 0.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 0.47, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.6667, "angle": 21.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 0.19, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1.6667, "angle": 0.34, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2, "angle": 21.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "angle": 0.19}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone27": {"rotate": [{"time": 0, "angle": 12.88, "curve": [0.289, 0, 0.628, 0.38]}, {"time": 0.2667, "angle": 12.33, "curve": [0.108, 0.12, 0.75, 1]}, {"time": 0.6667, "angle": 25.41, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.9333, "angle": 31.5, "curve": [0.121, 0.12, 0.54, 0.58]}, {"time": 1.3333, "angle": 12.88, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1.6, "angle": 12.33, "curve": [0.117, 0.15, 0.75, 1]}, {"time": 2, "angle": 27.7, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2.2667, "angle": 31.5, "curve": [0.108, 0.13, 0.75, 1]}, {"time": 2.6667, "angle": 12.88}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone28": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 7.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 7.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone29": {"rotate": [{"time": 0, "angle": 1.85, "curve": [0.131, 0.16, 0.75, 1]}, {"time": 0.3333, "angle": -3.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 9.79, "curve": [0.25, 0, 0.802, 0.76]}, {"time": 1.3333, "angle": 1.85, "curve": [0.131, 0.16, 0.75, 1]}, {"time": 1.6667, "angle": -3.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "angle": 9.79, "curve": [0.25, 0, 0.802, 0.76]}, {"time": 2.6667, "angle": 1.85}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone30": {"rotate": [{"time": 0, "angle": -6.11}, {"time": 0.5, "angle": -23.27, "curve": [0.25, 0, 0.753, 0.68]}, {"time": 1.0667, "angle": -12.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1.3333, "angle": -6.11}, {"time": 1.8333, "angle": -23.27, "curve": [0.25, 0, 0.753, 0.68]}, {"time": 2.4, "angle": -12.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2.6667, "angle": -6.11}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone31": {"rotate": [{"time": 0, "angle": -7.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -12.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -7.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "angle": -7.12}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone32": {"rotate": [{"time": 0, "angle": 8.83, "curve": [0.155, 0.19, 0.75, 1]}, {"time": 0.3333, "angle": 10.91, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.6667, "angle": 3.69, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1, "angle": 1.56, "curve": [0.25, 0, 0.888, 0.87]}, {"time": 1.3333, "angle": 8.83, "curve": [0.155, 0.19, 0.75, 1]}, {"time": 1.6667, "angle": 10.91, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2, "angle": 3.69, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2.3333, "angle": 1.56, "curve": [0.25, 0, 0.888, 0.87]}, {"time": 2.6667, "angle": 8.83}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone33": {"rotate": [{"time": 0, "angle": 12.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 4.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 12.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 4.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "angle": 12.07}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone34": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.2821, "angle": -0.03}, {"time": 1.3333, "angle": 0}, {"time": 2.6154, "angle": -0.03}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone35": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.2821, "angle": 0.03}, {"time": 1.3333, "angle": 0}, {"time": 2.6154, "angle": 0.03}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.2821, "x": 0, "y": 0.01}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 2.6154, "x": 0, "y": 0.01}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone36": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone37": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -11.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -11.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6154, "angle": 0.03}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": -0.41, "y": -0.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": -0.41, "y": -0.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone38": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 1.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 4.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 1.38, "y": -1.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1.38, "y": -1.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone39": {"rotate": [{"time": 0, "angle": 0}, {"time": 2.6154, "angle": -360.05}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.0667, "x": 0, "y": 0}, {"time": 2.1, "x": 0.95, "y": -0.23}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.0667, "x": 1, "y": 1}, {"time": 2.1, "x": 1, "y": 0.595}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone40": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone41": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -4.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -4.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 3.32, "y": -0.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 3.32, "y": -0.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.963, "y": 0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0.963, "y": 0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone42": {"rotate": [{"time": 0, "angle": 0, "curve": [0.079, 0.1, 0.75, 1]}, {"time": 0.3333, "angle": 8.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -5.04, "curve": [0.25, 0, 0.906, 0.89]}, {"time": 1.3333, "angle": 0, "curve": [0.079, 0.1, 0.75, 1]}, {"time": 1.6667, "angle": 8.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "angle": -5.04, "curve": [0.25, 0, 0.906, 0.89]}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone43": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 8.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 8.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.6667, "x": 0.972, "y": 1, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2, "x": 0.972, "y": 1, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone44": {"rotate": [{"time": 0, "angle": 11.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -11.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 11.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -11.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "angle": 11.09}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone45": {"rotate": [{"time": 0, "angle": 0, "curve": [0.103, 0.14, 0.75, 1]}, {"time": 0.3333, "angle": -11.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 17.18, "curve": [0.25, 0, 0.911, 0.88]}, {"time": 1.3333, "angle": 0, "curve": [0.103, 0.14, 0.75, 1]}, {"time": 1.6667, "angle": -11.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "angle": 17.18, "curve": [0.25, 0, 0.911, 0.88]}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone46": {"rotate": [{"time": 0, "angle": -8.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -22.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -8.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -22.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "angle": -8.56}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.693, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 1.014, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0.693, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1.014, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 0.693, "y": 1}]}, "bone48": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -14.25}, {"time": 1.3333, "angle": -0.18}, {"time": 2, "angle": -14.25}, {"time": 2.6667, "angle": -0.18}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.92, "y": -2.92}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 2, "x": 2.92, "y": -2.92}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "bone47": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -4.96}, {"time": 1.3333, "angle": -0.18}, {"time": 2, "angle": -4.96}, {"time": 2.6667, "angle": -0.18}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 3.81, "y": -2.07}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 2, "x": 3.81, "y": -2.07}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}}}}}